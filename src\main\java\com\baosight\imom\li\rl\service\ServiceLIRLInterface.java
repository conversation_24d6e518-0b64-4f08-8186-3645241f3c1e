package com.baosight.imom.li.rl.service;

import com.alibaba.fastjson.JSON;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imc.common.utils.ImcGlobalUtils;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.*;
import com.baosight.imom.li.ds.domain.LIDS0601;
import com.baosight.imom.li.rl.dao.*;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import com.baosight.xservices.em.util.SmsSendManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.printing.PDFPrintable;
import org.apache.pdfbox.printing.Scaling;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.imageio.ImageIO;
import javax.print.*;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.standard.MediaSizeName;
import java.awt.image.BufferedImage;
import java.awt.print.*;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.baosight.imom.li.rl.service.ServiceLIRLInterfacePda.STATUS_UPDATE_TOKEN;

/***
 * 一体机对外接口服务
 */
public class ServiceLIRLInterface extends ServiceBase {

    private static final Logger log = LoggerFactory.getLogger(ServiceLIRLInterface.class);
    private static final  String PRIORITY_LEVEL="99";
    private static final int PRINT_PORT = 9100; // 打印机默认端口
    private static final int TIMEOUT = 5000;   // 连接超时时间
    /**
     * 附件上传默认路径
     */
    private static final String ROOT_PATH = PlatApplicationContext.getProperty("docRootDir");
    private static final String DOWNLOAD_PATH = PlatApplicationContext.getProperty("docDownloadPath");


    /**
     * restTemplate
     */
    @Autowired
    private RestTemplate restTemplate;

    /***
     * 排队叫号---定时任务
     * 存储过程：PROG.WL_VEHICLE_QUENE_MGR.PROC_VEHICLE_QUENE_CALL
     * @param eiInfo
     * @return
     */
    public EiInfo vehicleQueueCall(EiInfo eiInfo) {
        //开始时间
        String startTime = DateUtil.curDateTimeStr14();
        //----先处处理超时数据
        System.out.println("-------------------------------------------开始处理超时数据--------------------------------------------------");
        System.out.println("开始时间："+startTime+"-----------------");
        try {
            //获取账套信息
            Map<String, String> mapQueue = new HashMap<>();
            mapQueue.put("processSwitchName", "WL_VEHICLE_ORDER_QUENE_SWITCH");
            mapQueue.put("processSwitchValue", "1");
            List<HashMap> list = dao.query(LIRL0100.QUERY_SWITCH, mapQueue);
            for (HashMap hashMap : list) {

                int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();//当前天是星期几
                switch (dayOfWeek) {
                    case 1:
                        hashMap.put("weekDays", "一");
                        break;
                    case 2:
                        hashMap.put("weekDays", "二");
                        break;
                    case 3:
                        hashMap.put("weekDays", "三");
                        break;
                    case 4:
                        hashMap.put("weekDays", "四");
                        break;
                    case 5:
                        hashMap.put("weekDays", "五");
                        break;
                    case 6:
                        hashMap.put("weekDays", "六");
                        break;
                    case 7:
                        hashMap.put("weekDays", "日");
                        break;
                }
                // 当天的23:59:59  停止自动叫号
                // 按当天叫号时段的最早时间+10分钟，启动叫号
                //查询当天最早预约时间+10
                List<String> queryList = this.dao.query(LIRL0105.QUERY_RESERVESION, hashMap);
                if (CollectionUtils.isNotEmpty(queryList)){
                    String[] split = queryList.get(0).split("-");
                    //获取小时和分钟
                    String[] split1 = split[0].split(":");
                    // 获取当前时间
                    LocalTime now = LocalTime.now();

                    // 当天的 23:59:59
                    LocalTime endOfDay = LocalTime.of(23, 59, 00);

                    // 最早时间是 07:30
                    LocalTime earliestTime = LocalTime.of(Integer.parseInt(split1[0]), Integer.parseInt(split1[1]));

                    // 启动叫号时间 = 最早时间 + 10 分钟
                    LocalTime startCallingTime = earliestTime.plusMinutes(10);

                    //第一次
                    int count=1;
                    // 判断是否在叫号时段内
                    if (now.isAfter(startCallingTime) && now.isBefore(endOfDay)) {
                        //启动叫号
                        queueCallM(eiInfo, hashMap);
                    } else {
                        System.out.println("停止叫号");
                        log.info("---------------------------------------------已过叫号时间停止叫号中----------------------------------------");
                    }

                }

            }
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }

        return eiInfo;

    }



    /***
     * 排队叫号
     * @param eiInfo
     * @param hashMap 排队叫号账套
     */
    public void queueCallM(EiInfo eiInfo, HashMap hashMap) {
        //开始时间
        long startTime = System.currentTimeMillis();
        //----先处处理超时数据
        System.out.println("-------------------------------------------开始处理超时数据--------------------------------------------------");
        // System.out.println("开始时间："+startTime+"-----------------");
        Map<Object, Object> hashMapSegNo = new HashMap<>();
        String segNo = MapUtils.getString(hashMap, "segNo", "");
        hashMapSegNo.put("segNo", segNo);
        //处理超时数据：
        // 检查叫号表数据，已叫号五分钟，但是还未进厂：
        //排队叫号是否开启处理超时
        String wlVehicleOrderQueneOvertime = new SwitchUtils().getProcessSwitchValue(segNo, "WL_VEHICLE_ORDER_QUENE_OVERTIME", dao);
        if ("1".equals(wlVehicleOrderQueneOvertime)) {
            List<LIRL0402> queryLIRL0402 = this.dao.query(LIRL0402.QUERY_CALL_M, hashMap);
            if (queryLIRL0402.size() > 0) {
                for (LIRL0402 lirl0402 : queryLIRL0402) {
                    Map map = lirl0402.toMap();
                    String targetHandPointId = lirl0402.getTargetHandPointId();
                    String carTraceNo = lirl0402.getCarTraceNo();
                    String vehicleNo = lirl0402.getVehicleNo();
                    hashMap.put("handPointId", targetHandPointId);
                    hashMap.put("carTraceNo", carTraceNo);
                    hashMap.put("vehicleNo", vehicleNo);
                    RecordUtils.setCreator(hashMap);
                    // --判断排队表中是否有此装卸点的车辆
                    int count = super.count(LIRL0402.QUERY_CALL_AND_HAND_POINT_IS_EXIST, hashMap);
                    // 如果后续排队车辆中有此装卸点，则此数据认为是超时的，必须插入超时表；
                    // --如果后续车辆中没有用这个装卸点的，就算超过十五分钟也可以继续待在叫号里
                    if (count > 0) {
                        //插入超时表时，检查之前是否存在过，若已经存在超时表，则直接更改超时次数即可；若不存在则直接插入
                        int count1 = super.count(LIRL0403.QUERY_COUNT, hashMap);
                        if (count1 <= 0) {
                            String queueNumber = MapUtils.getString(map, "queueNumber", "");
                            //超时数据插入超时表
                            hashMap.put("uuid", UUIDUtils.getUUID());
                            hashMap.put("segNo", segNo);
                            hashMap.put("unitCode", segNo);
                            hashMap.put("queueNumber", queueNumber);
                            hashMap.put("carTraceNo", MapUtils.getString(map, "carTraceNo", ""));
                            hashMap.put("vehicleNo", MapUtils.getString(map, "vehicleNo", ""));
                            hashMap.put("voucherNum", MapUtils.getString(map, "voucherNum", ""));
                            hashMap.put("priorityLevel", PRIORITY_LEVEL);
                            hashMap.put("queueDate", DateUtil.curDateTimeStr14());
                            hashMap.put("targetHandPointId", targetHandPointId);
                            hashMap.put("factoryArea", MapUtils.getString(map, "factoryArea", ""));
                            hashMap.put("overtimeCount", 1);
                            RecordUtils.setCreatorBYCall(hashMap);
                            this.dao.insert(LIRL0403.INSERT, hashMap);
                        } else {
                            List<String> overtimeCount = this.dao.query(LIRL0403.QUERY_OVER_COUNT, hashMap);
                            //超时数据已存在,更新超时次数
                            hashMap.put("overtimeCount", Integer.parseInt(overtimeCount.get(0)) + 1);
                            RecordUtils.setRevisorByCall(hashMap);
                            this.dao.update(LIRL0403.UPDATE_OVERTIME_COUNT, hashMap);
                        }

                        //--插入或者更新超时表后，叫号表中此数据要删除
                        this.dao.delete(LIRL0402.DELETE, hashMap);
                        // 叫号表数据删除后，装卸点的进车跟踪要随之改变
                        updateHandPointJobNumber(segNo, targetHandPointId, 0, -1, 30);
                    }
                }

            }

            // --预排序、排序数据重排
            // --先要处理超时数据
            List<HashMap> queryLIRL0403 = this.dao.query(LIRL0403.QUERY_INFO, hashMap);
            if (queryLIRL0403.size() > 0) {
                for (HashMap map : queryLIRL0403) {
                    map.put("segNo", segNo);
                    Integer overtimeCount = MapUtils.getInteger(map, "overtimeCount", 0);
                    Integer queueNumber = MapUtils.getInteger(map, "queueNumber", 0);
                    //  超时<2次插入排序临时表,超时次数带过来
                    if (overtimeCount < 2) {
                        LIRL0310 lirl0310 = new LIRL0310();
                        lirl0310.setUuid(UUIDUtils.getUUID());
                        lirl0310.setSegNo(segNo);
                        lirl0310.setUnitCode(segNo);
                        //超市一次顺序后延一位
                        lirl0310.setQueueNumber(queueNumber+1);
                        lirl0310.setCarTraceNo(MapUtils.getString(map, "carTraceNo"));
                        lirl0310.setVehicleNo(MapUtils.getString(map, "vehicleNo"));
                        lirl0310.setVoucherNum(MapUtils.getString(map, "voucherNum"));
                        lirl0310.setPriorityLevel(PRIORITY_LEVEL);
                        lirl0310.setQueueDate(DateUtil.curDateTimeStr14());
                        lirl0310.setTargetHandPointId(MapUtils.getString(map, "targetHandPointId"));
                        lirl0310.setFactoryArea(MapUtils.getString(map, "factoryArea"));
                        lirl0310.setOvertimeCount(MapUtils.getString(map, "overtimeCount"));
                        Map lirl0310Map = lirl0310.toMap();
                        RecordUtils.setCreatorBYCall(lirl0310Map);
                        this.dao.insert(LIRL0310.INSERT, lirl0310Map);
                    } else {
                        // 超时次数>2  超时次数太多 做为惩罚要重新排序
                        //重新插入预排序表,超时次数为0,顺序号按照预排序数据向后排
                        LIRL0310 lirl0310 = new LIRL0310();
                        HashMap<Object, Object> hashMapLIRL0303 = new HashMap<>();
                        hashMapLIRL0303.put("segNo",segNo);
                        List<String> queryList = this.dao.query(LIRL0401.QUERY_QUEUE_NUMBER, hashMapLIRL0303);
                        if (queryList.size() > 0&&CollectionUtils.isNotEmpty(queryList)){
                            String queueNumber1 = queryList.get(0);
                            int i = Integer.parseInt(queueNumber1);
                            lirl0310.setQueueNumber(i+1);
                        }else {
                            lirl0310.setQueueNumber(0);
                        }

                        lirl0310.setUuid(UUIDUtils.getUUID());
                        lirl0310.setSegNo(segNo);
                        lirl0310.setUnitCode(segNo);

                        lirl0310.setCarTraceNo(MapUtils.getString(map, "carTraceNo"));
                        lirl0310.setVehicleNo(MapUtils.getString(map, "vehicleNo"));
                        lirl0310.setVoucherNum(MapUtils.getString(map, "voucherNum"));
                        lirl0310.setVoucherNum(MapUtils.getString(map, "voucherNum"));
                        lirl0310.setPriorityLevel(PRIORITY_LEVEL);
                        lirl0310.setQueueDate(DateUtil.curDateTimeStr14());
                        lirl0310.setTargetHandPointId(MapUtils.getString(map, "targetHandPointId"));
                        lirl0310.setFactoryArea(MapUtils.getString(map, "factoryArea"));
                        lirl0310.setOvertimeCount("0");
                        Map lirl0310Map = lirl0310.toMap();
                        RecordUtils.setCreatorBYCall(lirl0310Map);
                        lirl0310Map.put("handType", "1");//无固定目标TARGET_TYPE
                        this.dao.insert(LIRL0303.INSERT, lirl0310Map);
                    }
                }
            }
            log("-------------------------------------------结束处理超时数据--------------------------------------------------");
            System.out.println("-------------------------------------------结束处理超时数据--------------------------------------------------");
            log.info("-------------------------------------------处理正常数据开始--------------------------------------------------");
        }
        //处理预排序表中的数据
        //1.根据账套查询预排序表中的所有数据，把预排序中的数据插入排序临时表
        List<LIRL0310> listLIRL0310 = new ArrayList<>();
        List<LIRL0303> lirl0303List = this.dao.query(LIRL0303.QUERY, hashMapSegNo);
        if (CollectionUtils.isNotEmpty(lirl0303List)) {
            for (LIRL0303 lirl0303 : lirl0303List) {
                LIRL0310 lirl0310 = new LIRL0310();
                lirl0310.setUuid(UUIDUtils.getUUID());
                lirl0310.setSegNo(segNo);
                lirl0310.setUnitCode(segNo);
                lirl0310.setQueueNumber(lirl0303.getQueueNumber());
                lirl0310.setCarTraceNo(lirl0303.getCarTraceNo());
                lirl0310.setVehicleNo(lirl0303.getVehicleNo());
                lirl0310.setVoucherNum(lirl0303.getVoucherNum());
                lirl0310.setPriorityLevel(PRIORITY_LEVEL);
                lirl0310.setQueueDate(DateUtil.curDateTimeStr14());
                lirl0310.setTargetHandPointId(lirl0303.getTargetHandPointId());
                lirl0310.setFactoryArea(lirl0303.getFactoryArea());
                lirl0310.setOvertimeCount(String.valueOf(0));
                Map map = lirl0310.toMap();
                RecordUtils.setCreatorBYCall(map);
                lirl0310 = JSON.parseObject(JSON.toJSONString(map), LIRL0310.class);
                listLIRL0310.add(lirl0310);

                //批量插入
                this.dao.insert(LIRL0310.INSERT,lirl0310);
                //批量删除
                this.dao.delete(LIRL0303.DELETE,lirl0303);
            }
        }

        //处理排序表中的数据
        //1.根据账套查询车辆排序主表，把数据插入排序临时表中。
        List<LIRL0401> lirl0401List = this.dao.query(LIRL0401.QUERY, hashMapSegNo);
        if (CollectionUtils.isNotEmpty(lirl0401List)) {
            for (LIRL0401 lirl0401 : lirl0401List) {
                LIRL0310 lirl0310 = new LIRL0310();
                lirl0310.setUuid(UUIDUtils.getUUID());
                lirl0310.setSegNo(segNo);
                lirl0310.setUnitCode(segNo);
                lirl0310.setQueueNumber(lirl0401.getQueueNumber());
                lirl0310.setCarTraceNo(lirl0401.getCarTraceNo());
                lirl0310.setVehicleNo(lirl0401.getVehicleNo());
                lirl0310.setVoucherNum(lirl0401.getVoucherNum());
                lirl0310.setPriorityLevel(PRIORITY_LEVEL);
                lirl0310.setQueueDate(DateUtil.curDateTimeStr14());
                lirl0310.setTargetHandPointId(lirl0401.getTargetHandPointId());
                lirl0310.setFactoryArea(lirl0401.getFactoryArea());
                lirl0310.setOvertimeCount("0");
                Map map = lirl0310.toMap();
                RecordUtils.setCreatorBYCall(map);
                lirl0310 = JSON.parseObject(JSON.toJSONString(map), LIRL0310.class);
                listLIRL0310.add(lirl0310);

                //批量插入
                this.dao.insert(LIRL0310.INSERT,lirl0310);
                //批量删除
                this.dao.delete(LIRL0401.DELETE,lirl0401);
            }
        }

        int lv_again_count = 0;
        //将排序临时表中数据重新排列 插入排序表中
        //1.根据账套和超时次数、优先级、排序时间、排序次数查询车辆排序临时主表，将排序临时表中数据重新排列，插入排序表中
        List<LIRL0310> queryLirl0310 = this.dao.query(LIRL0310.QUERY, hashMapSegNo);
        List<LIRL0401> listLIRL0401 = new ArrayList<>();
        if (queryLirl0310.size() > 0) {
            List<LIRL0310> queryLirl0310New=new ArrayList<>();
            //控制是否走最新的逻辑
            if ("1".equals(new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALTERNATE_CALL", dao))) {
                //排队叫号规则调整
                queryLirl0310New = adjustQueuingCallingNumbers(queryLirl0310);
            }else {
                queryLirl0310New=queryLirl0310;
            }
            for (LIRL0310 lirl0408 : queryLirl0310New) {
                Map lirl0408Map = lirl0408.toMap();
                String carTraceNo = lirl0408.getCarTraceNo();
                lirl0408Map.put("uuid", UUIDUtils.getUUID());
                RecordUtils.setCreator(lirl0408Map);
                LIRL0401 lirl0401 = new LIRL0401();
                lirl0401.setSegNo(segNo);
                lirl0401.setUnitCode(segNo);
                lirl0401.setQueueNumber(lirl0408.getQueueNumber());
                lirl0401.setCarTraceNo(carTraceNo);
                lirl0401.setVehicleNo(lirl0408.getVehicleNo());
                lirl0401.setVoucherNum(lirl0408.getVoucherNum());
                lirl0401.setPriorityLevel(PRIORITY_LEVEL);
                lirl0401.setQueueDate(DateUtil.curDateTimeStr14());
                lirl0401.setTargetHandPointId(lirl0408.getTargetHandPointId());
                lirl0401.setFactoryArea(lirl0408.getFactoryArea());
                Map map = lirl0401.toMap();
                RecordUtils.setCreatorBYCall(map);
                lirl0401 = JSON.parseObject(JSON.toJSONString(map), LIRL0401.class);
                listLIRL0401.add(lirl0401);
                // lv_again_count = lv_again_count + 1;

                //批量插入
                this.dao.insert(LIRL0401.INSERT,lirl0401);
                // //批量删除
                this.dao.delete(LIRL0310.DELETE,lirl0401);
            }
        }


        //根据账套和顺序号正序排列查询车辆排序主表查出顺序号、车牌号、目标装卸点、
        // 优先级、排序时间、提单号、目标类型（固定无固定目标）、车辆跟踪号、厂区
        //控制是否走最新的逻辑
        List<LIRL0401> queryLirl0401=new ArrayList<>();
        if ("1".equals(new SwitchUtils().getProcessSwitchValue(segNo, "IF_ALTERNATE_CALL", dao))) {
            //排队叫号规则调整
            queryLirl0401 = this.dao.query(LIRL0401.QUERY_ALTERNATE_CALL, hashMap);
        }else {
            queryLirl0401 = this.dao.query(LIRL0401.QUERY, hashMap);
        }
        if (CollectionUtils.isNotEmpty(queryLirl0401) && queryLirl0401.size() > 0) {
            for (LIRL0401 lirl0401 : queryLirl0401) {
                String lv_min_hand_point = "";
                int lv_vehicle_count = 0;
                int lv_vehicle_count_temp = 0;
                Map lirl0401Map = lirl0401.toMap();
                //查看登记业务 无固定目标
                //根据车辆跟踪号、车牌号、账套查询车辆进厂登记表中的最大装卸类型
                int count = super.count(LIRL0302.QUERY_MAX_HAND_TYPE, lirl0401Map);
                //如果是装卸类型 必须要先去卸货点 先检查卸货点能否去
                if (count == 30) {
                    int queryLIRL0302 = super.count(LIRL0302.QUERY_HAND_POINT_IS_USED, lirl0401.toMap());
                    log("报错:装卸类型装卸点不可用,请检查!!!");
                    System.out.println("报错:装卸类型装卸点不可用,请检查!!!");
                    if (queryLIRL0302<=0) {
                        continue;
                    }
                }

                //无预约登记，只有分配的时间段到了，才可进入到排队叫号
                // 查询时间车辆是否有预约
                List<HashMap> queryLIRL0201 = this.dao.query(LIRL0306.QUERY_EXIST_REVERSION, lirl0401Map);
                if (queryLIRL0201.size()>0){
                    String isReservation = MapUtils.getString(queryLIRL0201.get(0), "isReservation");
                    String reservationTime = MapUtils.getString(queryLIRL0201.get(0), "reservationTime");
                    if ("00".equals(isReservation)){
                        //判断该预约单是否到了预约时段,如果到了，则进入排队叫号
                        if (DateUtil.curDateTimeStr14().compareTo(reservationTime) < 0){
                            continue;
                        }
                    }
                }

                //TODO 若是提前登记的车辆，此时装卸点被占用无法自动进入到排队叫号，若到了预约时段，此时装卸点空闲，则可以让此车辆自动进入到排队叫号中。（排队叫号处理这段逻辑）
                //1.查询车辆进厂登记表的车辆，
                //2.根据装卸点查询车辆登记表中的状态查看装卸点是否被占用？若到了预约时段，此时装卸点空闲可以让此车辆自动进入到排队叫号中
                Map<String, Object> hashMapLIRL0302 = new HashMap<>();
                hashMapLIRL0302.put("segNo",segNo);
                hashMapLIRL0302.put("carTraceNo",MapUtils.getString(lirl0401Map, "carTraceNo",""));
                hashMapLIRL0302.put("status","20");
                List<LIRL0302> queryLIRL03021 = this.dao.query(LIRL0302.QUERY, hashMapLIRL0302);
                //TODO 配置开关
                if (queryLIRL03021.size() > 0) {
                    //迟到/早到标记
                    String lateEarlyFlag = queryLIRL03021.get(0).getLateEarlyFlag();
                    if ("20".equals(lateEarlyFlag)) {//提前
                        //判断所有的点是什么状态
                        List<LIRL0306> queryLIRL0306= this.dao.query(LIRL0306.QUERY, lirl0401Map);
                        if (CollectionUtils.isNotEmpty(queryLIRL0306)){
                            String status = queryLIRL0306.get(0).getStatus();
                            if ("10".equals(status)){//饱和
                               //判断预约单是否倒了时间
                                queryLIRL0201 = this.dao.query(LIRL0306.QUERY_EXIST_REVERSION, lirl0401Map);
                                if (queryLIRL0201.size()>0){
                                    String reservationTime = MapUtils.getString(queryLIRL0201.get(0), "reservationTime");
                                    //判断该预约单是否到了预约时段,如果到了，则进入排队叫号
                                    if (DateUtil.curDateTimeStr14().compareTo(reservationTime) >= 0){
                                        queryLIRL0306= this.dao.query(LIRL0306.QUERY, lirl0401Map);
                                        if (CollectionUtils.isNotEmpty(queryLIRL0306)) {
                                            String statusNew = queryLIRL0306.get(0).getStatus();
                                            if ("10".equals(statusNew)) {//饱和
                                                continue;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //查询所有能进车的装卸点
                int queryLIRL0302 = super.count(LIRL0302.QUERY_HAND_POINT_IS_EXIST, lirl0401.toMap());
                if (queryLIRL0302 > 0) {
                    int lv_sub_count = 0;
                    int lv_job_count = 0;
                    //先检查预计作业数+当前作业数最少的装卸点 如果只有一个  则取此装卸点
                    List<HashMap> queryHandPointList = this.dao.query(LIRL0302.QUERY_MIN_HAND_POINT, lirl0401.toMap());
                    lv_job_count = lv_job_count + 1;
                    if (queryHandPointList.size() > 0&&CollectionUtils.isNotEmpty(queryHandPointList)) {
                        for (HashMap handPointMap : queryHandPointList) {
                            String handPointId = MapUtils.getString(handPointMap, "handPointId", "");
                            String handType = MapUtils.getString(handPointMap, "handType", "");
                            String jobNumber = MapUtils.getString(handPointMap, "jobNumber", "");
                            int jobNumber1 = Integer.parseInt(jobNumber.split("\\.")[0]); // 提取小数点前的部分
                            if (lv_job_count == 1) {
                                if ("20".equals(handType) || "30".equals(handType)||"40".equals(handType)) {
                                    lv_min_hand_point = handPointId;
                                    break;
                                }
                                lirl0401.setQueueNumber(jobNumber1);
                                int count1 = super.count(LIRL0302.QUERY_MIN_HAND_POINT_BY_JOB_NUMBER, lirl0401.toMap());
                                if (count1 == 1) {
                                    lv_min_hand_point = handPointId;
                                    break;
                                } else {
                                    Map<String, Object> LIRL0302Map = new HashMap<>();
                                    LIRL0302Map.putAll(lirl0401.toMap());
                                    LIRL0302Map.put("jobNumber",jobNumber1);
                                    //如果作业点数最小的有多个  再考虑后续进车最少以及最大容纳数问题
                                    List<HashMap> queryList = this.dao.query(LIRL0302.QUERY_HAND_POINT_BY_JOB_NUMBER_DESC, LIRL0302Map);
                                    for (HashMap map : queryList) {
                                        handPointId = MapUtils.getString(map, "handPointId", "");
                                        segNo = MapUtils.getString(map, "segNo", "");
                                        lv_sub_count = lv_sub_count + 1;
                                        // 再看这些装卸点中哪个装卸点用的最少   只看排队表中的装卸点
                                        // 如果所用车辆最少的装卸点有多个  则根据容纳车辆数最大的先选
                                        hashMap.put("segNo", segNo);
                                        hashMap.put("handPointId", handPointId);
                                        //查询
                                        lv_vehicle_count_temp = super.count(LIRL0401.QUERY_HAND_POINT_COUNT, hashMap);
                                        if (lv_sub_count == 1) {
                                            lv_vehicle_count = lv_vehicle_count_temp;
                                            lv_min_hand_point = handPointId;
                                        }
                                        if (lv_vehicle_count > lv_vehicle_count_temp) {
                                            lv_vehicle_count = lv_vehicle_count_temp;
                                            lv_min_hand_point = handPointId;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //挑选好要叫号的装卸点后，插入叫号表，目标装卸点更新为所挑选的装卸点
                    hashMap.put("handPointId", lv_min_hand_point);
                    //判断装卸点是否被占用
                    List<LIRL0306> queryLIRL0306 = this.dao.query(LIRL0306.QUERY, hashMap);
                    if (CollectionUtils.isNotEmpty(queryLIRL0306)){
                        String status = queryLIRL0306.get(0).getStatus();
                        if ("20".equals(status)){
                            //装卸点空闲直接插入叫号表----
                            //空闲直接插入到排队叫号表中
                            hashMap.put("uuid", UUIDUtils.getUUID());
                            hashMap.put("segNo", segNo);
                            hashMap.put("unitCode", segNo);
                            hashMap.put("queueNumber", MapUtils.getString(lirl0401Map, "queueNumber", ""));
                            hashMap.put("carTraceNo", MapUtils.getString(lirl0401Map, "carTraceNo", ""));
                            hashMap.put("vehicleNo", MapUtils.getString(lirl0401Map, "vehicleNo", ""));
                            hashMap.put("voucherNum", MapUtils.getString(lirl0401Map, "voucherNum", ""));
                            hashMap.put("priorityLevel", PRIORITY_LEVEL);
                            hashMap.put("queueDate", DateUtil.curDateTimeStr14());
                            hashMap.put("targetHandPointId", lv_min_hand_point);
                            hashMap.put("factoryArea", MapUtils.getString(lirl0401Map, "factoryArea", ""));
                            hashMap.put("remark", DateUtil.curDateTimeStr14().concat(":排队叫号"));
                            hashMap.put("sysRemark", DateUtil.curDateTimeStr14().concat(":排队叫号"));
                            RecordUtils.setCreatorBYCall(hashMap);
                            this.dao.insert(LIRL0402.INSERT, hashMap);
                            //更新装卸点进车跟踪数据
                            updateHandPointJobNumber(segNo, lv_min_hand_point, 0, 1, 10);
                            //排序表数据删除
                            this.dao.delete(LIRL0401.DELETE, hashMap);
                            //--插入叫号表之后 更新车辆跟踪表的目标装卸点
                            RecordUtils.setRevisorByCall(hashMap);
                            this.dao.update(LIRL0301.UPDATE_TARGET_HAND_POINT, hashMap);
                            //发送短信
                            senMessage(hashMap);
                        }
                    }
                }
            }
        }
        eiInfo.setStatus(EiConstant.STATUS_DEFAULT);
        System.out.println("-------------------------------------------处理正常数据结束--------------------------------------------------");
        //结束时间
        long endTime = System.currentTimeMillis();
        System.out.println("程序运行时间：" + (endTime - startTime) +"ms");
        log.info("程序运行时间：" + (endTime - startTime) + "ms");
    }

    /***
     * 发送短信
     * @param lirl0401 排序主表
     * @return
     */
    /***
     * 发送短信
     * @param lirl0401 排序主表
     * @return
     */
    public EiInfo senMessage(Map lirl0401) {
        EiInfo vzbmInfo = new EiInfo();
        List list = new ArrayList();
        String driverTel = "";//司机手机号
        String driverName = "";//司机姓名
        String expectedLoadingTime = "";//司机姓名
        String targetHandPointId = MapUtils.getString(lirl0401, "targetHandPointId", "");//装卸点
        String carTraceNo = MapUtils.getString(lirl0401, "carTraceNo", "");//装卸点
        String vehicleNo = MapUtils.getString(lirl0401, "vehicleNo", "");//装卸点
        String segNo = MapUtils.getString(lirl0401, "segNo", "");//装卸点
        String handPointName= "";

        //根据车辆跟踪号、车牌号、账套查询司机信息
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("carTraceNo",carTraceNo);
        hashMap.put("vehicleNo",vehicleNo);
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",targetHandPointId);
        //查询装卸点名称
        List<LIRL0304> queryLIRL0304 = this.dao.query(LIRL0304.QUERY_HAND_POINT_INFO1, hashMap);
        if (queryLIRL0304.size()>0){
            handPointName = queryLIRL0304.get(0).getHandPointName();
        }
        List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, hashMap);
        if (queryLIRL0301.size()>0){
            driverName= queryLIRL0301.get(0).getDriverName();
            driverTel= queryLIRL0301.get(0).getTelNum();
            vehicleNo=queryLIRL0301.get(0).getVehicleNo();
            expectedLoadingTime=queryLIRL0301.get(0).getExpectedLoadingTime();
        }
//        String content="";
//        if (StringUtils.isBlank(expectedLoadingTime)){
//            content = "车牌号："+vehicleNo+"，司机："+driverName+"现可进厂，请到"+handPointName+"装卸点装卸货，若车辆在15分钟内未能进厂，将优先呼叫后续车辆，谢谢。";
//        }else {
//            content  = "车牌号："+vehicleNo+"，司机："+driverName+"现可进厂，预计装货"+expectedLoadingTime+"分钟，请到"+handPointName+"装卸点装卸货，若车辆在15分钟内未能进厂，将优先呼叫后续车辆，谢谢。";
//        }
        //短信内容
        list.add(vehicleNo);//司机姓名
        list.add(driverName);//验证码
        list.add(handPointName);//装卸点名称

        //短信接收人信息
        List list2 = new ArrayList();
        HashMap hashMap2 = new HashMap<>();
        hashMap2.put("mobile",driverTel);
        hashMap2.put("receiverJobNo",driverName);
        hashMap2.put("receiverName", driverName);
        list2.add(hashMap2);

        vzbmInfo.set("params",list);
        vzbmInfo.set("receiver",list2);
        vzbmInfo.set("msgTemplateId","MT0000001001");//短信登记号
        vzbmInfo.set(EiConstant.serviceId, "S_VI_PM_9067");
        vzbmInfo = EServiceManager.call(vzbmInfo, TokenUtils.getXplatToken());
        if (vzbmInfo.getStatus() == -1) {
            // 调用失败
            throw new RuntimeException("调用平台短信发送服务报错:" + vzbmInfo.getMsg());
        }
        if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            //打印日志到elk
            log(  "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
            //输出到应用日志
            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
            throw new RuntimeException(vzbmInfo.toJSONString());
        } else {
            //打印日志到elk
            log( "排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
            //输出到应用日志
            System.out.println("排队叫号定时任务：短信接口传入参数：" + vzbmInfo + "\n" + "排队叫号定时任务 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
        }
        return vzbmInfo;
    }

    /***
     *
     * @param segNo 账套
     * @param lv_min_hand_point 装卸点代码
     * @param in_current_job_number 当前作业数
     * @param in_pre_job_number 预计作业数
     * @param in_trace_type 跟踪类型
     *   --in_trace_type 极值解释  系统有表记录 wl.t_i_hand_trace_type
     *   --10 插入叫号表         当前装卸点预计作业数+1   通道预计作业装卸点数+1
     *   --11 优尼进厂           当前装卸点预计作业数+1   通道预计作业装卸点数+1
     *   --20 进厂               当前装卸点预计作业数-1+1 通道预计作业装卸点数-1+1
     *   --21 取消进厂           当前装卸点预计作业数+1-1 通道预计作业装卸点数+1-1
     *   --22 自动删除进厂       当前装卸点预计作业数-1  通道预计作业装卸点数-1  高强钢超时自动撤销进厂过程(相当于叫号之后，车辆可能出故障没进厂(因为进厂都是自动的))
     *   --30 超时删除叫号       当前装卸点预计作业数-1   通道预计作业装卸点数-1
     *   --31 撤销登记删除叫号   当前装卸点预计作业数-1   通道预计作业装卸点数-1
     *   --32 撤销车辆进厂删除叫号(佛宝) 当前装卸点预计作业数-1   通道预计作业装卸点数-1
     *   --40 开始装卸货一致     选择装卸点与目标装卸点一致：当前装卸点预计作业数-1,当前作业数+1 通道预计作业装卸点数-1,当前作业装卸点数+1
     *   --41 撤销开始装卸货一致 选择装卸点与目标装卸点一致：当前装卸点预计作业数+1,当前作业数-1 通道预计作业装卸点数+1,当前作业装卸点数-1
     *   --42 开始装卸货(佛宝)   删除叫号表 当前装卸点预计作业数-1+1 通道预计作业数-1+1
     *   --43 撤销开始装卸货(佛宝) 恢复叫号表  当前装卸点预计作业数+1-1 通道预计作业数+1-1
     *   --50 开始装卸货不一致   选择装卸点与目标装卸点不一致:当前装卸点预计作业数-1,所在通道预计作业装卸点数-1 选择装卸点当前作业数+1,所在通道当前作业装卸点数+1
     *   --51 撤销开始装卸货不一致   选择装卸点与目标装卸点不一致:当前装卸点预计作业数+1,所在通道预计作业装卸点数+1 选择装卸点当前作业数-1,所在通道当前作业装卸点数-1
     *   --60 结束装卸货         当前装卸点当前作业数-1,  所在通道当前作业装卸点数-1
     *   --61 撤销结束装卸货         当前装卸点当前作业数+1,  所在通道当前作业装卸点数+1
     *   --70 结束装卸货选择下一装卸点   下个装卸点预计作业数+1,所在通道预计作业装卸点数+1
     *   --71 撤销结束装卸货选择下一装卸点   下个装卸点预计作业数-1,所在通道预计作业装卸点数-1
     *   --81 未装离厂时         当前装卸点预计作业数-1   通道预计作业装卸点-1
     *   --90 更换装卸点         当前装卸点预计作业数-1  通道预计作业装卸点数-1
     *   --91 更换装卸点         目标装卸点预计作业数+1  通道预计作业数+1
     *   --92 更换装卸点         离厂，目标装卸点预计作业数-1，通道预计作业装卸点数-1
     * @return
     */
    public EiInfo updateHandPointJobNumber(String segNo, String lv_min_hand_point, int in_current_job_number, int in_pre_job_number, int in_trace_type) {
        EiInfo eiInfo = new EiInfo();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",lv_min_hand_point);
        hashMap.put("currentJobNumber",in_current_job_number);
        hashMap.put("preJobNumber",in_pre_job_number);
        hashMap.put("remark"," ");
        hashMap.put("sysRemark"," ");
        hashMap.put("delFlag","0");
        hashMap.put("tenantId","");
        hashMap.put("archiveFlag","0");
        hashMap.put("recCreator", "system");
        hashMap.put("recCreatorName", "system");
        hashMap.put("recRevisor", "system");
        hashMap.put("recRevisorName", "system");
        // 修改时间
        hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());

        // 创建时间
        hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());

        RecordUtils.setRevisorByCall(hashMap);

        // //取出装卸点的当前、预计作业数
        // List<LIRL0306> queryLIRL0306 = this.dao.query(LIRL0306.QUERY, hashMap);
        // if (CollectionUtils.isNotEmpty(queryLIRL0306)){
        //     int currentJobNumber = queryLIRL0306.get(0).getCurrentJobNumber().intValue();
        //     int preJobNumber = queryLIRL0306.get(0).getPreJobNumber().intValue();
        //     currentJobNumber=currentJobNumber+currentJobNumber;
        //     preJobNumber=preJobNumber+in_pre_job_number;
        //     hashMap.put("preJobNumber",preJobNumber);
        //     hashMap.put("currentJobNumber",currentJobNumber);
        // }
        if (in_trace_type==20 || in_trace_type==42){
        //--如果是进厂删除叫号表，预计作业数都是-1+1
            //更新装卸点进车跟踪表
            this.dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT,hashMap);
        }else if (in_trace_type==21){
            //更新装卸点进车跟踪表
            this.dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT,hashMap);
        }else {
            //更新装卸点进车跟踪表
            this.dao.update(LIRL0306.UPDATE_JOB_NUMBER_BY_HAND_POINT,hashMap);
        }
        //调用状态变化过程
        eiInfo = procHandPointStatusTrace(segNo, lv_min_hand_point);
        return eiInfo;
    }

    /***
     * 更新装卸状态
     * @param segNo 系统账套
     * @param lv_min_hand_point 装卸点代码
     */
    private EiInfo procHandPointStatusTrace(String segNo, String lv_min_hand_point) {
        EiInfo eiInfo = new EiInfo();
        // --作业状态：
        // --10车辆饱和：当前+预计>=最大容纳车辆数
        // --20可以进车：当前作业数=0，预计作业数=0  当前+预计<最大容纳车辆数
        // --30暂停进车：装卸点暂不用
        //查询装卸点状态跟踪表
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        hashMap.put("handPointId", lv_min_hand_point);
        hashMap.put("recCreator", "system");
        hashMap.put("recCreatorName", "system");
        hashMap.put("recRevisor", "system");
        hashMap.put("recRevisorName", "system");
        // 修改时间
        hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());

        // 创建时间
        hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
        String lv_status = "";
        List<LIRL0306> queryLIRL0306 = this.dao.query(LIRL0306.QUERY, hashMap);
        if (queryLIRL0306.size() > 0) {
            BigDecimal currentJobNumber = queryLIRL0306.get(0).getCurrentJobNumber(); //当前作业数
            BigDecimal preJobNumber = queryLIRL0306.get(0).getPreJobNumber();//预计作业数
            //查此装卸点的最大容纳车辆数
            List<HashMap> queryLIRL0304 = this.dao.query(LIRL0304.QUERY_VEHICLE_NUMER, hashMap);
            if (queryLIRL0304.size() > 0) {
                String vehicleNumer = MapUtils.getString(queryLIRL0304.get(0), "vehicleNumer", "");
                // 10车辆饱和：当前+预计>=最大容纳车辆数

                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))>=Float.parseFloat(vehicleNumer)&&Float.parseFloat(vehicleNumer)!=0) {
                    lv_status = "10";//饱和
                }
                // --20可以进车：当前作业数=0，预计作业数=0  当前+预计<最大容纳车辆数
                //TODO 分开转换
                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))<Float.parseFloat(vehicleNumer)) {
                    lv_status = "20";//可以进车
                }

                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))>Float.parseFloat(vehicleNumer)&&Float.parseFloat(vehicleNumer)==0) {
                    lv_status = "10";//饱和
                }

                if (Float.parseFloat(String.valueOf(currentJobNumber))+Float.parseFloat(String.valueOf(preJobNumber))==0&&Float.parseFloat(vehicleNumer)==0){
                    lv_status = "20";//可以进车
                }
                hashMap.put("status", lv_status);
                // 更新车辆跟踪的进车状态
                this.dao.update(LIRL0306.UPDATELV_STATUS, hashMap);
            }

        }
        return eiInfo;
    }

    /***
     * 排队叫号规则修改
     */
    public List<LIRL0310> adjustQueuingCallingNumbers(List<LIRL0310> list) {
        List<LIRL0310> lirl0310s = new ArrayList<>();
        List<Map> lirl0310befoer = new ArrayList<>();
        String segNo = "";
        for (LIRL0310 lirl0310 : list) {
            String carTraceNo = lirl0310.getCarTraceNo();
            segNo = lirl0310.getSegNo();
            //查询车辆的预约时间
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("segNo", segNo);
            stringStringHashMap.put("carTraceNo", carTraceNo);
            stringStringHashMap.put("status", "10");
            List<HashMap> queryLIRL0301 = this.dao.query(LIRL0301.QUERY_RESERVATION_DATE, stringStringHashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL0301)) {
                String reservationDate = MapUtils.getString(queryLIRL0301.get(0), "reservationDate"); //预约日期
                String reservationTime = MapUtils.getString(queryLIRL0301.get(0), "reservationTime"); //预约时段
                String checkDate = MapUtils.getString(queryLIRL0301.get(0), "checkDate"); //登记时间
                String beforeTime = reservationTime.split("-")[0];
                String afterTime = reservationTime.split("-")[1];
                //组装时间
                String reservationTimeBefore = reservationDate.concat(beforeTime.replace(":", ""));
                String reservationTimeAfter = reservationDate.concat(afterTime.replace(":", ""));
                //获取当前时间
                checkDate = checkDate.substring(0, checkDate.length() - 2);//登记时间

                Map map = lirl0310.toMap();
                map.put("checkDate", checkDate);
                map.put("reservationTimeBefore", reservationTimeBefore);
                map.put("reservationTimeAfter", reservationTimeAfter);
                lirl0310befoer.add(map);
            }
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");

        // 第一次叫号，按预约时间段的早晚排序
        // 当天的23:59:59  停止自动叫号
        // 当天的23:59:59  停止自动叫号
        // 按当天叫号时段的最早时间+10分钟，启动叫号
        //查询当天最早预约时间+10
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("segNo", segNo);
        int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();//当前天是星期几
        switch (dayOfWeek) {
            case 1:
                hashMap.put("weekDays", "一");
                break;
            case 2:
                hashMap.put("weekDays", "二");
                break;
            case 3:
                hashMap.put("weekDays", "三");
                break;
            case 4:
                hashMap.put("weekDays", "四");
                break;
            case 5:
                hashMap.put("weekDays", "五");
                break;
            case 6:
                hashMap.put("weekDays", "六");
                break;
            case 7:
                hashMap.put("weekDays", "日");
                break;
        }
        List<String> queryList = this.dao.query(LIRL0105.QUERY_RESERVESION, hashMap);
        if (CollectionUtils.isNotEmpty(queryList)) {
            String[] split = queryList.get(0).split("-");
            //获取小时和分钟
            String[] split1 = split[0].split(":");
            // 获取当前时间
            LocalTime now = LocalTime.now();

            // 最早时间是 07:30
            LocalTime earliestTime = LocalTime.of(Integer.parseInt(split1[0]), Integer.parseInt(split1[1]));

            // 启动叫号时间 = 最早时间 + 10 分钟
            LocalTime startCallingTime = earliestTime.plusMinutes(10);
            LocalTime endCallingTime = earliestTime.plusMinutes(12);

            //第一次
            // 判断是否在叫号时段内
            if (now.isAfter(startCallingTime) && now.isBefore(endCallingTime)) {
                lirl0310befoer.sort((v1, v2) -> {
                    try {
                        Date v1Start = dateFormat.parse(MapUtils.getString(v1, "reservationTimeBefore"));
                        Date v2Start = dateFormat.parse(MapUtils.getString(v2, "reservationTimeBefore"));
                        return v1Start.compareTo(v2Start);
                    } catch (ParseException e) {
                        throw new RuntimeException("时间解析失败", e);
                    }
                });
                System.out.println("第一次叫号顺序：");
                lirl0310befoer.forEach(System.out::println);
            }else {
                // 叫号时间
                String callTimeStr = DateUtil.curDateTimeStr14();
                callTimeStr= callTimeStr.substring(0, callTimeStr.length() - 2);
                Date callTime = null;
                try {
                    callTime = dateFormat.parse(callTimeStr);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                // 第二次及以后的叫号规则
                Date finalCallTime = callTime;
                lirl0310befoer.sort((v1, v2) -> {
                    try {
                        Date v1Start = dateFormat.parse(MapUtils.getString(v1, "reservationTimeBefore"));
                        Date v2Start = dateFormat.parse(MapUtils.getString(v2, "reservationTimeBefore"));
                        Date v1Reg = dateFormat.parse(MapUtils.getString(v1, "checkDate"));
                        Date v2Reg = dateFormat.parse(MapUtils.getString(v2, "checkDate"));

                        // 如果预约时间在叫号时间之前，按登记时间排序
                        if (v1Start.before(finalCallTime)) {
                            if (v2Start.before(finalCallTime)) {
                                return v1Reg.compareTo(v2Reg);
                            } else {
                                return -1; // v1 在叫号时间之前，v2 在之后，v1 优先
                            }
                        } else if (v2Start.before(finalCallTime)) {
                            return 1; // v2 在叫号时间之前，v1 在之后，v2 优先
                        } else {
                            // 如果预约时间在叫号时间之后，按登记时间排序，但排在最后
                            return v1Reg.compareTo(v2Reg);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("时间解析失败", e);
                    }
                });

                System.out.println("第二次及以后的叫号顺序：");
                lirl0310befoer.forEach(System.out::println);
            }
        }

        int count = 0;
        for (Map map : lirl0310befoer) {
            map.put("queueNumber",count);
            map.remove("checkDate");
            map.remove("reservationTimeBefore");
            map.remove("reservationTimeAfter");
            count++;
        }

        ObjectMapper mapper = new ObjectMapper();
        lirl0310s = lirl0310befoer.stream()
                .map(map -> mapper.convertValue(map, LIRL0310.class))
                .collect(Collectors.toList());

        //map转成实体类
        // lirl0310s = lirl0310befoer.stream()
        //         .map(map -> mapToEntity(map, LIRL0310.class))
        //         .collect(Collectors.toList());

        return lirl0310s;
    }

    public  <T> T mapToEntity(Map<String, Object> map, Class<T> clazz) {
        try {
            T entity = clazz.getDeclaredConstructor().newInstance();
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (map.containsKey(fieldName)) {
                    Object value = map.get(fieldName);
                    field.set(entity, value);
                }
            }
            return entity;
        } catch (Exception e) {
            throw new RuntimeException("转换失败", e);
        }
    }

    /***
     * 车辆出厂
     * @param eiInfo
     * @return
     */
    public EiInfo vehicleLeaveFactory(EiInfo eiInfo) {

        try {
            List<HashMap> result2 = eiInfo.getBlock("result2").getRows();
            for (HashMap hashMap : result2) {
                String segNo = MapUtils.getString(hashMap, "segNo");
                String carTraceNo = MapUtils.getString(hashMap, "carTraceNo");
                String stringDate = DateUtil.curDateTimeStr14();//出厂时间
                Date date = new Date(System.currentTimeMillis());
                //查询出来跟踪表中的数据插入到出厂维护表中
                List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, hashMap);
                if (queryLIRL0301.size() < 0) {
                    String massage = "车辆跟踪表中数据不存在！";
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg(massage);
                    throw new RuntimeException(massage);
                }
                //出厂流水号
                String strSeqTypeId = "TMELI0408_SEQ01";
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String leaveFactoryId = SequenceGenerator.getNextSequence(strSeqTypeId, args); //出厂流水号
                String vehicleNo = queryLIRL0301.get(0).getVehicleNo();
                String factoryArea = queryLIRL0301.get(0).getFactoryArea();
                String handType = queryLIRL0301.get(0).getHandType();
                hashMap.put("leaveFactoryId", leaveFactoryId);
                hashMap.put("vehicleNo", vehicleNo);
                hashMap.put("factoryArea", factoryArea);
                hashMap.put("status", MesConstant.Status.K10);
                hashMap.put("leaveFactoryDate", stringDate);
                hashMap.put("uuid", UUIDUtils.getUUID());
                RecordUtils.setCreator(hashMap);
                LIRL0408 lirl0408 = new LIRL0408();
                lirl0408.fromMap(hashMap);

                // 修改跟踪表的状态
                HashMap<String, String> hashMapLirl0301 = new HashMap<>();
                hashMapLirl0301.put("segNo", segNo);
                hashMapLirl0301.put("carTraceNo", carTraceNo);
                hashMapLirl0301.put("leaveFactoryDate", stringDate);
                hashMapLirl0301.put("delFlag", "0");
                hashMapLirl0301.put("status", "50");
                RecordUtils.setRevisor(hashMapLirl0301);
                this.dao.update(LIRL0301.UPDATE_LIRL0301CC, hashMapLirl0301);

                //把出厂数据插入发货看板车辆跟踪管理备份表
                /*
                自提：
                如果客户自提，直接扔到备份表
                代运：
                客户签收后之后才能插入备份表
                如果业务类型：装卸货直接插入备份表
                */
                String identityType = "";
                String reservationNumber = queryLIRL0301.get(0).getReservationNumber();
                if ("30".equals(handType)) {
                    //直接插入备份表
                    LIRL0301 lirl0301 = queryLIRL0301.get(0);
                    Map map = lirl0301.toMap();
                    map.put("status", "50");
                    map.put("leaveFactoryDate", stringDate);
                    map.put("uuid", UUIDUtils.getUUID());
                    RecordUtils.setCreator(map);
                    this.dao.insert(LIRL0311.INSERT, map);
                }else {
                    //查看是自提还是代运
                    String driverName = queryLIRL0301.get(0).getDriverName();
                    String telNum = queryLIRL0301.get(0).getTelNum();
                    HashMap<String, String> hashMapLIRL0102 = new HashMap<>();
                    hashMapLIRL0102.put("segNo", segNo);
                    hashMapLIRL0102.put("reservationNumber", reservationNumber);
                    hashMapLIRL0102.put("driverName", driverName);
                    hashMapLIRL0102.put("telNum", telNum);
                    List<LIRL0101> queryLIRL0101 = this.dao.query(LIRL0101.QUERY, hashMapLIRL0102);
                    if (queryLIRL0101.size() > 0) {
                        identityType = queryLIRL0101.get(0).getIdentityType();
                        if (MesConstant.Status.K10.equals(identityType)) {
                            //自提，直接扔到备份表
                            LIRL0301 lirl0301 = queryLIRL0301.get(0);
                            Map map = lirl0301.toMap();
                            map.put("status", "50");
                            map.put("leaveFactoryDate", stringDate);
                            map.put("uuid", UUIDUtils.getUUID());
                            RecordUtils.setCreator(map);
                            this.dao.insert(LIRL0311.INSERT, map);
                        } else {
                            //代运，查看是否客户签收，签收后插入备份表
                            //小程序签收时,出库单信息同步插入车辆跟踪表备份表
                            // 查询车辆跟踪
                            // String status = queryLIRL0101.get(0).getStatus();
                            //自提，直接扔到备份表
                            LIRL0301 lirl0301 = queryLIRL0301.get(0);
                            Map map = lirl0301.toMap();
                            map.put("status", "50");
                            map.put("leaveFactoryDate", stringDate);
                            map.put("uuid", UUIDUtils.getUUID());
                            RecordUtils.setCreator(map);
                            this.dao.insert(LIRL0311.INSERT, map);

                        }
                    }
                }

                //删除车辆跟踪表
                Map<Object, Object> hashMapLIRL0301 = new HashMap<>();
                hashMapLIRL0301.put("segNo", segNo);
                hashMapLIRL0301.put("carTraceNo", carTraceNo);
                RecordUtils.setRevisor(hashMapLIRL0301);
                this.dao.delete(LIRL0301.DELETE, hashMapLIRL0301);

                //更新车辆预约表状态
                Map updateLIRL0201 = new HashMap();
                updateLIRL0201.put("segNo", segNo);
                updateLIRL0201.put("reservationNumber", reservationNumber);
                updateLIRL0201.put("status", "99");
                updateLIRL0201.put("recReviseTime", stringDate);
                updateLIRL0201.put("delFlag", 0);
                RecordUtils.setRevisor(updateLIRL0201);
                dao.update(LIRL0201.UPDATE_STATUS, updateLIRL0201);

                //判断是否开始运输返单功能
                String ifTransportationRefund = new SwitchUtils().getProcessSwitchValue(segNo, "IF_TRANSPORTATION_REFUND", dao);
                if ("0".equals(ifTransportationRefund)||StringUtils.isBlank(ifTransportationRefund)){
                   continue;
                }
                //增加运输返单功能
                List<String> billIds = new ArrayList<>();//提单集合
                List<String> packIds = new ArrayList<>();//捆包集合
                List<HashMap> list = new ArrayList<>();//捆包集合
                HashMap<String, Object> hashMapTwo = new HashMap<>();
                //根据车辆跟踪号、车牌号、账套查询提单、捆包明细
                HashMap<String, String> hashMapOne = new HashMap<>();
                hashMapOne.put("segNo", segNo);
                hashMapOne.put("carTraceNo", carTraceNo);
                hashMapOne.put("vehicleNo", vehicleNo);

                billIds = this.dao.query(LIRL0308.QUERY_B_LINFO, hashMapOne);
                if (CollectionUtils.isNotEmpty(billIds)){
                    //查询捆包
                    packIds = this.dao.query(LIRL0308.QUERY_P_CINFO, hashMapOne);

                    hashMapTwo.put("billIds",billIds);
                    hashMapTwo.put("packIds",packIds);
                    hashMapTwo.put("channelTransId",leaveFactoryId);
                    hashMapTwo.put("operateType","10");
                    list.add(hashMapTwo);


                    EiInfo outInfo =new EiInfo();
                    eiInfo.set("list",list);
                    eiInfo.set("segNo",segNo);
                    eiInfo.set(EiConstant.serviceId,"S_UC_PR_0424");
                    try {
                        outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                           //失败记录次数
                            lirl0408.setBackStatus("-1");
                        }else {
                            lirl0408.setBackStatus("1");
                            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                        }
                    } catch (Exception e) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg(e.getMessage());
                        return outInfo;
                    }
                    this.dao.insert(LIRL0408.INSERT, lirl0408);
                }
            }
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsgByKey(MesConstant.EPResource.EP_0001, new String[]{ex.getMessage()});
        }

        return eiInfo;
    }


    /***
     * 根据账套、身份证号查询车牌
     * segNo 账套
     *
     */
    public EiInfo lookUpTheLicensePlate(EiInfo eiInfo){
        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = dateFormat.format(l);//当前日期
        String segNo = (String) eiInfo.get("segNo");//账套
        String driverIdentity = (String) eiInfo.get("driverIdentity");//身份证号
        Map hashMap = new HashMap();
        hashMap.put("segNo",segNo);
        hashMap.put("driverIdentity",driverIdentity);
        hashMap.put("reservationDate",format);
        hashMap.put("status",20);
        hashMap.put("isReservation", "10");

        /*//查询司机今天预约任务
        List<HashMap> hashMapList = this.dao.query(LIRL0201.QUERY_VEHICLE_NO,hashMap);
        *//*List<HashMap> hashMapList = this.dao.query(LIRL0103.LOOK_UP_THE_LICENSE_PLATE, hashMap);*//*
        LinkedHashSet<HashMap> set = new LinkedHashSet<>(hashMapList);
        hashMapList = new ArrayList<>(set);
        if (CollectionUtils.isEmpty(hashMapList)){
            //查超时或者提前的预约单
            hashMap.put("reservationDate","");
            // hashMap.put("flag","all");
            hashMap.put("isReservation", "10");
            hashMapList = this.dao.query(LIRL0201.QUERY_VEHICLE_NO, hashMap);
        }*/

        List<HashMap> hashMapList = new ArrayList<>();
        //如果没有预约单 查询司机下所有车辆
        if (CollectionUtils.isEmpty(hashMapList)){
            Map queryLIRL0102 = new HashMap();
            queryLIRL0102.put("segNo",segNo);
            queryLIRL0102.put("driverIdentity",driverIdentity);
            queryLIRL0102.put("status",20);
            List<HashMap> mapList = dao.query(LIRL0102.QUERY, queryLIRL0102);
            for (HashMap hashMap1:mapList){
                String pageId = MapUtils.getString(hashMap1, "pageId", "");
                String tel = MapUtils.getString(hashMap1, "tel", "");
                Map queryLIRL0103 = new HashMap();
                queryLIRL0103.put("segNo",segNo);
                queryLIRL0103.put("m_uuid",pageId);
                queryLIRL0103.put("statuss",20);
                List<LIRL0103> lirl0103s = dao.query(LIRL0103.QUERY, queryLIRL0103);
                for (LIRL0103 lirl0103:lirl0103s){
                    HashMap hashMap2 = new HashMap();
                    hashMap2.put("segNo",segNo);
                    hashMap2.put("vehicleNo",lirl0103.getVehicleNo());
                    hashMap2.put("driverTel",tel);
                    hashMapList.add(hashMap2);
                }
            }
        }
        List<HashMap> deduplicatedList = new ArrayList<>();
        if (hashMapList.size()>0){
            deduplicatedList = deduplicateList(hashMapList);
        }


        //判断若当天此司机只有一次预约任务，则车牌号直接默认，
        // 若此司机当天有多次预约任务并且车牌号不同，则车牌号不进行默认，
        // 车牌号查出显示即可，由司机自行选择
        /*if (lirl0201s.size()>1){
            for (LIRL0201 lirl0201:lirl0201s){
                list.add(lirl0201.getVehicleNo());
            }
        }else if (lirl0201s.size() == 1){
            list.add(lirl0201s.get(0).getVehicleNo());
        }*/

        eiInfo.set("list",deduplicatedList);
        return eiInfo;
    }

    public static List<HashMap> deduplicateList(List<HashMap> hashMapList) {
        List<HashMap> resultList = new ArrayList<>();
        for (HashMap map : hashMapList) {
            if (!containsMap(resultList, map)) {
                resultList.add(map);
            }
        }
        return resultList;
    }

    private static boolean containsMap(List<HashMap> list, HashMap targetMap) {
        for (HashMap map : list) {
            if (map.equals(targetMap)) {
                return true;
            }
        }
        return false;
    }

    /***
     * 查询是否有当前时段预约
     * segNo 账套
     *
     */
    public EiInfo   currentAppointments(EiInfo eiInfo){
        try {
            long l = System.currentTimeMillis(); //获取时间戳效率最高
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
            String format = dateFormat.format(l);//当前日期
            //校验预约单号。
            //如有登记时间中的预约单，可以直接带入。（业务+车牌号+司机+时间匹配）
            String segNo = (String) eiInfo.get("segNo");//账套
            String vehicleNo = (String) eiInfo.get("vehicleNo");//车牌号
            String businessType = (String) eiInfo.get("businessType");//业务类型
            String voucherNumHandType = (String) eiInfo.get("handType");//装卸类型
            String driverIdentity = (String) eiInfo.get("driverIdentity");//司机身份
            String isNoReservation = (String) eiInfo.get("isNoReservation");//司机身份
            List<String> voucherNumList = (List<String>) eiInfo.get("voucherNumList");//提单号
            String voucherNum1 = "";
            String voucherNum = "";
            if (CollectionUtils.isNotEmpty(voucherNumList)){
                voucherNum1 = String.join(",",voucherNumList);
            }
            String factoryArea = (String) eiInfo.get("factoryArea");//厂区
            String factoryAreaName = (String) eiInfo.get("factoryAreaName");//厂区名称
            String driverTel = (String) eiInfo.get("driverTel");//司机手机号
            String driverName = (String) eiInfo.get("driverName");//司机姓名
            String fileUUID = (String) eiInfo.get("fileUUID");//文件ID
            String customerId = (String) eiInfo.get("customerId");//客户代码
            String customerName = (String) eiInfo.get("customerName");//客户名称

            //提单号不为空 查询提单是否有效
            if (StringUtils.isNotBlank(voucherNum1)) {
                for (String s : voucherNumList) {
                    voucherNum=s;
                    EiInfo outInfo = new EiInfo();
                    //判断是是否是转库单
                    String substring = voucherNum.substring(0, 2);
                    if ("ZK".equals(substring)) {
                        //调IMC服务拿到捆包信息
                        EiInfo eiInfo1 = new EiInfo();
                        eiInfo1.set("segNo", segNo);
                        eiInfo1.set("transBillId", voucherNum);
                        eiInfo1.set(EiConstant.serviceId, "S_UC_EP_0044");
                        //调post请求
                        outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == -1) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                            throw new RuntimeException(outInfo.getMsg());
                        }
                        List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
                        if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("未匹配到转库单信息，不允许登记!");
                            return outInfo;
                        }
                        // else {
                        //     String ladingBillStatus = MapUtils.getString(rusult.get(0), "transBillType", "");//开单方式 10-按捆包 20-按重量 30-按件数
                        //     if ("00".equals(ladingBillStatus)){
                        //         outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        //         outInfo.setMsg("该转库单未生成计划！");
                        //         return outInfo;
                        //     }else if ("20".equals(ladingBillStatus)){
                        //         outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        //         outInfo.setMsg("该转库单已出库！");
                        //         return outInfo;
                        //     }else if ("30".equals(ladingBillStatus)){
                        //         outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        //         outInfo.setMsg("该转库单已完成！");
                        //         return outInfo;
                        //     }else if ("90".equals(ladingBillStatus)){
                        //         outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        //         outInfo.setMsg("该转库单已撤销！");
                        //         return outInfo;
                        //     }
                        // }
                    } else {
                        //调IMC服务拿到捆包信息
                        EiInfo eiInfo1 = new EiInfo();
                        eiInfo1.set("segNo", segNo);
                        eiInfo1.set("ladingBillId", voucherNum);
                        // eiInfo1.set("ladingBillStatus", "50");//生效
                        eiInfo1.set(EiConstant.serviceId, "S_UV_SL_9018");
                        //调post请求
                        outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == -1) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                            throw new RuntimeException(outInfo.getMsg());
                        }
                        List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
                        List<HashMap> result2 = (List<HashMap>) outInfo.get("result2");
                        if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("未匹配到提单信息，不允许登记!");
                            return outInfo;
                        }
                        if (rusult.size() > 0) {
                            //判断开单方式
                            HashMap hashMapRusult = rusult.get(0);
                            String billingMethod = MapUtils.getString(hashMapRusult, "billingMethod", "");//开单方式 10-按捆包 20-按重量 30-按件数
                            String ladingBillStatus = MapUtils.getString(hashMapRusult, "ladingBillStatus", "");//开单方式 10-按捆包 20-按重量 30-按件数
                            if ("10".equals(ladingBillStatus)) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("该提单新增未生效！");
                                return outInfo;
                            } else if ("00".equals(ladingBillStatus)) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("该提单已被撤销！");
                                return outInfo;
                            } else if ("90".equals(ladingBillStatus)) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("该提单已提货完成！");
                                return outInfo;
                            } else if ("20".equals(ladingBillStatus)) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("该提单已提交未生效！");
                                return outInfo;
                            } else if ("99".equals(ladingBillStatus)) {
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg("该提单已强制完成！");
                                return outInfo;
                            }
                            if (!"10".equals(billingMethod)) {
                            } else {
                                if (result2.size() > 0) {
                                } else {
                                    String massage = "未匹配到提单信息，不允许登记!";
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg(massage);
                                    return outInfo;
                                }
                            }
                        }
                    }
                }
            }
            //1、针对周转架、废料提货、资材卸货、欧冶电商密码提货4种业务，不强控登记的司机信息在MES中有维护且生效
            // if (ObjectUtils.notEqual("20",businessType)&& ObjectUtils.notEqual("40",businessType)&&ObjectUtils.notEqual("50",businessType)&&ObjectUtils.notEqual("30",businessType)) {
            //     //根据司机身份证查询司机信息
            //     Map queryLIRL0102 = new HashMap();
            //     queryLIRL0102.put("segNo", segNo);
            //     queryLIRL0102.put("status", "20");
            //     queryLIRL0102.put("driverIdentity", driverIdentity);
            //     queryLIRL0102.put("tel", driverTel);
            //     queryLIRL0102.put("driverName", driverName);
            //     queryLIRL0102.put("delFlag", "0");
            //     List<HashMap> hashMapList = dao.query(LIRL0102.QUERY, queryLIRL0102);
            //     // String driverTel = "";
            //     if (hashMapList.size() > 0) {
            //         HashMap hashMap = hashMapList.get(0);
            //         driverTel = MapUtils.getString(hashMap, "tel", "");
            //         driverName = MapUtils.getString(hashMap, "driverName", "");
            //     } else {
            //         String massage = "该司机信息在安徽宝钢数智物流系统中不存在或不为生效状态，请联系所属承运商或客户的管理员（调度员）进行维护！";
            //         eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            //         eiInfo.setMsg(massage);
            //         return eiInfo;
            //     }
            // }

            Map hashMap = new HashMap();
            if (StringUtils.isBlank(voucherNum1)){
                if ("10".equals(businessType)){
                    hashMap.put("typeOfHandling","20");
                }else if ("20".equals(businessType)){
                    hashMap.put("typeOfHandling","60");
                }else if ("30".equals(businessType)){
                    hashMap.put("typeOfHandling","40");
                }else if ("40".equals(businessType)){
                    hashMap.put("typeOfHandling","50");
                }else if ("50".equals(businessType)){
                    hashMap.put("typeOfHandling","70");
                }else if ("60".equals(businessType)){
                    hashMap.put("typeOfHandling","80");
                }
            }else {
                if ("10".equals(voucherNumHandType)){
                    hashMap.put("typeOfHandling","10");
                }else if ("30".equals(voucherNumHandType)) {
                    hashMap.put("typeOfHandling","30");
                }
            }
            hashMap.put("segNo",segNo);
            hashMap.put("vehicleNo",vehicleNo);
            hashMap.put("driverIdentity",driverIdentity);
            hashMap.put("reservationDate",format);//预约日期
            hashMap.put("status",20);
            hashMap.put("isReservation","10");
            //查询司机今天预约任务
            List<LIRL0201> lirl0201s = this.dao.query(LIRL0201.QUERY_REVERSION,hashMap);
            //查询超时或者提前的预约单
            hashMap.put("reservationDate","");//预约日期
            List<LIRL0201> lirl0201s1s = this.dao.query(LIRL0201.QUERY_OVER_TIME_REVERSION,hashMap);
            //查询厂内装卸点
            Map queryLirl0309 = new HashMap();
            queryLirl0309.put("segNo",segNo);
            queryLirl0309.put("businessType",businessType);
            queryLirl0309.put("status",30);
            queryLirl0309.put("delFlag",0);
            List<LIRL0309> lirl0309s = this.dao.query(LIRL0309.QUERY, queryLirl0309);

            String handType = "";

            if (StringUtils.isBlank(voucherNum)){
                if (lirl0309s.size()>0){
                    LIRL0309 lirl0309 = lirl0309s.get(0);
                    handType = lirl0309.getHandType();
                    if("30".equals(businessType)){
                        handType="40";
                    }
                }else {
                    String massage = "没匹配到有效的装卸点，请联系工作人员进行处理！";
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg(massage);
                    return eiInfo;
                }
            }else {
                if ("10".equals(voucherNumHandType)){
                    handType="10";
                    businessType="10";
                }else if ("30".equals(voucherNumHandType)) {
                    handType="30";
                    businessType="30";
                }
            }

            /*LIRL0309 lirl0309 = lirl0309s.get(0);*/
            List list = new ArrayList();
            boolean b = false;
            //需校验此车辆是否有当前时间段的预约，若无预约则给提示"
            // 此车辆无预约，不允许登记，登记信息已发送仓库待审核！"
            if (lirl0201s.size()>0){
                //如有登记时间中的预约单，可以直接带入。（业务+车牌号+司机+时间匹配）
                //根据扫描的提单号调开单和物流服务把信息落地到车辆登记提单信息表。
                //如提前或超时登记，匹配不到预约单，此时点击入厂登记需弹出窗口，将车辆和司机查询出的预约单集合，供客户选择
               for (LIRL0201 lirl0201:lirl0201s){
                   String reservationDate = lirl0201.getReservationDate();//预约日期
                   String reservationTime = lirl0201.getReservationTime();//预约时段
                   String reservationNumber = lirl0201.getReservationNumber();//预约单号
                   String substring = format.substring(0, 8);
                   String[] split = reservationTime.split("-");
                   String time1 = substring+split[0].replace(":", "");
                   String time2 = substring+split[1].replace(":", "");
                   Date startTime = dateFormat.parse(time1);
                   Date endTime = dateFormat.parse(time2);
                   Date targetTime = dateFormat.parse(format);
                   boolean timeBetween = DateUtils.isTimeBetween(startTime, endTime, targetTime);
                   if (timeBetween) {
                       b = true;
                       List<Map> lirl0201s1 = new ArrayList<>();
                       Map map = lirl0201.toMap();
                       map.put("numberTime",reservationNumber+" / "+reservationDate+" "+reservationTime);
                       lirl0201s1.add(map);
                       eiInfo.set("list",lirl0201s1);
                   }

               }
                if (!b){
                    List<Map> lirl0201s1 = new ArrayList<>();
                    for (LIRL0201 lirl0201:lirl0201s){
                        String reservationDate = lirl0201.getReservationDate();//预约日期
                        String reservationTime = lirl0201.getReservationTime();//预约时段
                        String reservationNumber = lirl0201.getReservationNumber();//预约单号
                        Map map = lirl0201.toMap();
                        map.put("numberTime",reservationNumber+" / "+reservationDate+" "+reservationTime);
                        lirl0201s1.add(map);
                    }
                    eiInfo.set("list",lirl0201s1);
                }
            }else if ((CollectionUtils.isEmpty(lirl0201s)&& CollectionUtils.isNotEmpty(lirl0201s1s))&&!"1".equals(isNoReservation)) {
                List<Map> lirl0201s1 = new ArrayList<>();
                for (LIRL0201 lirl0201:lirl0201s1s){
                    String reservationDate = lirl0201.getReservationDate();//预约日期
                    String reservationTime = lirl0201.getReservationTime();//预约时段
                    String reservationNumber = lirl0201.getReservationNumber();//预约单号
                    Map map = lirl0201.toMap();
                    map.put("numberTime",reservationNumber+" / "+reservationDate+" "+reservationTime);
                    lirl0201s1.add(map);
                }

                eiInfo.set("list",lirl0201s1);
                eiInfo.set("overFlag","1");
            }else if ((CollectionUtils.isEmpty(lirl0201s)&& CollectionUtils.isEmpty(lirl0201s1s))||"1".equals(isNoReservation)){
                //车辆无预约，重复进厂登记，删除上一条无预约单进厂登记！
                Map queryLIRL0302 = new HashMap();
                queryLIRL0302.put("segNo",segNo);
                queryLIRL0302.put("vehicleNo",vehicleNo);
                queryLIRL0302.put("status","10");
                queryLIRL0302.put("delFlag",0);
                List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY, queryLIRL0302);
                if (lirl0302s.size()>0){
                    for (LIRL0302 lirl0302:lirl0302s){
                        String status = lirl0302.getStatus();//进厂登记单状态
                        //删除上一条进厂登记信息
                        lirl0302.setStatus("00");
                        lirl0302.setRecRevisor(driverName);
                        lirl0302.setRecRevisorName(driverName);
                        lirl0302.setRecReviseTime(DateUtil.curDateTimeStr14());
                        lirl0302.setDelFlag(1);
                        lirl0302.setRemark("撤销进厂登记"+DateUtil.curDateTimeStr14());
                        lirl0302.setSysRemark("撤销进厂登记"+DateUtil.curDateTimeStr14());
                        lirl0302.setDelFlag(1);
                        dao.delete(LIRL0302.DELETE, lirl0302);
                    }
                }
                //车辆无预约，不允许登记，登记信息已发送仓库待审核！
                Map insertLirl0302 = new HashMap();
                insertLirl0302.put("segNo",segNo);
                insertLirl0302.put("unitCode",segNo);
                insertLirl0302.put("checkId","");//车辆登记流水号
                insertLirl0302.put("carTraceNo","");//车辆跟踪号
                insertLirl0302.put("status","10");//状态
                insertLirl0302.put("handType",handType);//装卸类型
                insertLirl0302.put("vehicleNo",vehicleNo);//车牌号
                insertLirl0302.put("idCard",driverIdentity);//身份证号
                insertLirl0302.put("driverName",driverName);//司机姓名
                insertLirl0302.put("telNum",driverTel);//手机号
                insertLirl0302.put("checkSource",10);//数据来源(10一体机)
                insertLirl0302.put("voucherNum",voucherNum1);//提单号
                insertLirl0302.put("businessType",businessType);//业务类型
                insertLirl0302.put("checkDate", DateUtil.curDateTimeStr14());//业务类型
                insertLirl0302.put("delFlag", 0);// 修改时间
                insertLirl0302.put("uuid", StrUtil.getUUID());// 修改时间
                insertLirl0302.put("archiveFlag", "0");
                insertLirl0302.put("tenantId", " ");
                insertLirl0302.put("factoryArea", factoryArea);
                insertLirl0302.put("factoryAreaName", factoryAreaName);
                String strSeqTypeId = "TLIRL_SEQ0201";
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                insertLirl0302.put("reservationNumber", uuid);
                RecordUtils.setCreator(insertLirl0302);
                // 创建人工号
                insertLirl0302.put("recCreator", driverName);
                // 创建人姓名
                insertLirl0302.put("recCreatorName", driverName);
                // 创建时间
                insertLirl0302.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                insertLirl0302.put("recRevisor", driverName);
                // 修改人姓名
                insertLirl0302.put("recRevisorName", driverName);
                // 修改时间
                insertLirl0302.put("recReviseTime", DateUtil.curDateTimeStr14());
                insertLirl0302.put("customerId", customerId);
                insertLirl0302.put("customerName", customerName);
                LIRL0302 lirl0302 = new LIRL0302();
                lirl0302.fromMap(insertLirl0302);
                dao.insert(LIRL0302.INSERT,lirl0302);
                String msg = "此车辆无预约，不允许登记，登记信息已发送仓库待审核！";
                //无预约单返回页面预约单号
                eiInfo.set("reservationNumber",uuid);
                if (StringUtils.isNotEmpty(fileUUID)){
                    //修改客户签收附件表把附件ID关联预约单号
                    Map updateLIRL0312 = new HashMap();
                    updateLIRL0312.put("segNo",segNo);
                    updateLIRL0312.put("fileId",fileUUID);
                    updateLIRL0312.put("relevanceType",uuid);
                    updateLIRL0312.put("relevanceId",uuid);
                    updateLIRL0312.put("recRevisor",driverName);
                    updateLIRL0312.put("recRevisorName",driverName);
                    updateLIRL0312.put("recReviseTime",DateUtil.curDateTimeStr14());
                    dao.update(LIRL0312.UPDATE_RELEVANCE_ID,updateLIRL0312);
                }
                String businessTypeName="";
                if (StringUtils.isBlank(voucherNum)){
                    if ("10".equals(businessType)){
                        businessTypeName="钢材卸货";
                    }else if ("20".equals(businessType)){
                        businessTypeName="废料提货";
                    }else if ("30".equals(businessType)){
                        businessTypeName="托盘运输";
                    }else if ("40".equals(businessType)){
                        businessTypeName="资材卸货";
                    }else if ("50".equals(businessType)){
                        businessTypeName="欧冶提货";
                    }else if ("60".equals(businessType)){
                        businessTypeName="其他物品运输";
                    }
                }else {
                    if ("10".equals(businessType)){
                        businessTypeName="钢材装货";
                    }else if ("30".equals(businessType)){
                        businessTypeName="钢材卸货+装货";
                    }
                }

                //发送短信通知仓管人员
                Map sendSms = new HashMap();
                sendSms.put("segNo",segNo);
                sendSms.put("sendType","10");//仓管人员
                sendSms.put("status","20");//启用
                sendSms.put("flag","20");//启用
                int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();//当前天是星期几
                sendSms.put("dayOfWeek",dayOfWeek);
                List<LIRL0505> query = this.dao.query(LIRL0505.QUERY, sendSms);
                if (CollectionUtils.isNotEmpty(query)){
                    for (LIRL0505 lirl0505:query){
                        //通知仓管人员进行提醒
                        //宋勇有待审核的预约单：车牌号、司机姓名、手机号、所属公司、业务类型，已做无预约进厂登记，请及时审核处理，谢谢！
                        String perTel = lirl0505.getPerTel();
                        String perName = lirl0505.getPerName();
//                        String content="";
//                        if (StringUtils.isBlank(customerName)){
//                            content = perName+"有待审核的预约单：车牌号："+vehicleNo+"、司机："+driverName+"、手机号："+driverTel+"、业务类型"+businessTypeName+",已做无预约进厂登记，请及时审核处理，谢谢！";
//                        }else {
//                            content = perName+"有待审核的预约单：车牌号："+vehicleNo+"、司机："+driverName+"、手机号："+driverTel+"、所属公司："+customerName+"、业务类型"+businessTypeName+",已做无预约进厂登记，请及时审核处理，谢谢！";
//                        }
//                        EiInfo outInfo = new EiInfo();
//                        outInfo.set("content",content);
//                        outInfo.set("mobileNum",perTel);
//                        EiInfo vzbmInfo = SmsSendManager.sendMobile(outInfo);
//                        if (vzbmInfo.getStatus() == EiConstant.STATUS_FAILURE) {
//                            //打印日志到elk
//                            log(  "进厂登记未审核：短信接口传入参数：" + vzbmInfo + "\n" + "进厂登记未审核 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            //输出到应用日志
//                            System.out.println("进厂登记未审核：短信接口传入参数：" + vzbmInfo + "\n" + "进厂登记未审核 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            throw new RuntimeException(vzbmInfo.toJSONString());
//                        } else {
//                            //打印日志到elk
//                            log( "进厂登记未审核：短信接口传入参数：" + vzbmInfo + "\n" + "进厂登记未审核 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                            //输出到应用日志
//                            System.out.println("进厂登记未审核：短信接口传入参数：" + vzbmInfo + "\n" + "进厂登记未审核 ：短信接口返回的参数：" + vzbmInfo.toJSONString());
//                        }
                        HashMap<String, Object> messageMap = new HashMap<>();
                        messageMap.put("param1",perTel);
                        messageMap.put("param2",perName);
                        messageMap.put("param3",vehicleNo);
                        messageMap.put("param4",driverName);
                        messageMap.put("param5",driverTel);
                        messageMap.put("param6",customerName);
                        messageMap.put("param7",businessTypeName);
                        MessageUtils.sendMessage(messageMap,"MT0000001010");
                    }
                }
                eiInfo.setMsg(msg);
            }
            // 返回成功状态和消息
            eiInfo.setStatus(EiConstant.STATUS_DEFAULT);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return eiInfo;
    }


    /***
     * 车辆进厂登记新增
     * segNo 账套
     * 存储过程：prog.wl_logistics_plan_mgr.wl_t_i_vehicle_check_insert
     * S_LI_RL_0058
     */
    public synchronized EiInfo entryRegistration(EiInfo eiInfo) {
        try {
            String stringDate = DateUtil.curDateTimeStr14();//进场登记时间
            String segNo = (String) eiInfo.get("segNo");//账套
            String reservationNumber = (String) eiInfo.get("reservationNumber");//预约单号
            List<String> voucherNumList = (List<String>) eiInfo.get("voucherNumList");//提单号
            String voucherNum = " ";
            if (CollectionUtils.isNotEmpty(voucherNumList)){
                voucherNum =  String.join(",", voucherNumList);
            }
            String idCard = (String) eiInfo.get("idCard");//身份证号
            String checkSource = (String) eiInfo.get("checkSource");//数据来源
            //String handType = (String) eiInfo.get("handType");//装卸类型(10 装 20卸 30装卸)
            String factoryArea = (String) eiInfo.get("factoryArea");//厂区
            String factoryAreaName = (String) eiInfo.get("factoryAreaName");//厂区名称
            String vehicleNo = (String) eiInfo.get("vehicleNo");//车牌号
            String businessType = (String) eiInfo.get("businessType");//业务类型
            String fileUUID = (String) eiInfo.get("fileUUID");//文件ID
            String pageFlag = (String) eiInfo.get("pageFlag");//页面标记
            String customerId = (String) eiInfo.get("customerId");//客户代码
            String customerName = (String) eiInfo.get("customerName");//客户名称
            String preFlag = "0";//预排序标记
            Date date = new Date(System.currentTimeMillis());
            String[] args = {segNo.substring(0, 2), date.toString(), ""};

            //查询司机信息
            String driverName = "";
            String telNum = "";
            //车辆进场登记数据
            LIRL0302 lirl0302 = new LIRL0302();
            String checkId = "";//车辆登记流水号

            Map queryLIRL0102 = new HashMap();
            queryLIRL0102.put("segNo", segNo);
            queryLIRL0102.put("status", "20");
            queryLIRL0102.put("driverIdentity", idCard);
            List<HashMap> listLIRL0102 = this.dao.query(LIRL0102.QUERY, queryLIRL0102);
            if (listLIRL0102.size() > 0) {
                HashMap hashMap = listLIRL0102.get(0);
                driverName = MapUtils.getString(hashMap, "driverName", "");
                telNum = MapUtils.getString(hashMap, "tel", "");
            }


            //查询车辆是否审核
            Map queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("vehicleNo", vehicleNo);
            queryMap.put("status", 20);
            if ("10".equals(businessType)||StringUtils.isBlank(businessType)) {
                int count = super.count("LIRL0103.count", queryMap);
                if (count < 1) {
                    String massage = "车牌号:" + vehicleNo + "该车辆未进行审核，请检查！";
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg(massage);
                    return eiInfo;
                }
            }

            //查询预约维护表
            queryMap = new HashMap<>();
            queryMap.put("segNo", segNo);
            queryMap.put("reservationNumber", reservationNumber);
            queryMap.put("notStatus", "00");
            queryMap.put("delFlag", 0);
            List<LIRL0201> lirl0201s = this.dao.query(LIRL0201.QUERY, queryMap);
            if (lirl0201s.size()<1){
                String massage = "预约单号:" + reservationNumber + "未查到有效数据！";
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg(massage);
                return eiInfo;
            }

            LIRL0201 lirl0201 = lirl0201s.get(0);
            String status = lirl0201.getStatus();
            if ("99".equals(status)){
                String massage = "预约单号:" + reservationNumber + "已完成，不允许登记！";
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg(massage);
                return eiInfo;
            }
            String typeOfHandling = lirl0201.getTypeOfHandling();//装卸业务(装货、卸货、卸货+装货、周转架、资材卸货、废料提货)
            String reservationDate = lirl0201.getReservationDate();//预约日期
            String reservationTime = lirl0201.getReservationTime();//预约时段
            customerId = lirl0201.getCustomerId();
            customerName = lirl0201.getCustomerName();
            //判断迟到还是早到
            String[] split = reservationTime.split("-");
            String startReservationTime = reservationDate + split[0].replace(":", "") + "00";
            String endReservationTime = reservationDate + split[1].replace(":", "") + "00";
            BigDecimal stringDateBigDecimal = new BigDecimal(stringDate);
            BigDecimal startTimeBigDecimal = new BigDecimal(startReservationTime);
            BigDecimal endTimeBigDecimal = new BigDecimal(endReservationTime);
            //控制针对于不是今天或者之前的预约单不允许登记
            String curredDateTimeStr14 = DateUtil.curDateTimeStr14();
            String endTime= curredDateTimeStr14.substring(0,curredDateTimeStr14.length()-6).concat("235959");
            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            // 将字符串转换为 LocalDateTime
            LocalDateTime appointmentTime = LocalDateTime.parse(startReservationTime, formatter); //预约时间
            LocalDateTime endTime1 = LocalDateTime.parse(endTime, formatter); //预约时间
            // 比较预约时间是否不大于当晚截止时间
            if (appointmentTime.isAfter(endTime1)) {
                String massage = "您的预约单在今日之后，不可登记，请于预约单日期进行登记！";
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg(massage);
                return eiInfo;
            }
            if ("40".equals(typeOfHandling)) {
                typeOfHandling = "40";
            } else if ("50".equals(typeOfHandling)) {
                typeOfHandling = "20";
            } else if ("60".equals(typeOfHandling)) {
                typeOfHandling = "10";
            } else if ("30".equals(typeOfHandling)) {
                typeOfHandling = "30";
            }else if ("70".equals(typeOfHandling)){
                typeOfHandling = "10";
            }else if ("80".equals(typeOfHandling)){
                typeOfHandling = "20";
            }

            if (StringUtils.isNotBlank(voucherNum)){
                if ("10".equals(typeOfHandling)) {
                    businessType = "10";
                } else if ("30".equals(typeOfHandling)) {
                    businessType = "30";
                }
            }

            if ("1".equals(pageFlag)){
                if ("20".equals(businessType)){
                    businessType="10";
                }else if ("60".equals(businessType)){
                    businessType="20";
                }else if ("40".equals(businessType)){
                    businessType="30";
                }else if ("50".equals(businessType)){
                    businessType="40";
                }else if ("70".equals(businessType)) {
                    businessType="50";
                } else if ("80".equals(businessType)) {
                    businessType="60";
                }
            }
//            //校验同一个预约单，同一时间段是否已经存在
//            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//            objectObjectHashMap.put("segNo", segNo);
//            objectObjectHashMap.put("reservationNumber", reservationNumber);
//            objectObjectHashMap.put("status", "10");
//            List<LIRL0301> queryLIRL0301 = this.dao.query(LIRL0301.QUERY, objectObjectHashMap);
//            if (CollectionUtils.isNotEmpty(queryLIRL0301)){
//                //
//            }
            //判断数据来源是否是车辆进厂登记审批数据
            Map queryLIRL0302 = new HashMap();
            queryLIRL0302.put("segNo", segNo);
            queryLIRL0302.put("reservationNumber", reservationNumber);
            queryLIRL0302.put("carTraceNoIsNull", 1);
            queryLIRL0302.put("status", "20");
            queryLIRL0302.put("delFlag", 0);
            List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY, queryLIRL0302);
            String strSeqTypeId = "TLIRL_SEQ0301";//车辆跟踪流水号
            String carTraceNo = SequenceGenerator.getNextSequence(strSeqTypeId, args);
            //插入车辆进厂登记表
            strSeqTypeId = "TLIRL_SEQ0302";//车辆登记流水号
            checkId = SequenceGenerator.getNextSequence(strSeqTypeId, args);
            if (lirl0302s.size() > 0) {
                lirl0302 = lirl0302s.get(0);
                lirl0302.setCheckId(checkId);
                lirl0302.setCarTraceNo(carTraceNo);
                lirl0302.setCheckSource("10");
                //如果指定的数与参数相等返回 0。
                //如果指定的数小于参数返回 -1。
                //如果指定的数大于参数返回 1。
                int i = stringDateBigDecimal.compareTo(startTimeBigDecimal);
                int i2 = stringDateBigDecimal.compareTo(endTimeBigDecimal);
                if (i >= 0 && i2 <= 0) {
                    //登记时间在预约时段内 正常
                    lirl0302.setLateEarlyFlag("30");
                } else if (i < 0) {
                    //当前时间大于预约时间是早到
                    lirl0302.setLateEarlyFlag("20");
                } else {
                    //其他是迟到
                    lirl0302.setLateEarlyFlag("10");
                }

                dao.update(LIRL0302.UPDATE, lirl0302);
            } else {
                Map insertLIRL0302 = new HashMap();
                int i = stringDateBigDecimal.compareTo(startTimeBigDecimal);
                int i2 = stringDateBigDecimal.compareTo(endTimeBigDecimal);
                if (i >= 0 && i2 <= 0) {
                    //登记时间在预约时段内 正常
                    insertLIRL0302.put("lateEarlyFlag", "30");
                } else if (i < 0) {
                    //当前时间大于预约时间是早到
                    insertLIRL0302.put("lateEarlyFlag", "20");
                } else {
                    //其他是迟到
                    insertLIRL0302.put("lateEarlyFlag", "10");
                }
                insertLIRL0302.put("segNo", segNo);
                insertLIRL0302.put("unitCode", segNo);
                insertLIRL0302.put("checkId", checkId);
                insertLIRL0302.put("carTraceNo", carTraceNo);
                insertLIRL0302.put("status", 20);
                insertLIRL0302.put("handType", typeOfHandling);
                insertLIRL0302.put("vehicleNo", vehicleNo);
                insertLIRL0302.put("idCard", idCard);
                insertLIRL0302.put("driverName", driverName);
                insertLIRL0302.put("telNum", telNum);
                insertLIRL0302.put("reservationNumber", reservationNumber);
                insertLIRL0302.put("checkSource", checkSource);/* 数据来源(10一体机)*/
                insertLIRL0302.put("voucherNum", voucherNum);
                insertLIRL0302.put("businessType", businessType);
                insertLIRL0302.put("factoryArea", factoryArea);
                insertLIRL0302.put("factoryAreaName", factoryAreaName);
                insertLIRL0302.put("reservationTime", reservationTime);
                insertLIRL0302.put("reservationDate", reservationDate);
                insertLIRL0302.put("checkDate", stringDate);
                insertLIRL0302.put("delFlag", 0);// 记录删除标记
                insertLIRL0302.put("uuid", StrUtil.getUUID());// 记录删除标记
                // 创建人工号
                insertLIRL0302.put("recCreator", driverName);
                // 创建人姓名
                insertLIRL0302.put("recCreatorName", driverName);
                // 创建时间
                insertLIRL0302.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                insertLIRL0302.put("recRevisor", driverName);
                // 修改人姓名
                insertLIRL0302.put("recRevisorName", driverName);
                // 修改时间
                insertLIRL0302.put("recReviseTime", DateUtil.curDateTimeStr14());
                insertLIRL0302.put("archiveFlag", "0");
                insertLIRL0302.put("tenantId", " ");
                insertLIRL0302.put("customerId", customerId);
                insertLIRL0302.put("customerName", customerName);
                //RecordUtils.setCreator(insertLIRL0302);
                lirl0302 = new LIRL0302();
                lirl0302.fromMap(insertLIRL0302);
                dao.insert(LIRL0302.INSERT, lirl0302);
                if (StringUtils.isNotEmpty(fileUUID)){
                    //修改客户签收附件表把附件ID关联预约单号
                    Map updateLIRL0312 = new HashMap();
                    updateLIRL0312.put("segNo",segNo);
                    updateLIRL0312.put("fileId",fileUUID);
                    updateLIRL0312.put("relevanceType",reservationNumber);
                    updateLIRL0312.put("relevanceId",reservationNumber);
                    updateLIRL0312.put("recRevisor",driverName);
                    updateLIRL0312.put("recRevisorName",driverName);
                    updateLIRL0312.put("recReviseTime",DateUtil.curDateTimeStr14());
                    dao.update(LIRL0312.UPDATE_RELEVANCE_ID,updateLIRL0312);
                }
            }



            //先调用检查重复登记的过程
            EiInfo eiInfo2 = checkWlVehicleCheckin(segNo, checkId,driverName);
            if (eiInfo2.getStatus() < 0) {
                return eiInfo2;
            }


            //插入车辆跟踪表
            insertLIRL0301(stringDate, segNo, carTraceNo, lirl0201, factoryArea, factoryAreaName, typeOfHandling);

            //如果装卸类型 lv_hand_type='10' and lv_gqg_pre_flag='1' 或者 lv_hand_type='20' 或者 lv_hand_type='30'（装卸业务）
            //取顺序号：根据账套查询车辆预排序主表的数量（count）+1，把最新的顺序号（（count）+1）插入预排序主表
            /*if (("10".equals(typeOfHandling)&&"1".equals(preFlag))||("20".equals(typeOfHandling))||("30".equals(typeOfHandling))){
                //根据账套查询车辆预排序主表的数量（count）+1

                //优先级 有配车单90  无配车单99
                int lv_priority_level = 99;//优先级
                String lv_target_type  = "1"; //无固定目标
                String lv_target_hand_point_id = ""; //目标装卸点为空
                //判断是否可以进预排序
                //如果是无提单  根据装卸业务确定装卸点
            }*/

            //车辆登记向预排序表插数据
            eiInfo2 = addPreVehicleQuene(segNo, checkId, telNum, driverName, businessType,pageFlag);
            if (eiInfo2.getStatus() < 0) {
                return eiInfo2;
            }
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.setMsg("车辆进厂登记新增成功!");
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return eiInfo;
    }

    private void insertLIRL0301(String stringDate, String segNo, String carTraceNo, LIRL0201 lirl0201, String factoryArea,String factoryAreaName, String typeOfHandling) {


        Map insertLIRL0301 = new HashMap();
        insertLIRL0301.put("segNo", segNo);
        insertLIRL0301.put("unitCode", segNo);
        insertLIRL0301.put("carTraceNo",carTraceNo);
        insertLIRL0301.put("status",10);
        insertLIRL0301.put("handType", typeOfHandling);
        insertLIRL0301.put("vehicleNo", lirl0201.getVehicleNo());
        insertLIRL0301.put("idCard", lirl0201.getDriverIdentity());
        insertLIRL0301.put("driverName", lirl0201.getDriverName());
        insertLIRL0301.put("telNum", lirl0201.getDriverTel());
        insertLIRL0301.put("reservationNumber", lirl0201.getReservationNumber());
        insertLIRL0301.put("factoryArea", factoryArea);
        insertLIRL0301.put("factoryAreaName", factoryAreaName);
        insertLIRL0301.put("checkDate", stringDate);
        insertLIRL0301.put("uuid", StrUtil.getUUID());// uuid
        insertLIRL0301.put("recCreator", lirl0201.getDriverTel());//
        insertLIRL0301.put("recCreatorName", lirl0201.getDriverName());//
        insertLIRL0301.put("recCreateTime", DateUtil.curDateTimeStr14());//
        insertLIRL0301.put("recRevisor", lirl0201.getDriverTel());//
        insertLIRL0301.put("recRevisorName", lirl0201.getDriverName());//
        insertLIRL0301.put("recReviseTime", DateUtil.curDateTimeStr14());//
        insertLIRL0301.put("delFlag", "0");
        insertLIRL0301.put("archiveFlag", "0");
        insertLIRL0301.put("tenantId", " ");
        LIRL0301 lirl0301 = new LIRL0301();
        lirl0301.fromMap(insertLIRL0301);
        dao.insert(LIRL0301.INSERT,lirl0301);
    }


    /**
     * 提单打印
     *
     * 根据身份证信息查找提单信息
     * @param eiInfo
     * 	S_LI_RL_0061
     * @return
     */
    public EiInfo queryVoucherInfo(EiInfo eiInfo){
        try {

            String segNo = (String) eiInfo.get("segNo");//账套
            String idCard = (String) eiInfo.get("idCard"); //身份信息
            String driverName = (String) eiInfo.get("driverName"); //司机姓名
            String ladingBillId = (String) eiInfo.get("voucherNum"); //提单号
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("driverId", idCard);
            hashMap.put("driver", driverName);
            if (StringUtils.isBlank(ladingBillId)){
                hashMap.put("ladingBillStatus","50");
            }
            hashMap.put("ladingBillIdEq", ladingBillId);

            //先查询提单是否存在，不存在调服务写入车辆登记提单信息表
            String xplatToken = TokenUtils.getXplatToken();
            eiInfo.set(EiConstant.serviceId, "S_UV_SL_9020");//查询提单服务
            eiInfo.set("main", hashMap);
            EiInfo outInfo = EServiceManager.call(eiInfo,xplatToken);
            if (outInfo.getStatus() == -1) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("开单中心返回报错:" + outInfo.getMsg());
                throw new RuntimeException(eiInfo.getMsg());
            }
            List<HashMap> listAdd = new ArrayList<>();
            List<HashMap> list = (List<HashMap>) outInfo.get("result");
            if (CollectionUtils.isNotEmpty(list)) {
                String ladingBillStatus = (String) list.get(0).get("ladingBillStatus");
                if ("10".equals(ladingBillStatus)) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单新增未生效！");
                } else if ("00".equals(ladingBillStatus)) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单已被撤销！");
                } else if ("90".equals(ladingBillStatus)) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单已提货完成！");
                } else if ("20".equals(ladingBillStatus)) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单已提交未生效！");
                } else if ("99".equals(ladingBillStatus)) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单已强制完成！");
                }
            }

                //调IMC服务拿到捆包信息
                EiInfo eiInfo1 = new EiInfo();
                eiInfo1.set("segNo", segNo);
                eiInfo1.set("driverId", idCard);
                if (StringUtils.isBlank(ladingBillId)){
                    eiInfo1.set("transBillType","10");
                }
                eiInfo1.set("transBillId", ladingBillId);
                eiInfo1.set("driverName", driverName);
                eiInfo1.set(EiConstant.serviceId, "S_UC_EP_0044");
                //调post请求
                outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                // if (outInfo.getStatus() == -1) {
                //     outInfo.setStatus(EiConstant.STATUS_FAILURE);
                //     outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                //     throw new RuntimeException(outInfo.getMsg());
                // }
                List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
                if (CollectionUtils.isEmpty(rusult)&&CollectionUtils.isEmpty(list)) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("未查询到提货委托的提单或转库单!");
                    return outInfo;
                } else if (CollectionUtils.isNotEmpty(rusult)&&StringUtils.isNotBlank(ladingBillId)){
                    String ladingBillStatus = MapUtils.getString(rusult.get(0), "transBillType", "");//开单方式 10-按捆包 20-按重量 30-按件数
                    if ("00".equals(ladingBillStatus)) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("该转库单未生成计划！");
                        return outInfo;
                    } else if ("20".equals(ladingBillStatus)) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("该转库单已出库！");
                        return outInfo;
                    } else if ("30".equals(ladingBillStatus)) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("该转库单已完成！");
                        return outInfo;
                    } else if ("90".equals(ladingBillStatus)) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("该转库单已撤销！");
                        return outInfo;
                    }
                }
                if (CollectionUtils.isNotEmpty(rusult)){
                    for (HashMap map : rusult) {
                        map.put("ladingBillId", MapUtils.getString(map,"transBillId"));
                        map.put("billingTime", MapUtils.getString(map,"recCreateTime"));
                        map.put("userName", MapUtils.getString(map,"packSettleUserName"));
                        String totalQty = MapUtils.getString(map, "sumNtotalQty");
                        if ("0E-8".equals(totalQty)){
                            map.put("totalPackQty", "0.000000");
                        }else {
                            map.put("totalPackQty", totalQty);
                        }
                        String planNetWeight = MapUtils.getString(map, "sumNetWeight");
                        if ("0E-8".equals(planNetWeight)){
                            map.put("totalWeight", "0.000000");
                        }else {
                            map.put("totalWeight", planNetWeight);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(list)&&CollectionUtils.isEmpty(rusult)){
                    eiInfo.set("list",list);
                }else if (CollectionUtils.isEmpty(list)&&CollectionUtils.isNotEmpty(rusult)){
                    eiInfo.set("list",rusult);
                }else if (CollectionUtils.isNotEmpty(list)&&CollectionUtils.isNotEmpty(rusult)){
                    List<HashMap> mergedList = new ArrayList<>(list);
                    mergedList.addAll(rusult);
                    eiInfo.set("list",mergedList);
                }else {
                    eiInfo.set("list",listAdd);
                }

        } catch (RuntimeException e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    // /***
    //  * 提单打印
    //  * @ServiceId：	S_LI_RL_0064
    //  */
    // public EiInfo ladingBillIdPrint(EiInfo eiInfo) throws JsonProcessingException {
    //     EiInfo outInfo = new EiInfo();
    //     List<HashMap> result = (List<HashMap>)eiInfo.get("result");
    //     if (CollectionUtils.isEmpty(result)){
    //         eiInfo.setStatus(EiConstant.STATUS_FAILURE);
    //         eiInfo.setMsg("至少勾选一条数据进行打印!");
    //         return eiInfo;
    //     }
    //     ObjectMapper objectMapper = new ObjectMapper();
    //     System.out.println(objectMapper.writeValueAsString(result));
    //     eiInfo.set("result", result);
    //     eiInfo.set( "printKey","servicesPrint");
    //     eiInfo.set(EiConstant.serviceId,"S_UV_SL_9085"); //提单打印服务
    //     try {
    //         outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());
    //         if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
    //             outInfo.setStatus(EiConstant.STATUS_FAILURE);
    //             return outInfo;
    //         }
    //         //判断自动打印开关是否开启
    //         String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(result.get(0).get("segNo").toString(), "IF_REMOTE_PRINT", dao);
    //         if ("1".equals(ifRemotePrint)){
    //             ArrayList<String> urles = (ArrayList<String>) outInfo.get("urles");
    //             for (String url:urles){
    //                     uploadPrintFile(url, "/SSSLR003" + ".pdf",
    //                             result.get(0).get("segNo").toString(), "URLTD");
    //             }
    //         }else {
    //             eiInfo.set("docUrl",outInfo.get("urles"));
    //         }
    //         eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
    //     } catch (Exception e) {
    //         eiInfo.setStatus(EiConstant.STATUS_FAILURE);
    //         eiInfo.setMsg(e.getMessage());
    //         return eiInfo;
    //     }
    //     return eiInfo;
    // }



    /***
     * 提单打印
     * @ServiceId：	S_LI_RL_0064
     */
    public EiInfo ladingBillIdPrint(EiInfo eiInfo) throws JsonProcessingException {
        EiInfo outInfo = new EiInfo();
        List<HashMap> result = (List<HashMap>)eiInfo.get("result");
        if (CollectionUtils.isEmpty(result)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("至少勾选一条数据进行打印!");
            return eiInfo;
        }
        String segNo = (String) result.get(0).get("segNo");
        //提单
        List<HashMap> list = new ArrayList<>();
        //转库单
        List<String> ladingBillIdList = new ArrayList<>();
        for (HashMap hashMap : result) {
            String ladingBillId = (String) hashMap.get("ladingBillId");
            if (ladingBillId.startsWith("ZK")){
                ladingBillIdList.add(ladingBillId);
            }else {
                list.add(hashMap);
            }
        }

        EiInfo out=new EiInfo();
        List<String> url1=new ArrayList<>();
        ArrayList<String> urles= new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(ladingBillIdList)&&CollectionUtils.isNotEmpty(list)){
                // 转库单

                EiInfo eiInfo1 = new EiInfo();
                eiInfo1.set("segNo",segNo);
                eiInfo1.set("ladingBillIdList",ladingBillIdList);
                eiInfo1.set(EiConstant.serviceId,"S_UC_PR_0421");
                out = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                if (out.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg(out.getMsg());
                    return outInfo;
                }
                url1 =(List<String>) out.get("docUrl");


                eiInfo.set("result", list);
                eiInfo.set( "printKey","servicesPrint");
                eiInfo.set(EiConstant.serviceId,"S_UV_SL_9085"); //提单打印服务
                outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());

                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return outInfo;
                }
                urles = (ArrayList<String>) outInfo.get("urles");
                urles.addAll(url1);
                //判断自动打印开关是否开启
                String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(result.get(0).get("segNo").toString(), "IF_REMOTE_PRINT", dao);
                if ("1".equals(ifRemotePrint)){
                    for (String url:urles){
                        uploadPrintFile(url, "/SSSLR003" + ".pdf",
                                result.get(0).get("segNo").toString(), "URLTD","","");
                    }
                }else {
                    eiInfo.set("docUrl",urles);
                }
            }else if (CollectionUtils.isNotEmpty(list)){
                eiInfo.set("result", list);
                eiInfo.set( "printKey","servicesPrint");
                eiInfo.set(EiConstant.serviceId,"S_UV_SL_9085"); //提单打印服务
                outInfo = EServiceManager.call(eiInfo, TokenUtils.getXplatToken());

                if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return outInfo;
                }
                urles = (ArrayList<String>) outInfo.get("urles");
                //判断自动打印开关是否开启
                String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(result.get(0).get("segNo").toString(), "IF_REMOTE_PRINT", dao);
                if ("1".equals(ifRemotePrint)){
                    for (String url:urles){
                        uploadPrintFile(url, "/SSSLR003" + ".pdf",
                                result.get(0).get("segNo").toString(), "URLTD","","");
                    }
                }else {
                    eiInfo.set("docUrl",urles);
                }
            }else if (CollectionUtils.isNotEmpty(ladingBillIdList)){
                EiInfo eiInfo1 = new EiInfo();
                eiInfo1.set("segNo",segNo);
                eiInfo1.set("ladingBillIdList",ladingBillIdList);
                eiInfo1.set(EiConstant.serviceId,"S_UC_PR_0421");
                out = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                if (out.getStatus() == EiConstant.STATUS_FAILURE) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg(out.getMsg());
                    return outInfo;
                }
                url1 =(List<String>) out.get("docUrl");

                //判断自动打印开关是否开启
                String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(result.get(0).get("segNo").toString(), "IF_REMOTE_PRINT", dao);
                if ("1".equals(ifRemotePrint)){
                    for (String url:url1){
                        uploadPrintFile(url, "/SSSLR003" + ".pdf",
                                result.get(0).get("segNo").toString(), "URLTD","","");
                    }
                }else {
                    eiInfo.set("docUrl",url1);
                }
            }
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }


    /***
     * 车辆审核补登记数据
     * 存储过程：prog.wl_vehicle_quene_mgr.add_pre_vehicle_quene_byaudit
     * @param in_seg_no 系统账套号
     * @param in_vehicle_id  车牌号
     * @param in_modi_person  修改人
     * @return
     */
    private EiInfo addPreVehicleQueneByaudit(String in_seg_no, String in_vehicle_id, String in_modi_person,String recCreatorName,String in_business_type) {
        EiInfo eiInfo = new EiInfo();
        Map queryLIRL0103 = new HashMap();
        queryLIRL0103.put("segNo",in_seg_no);
        queryLIRL0103.put("vehicleNo",in_vehicle_id);
        queryLIRL0103.put("status","20");
        int count = super.count(LIRL0103.COUNT, queryLIRL0103);
        if (count == 0){
            String massage = "ok";
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
            eiInfo.setMsg(massage);
            return eiInfo;
        }
        //检查此车辆是否有登记数据，如果有，补预排序数据
        eiInfo = addPreVehicleQuene(in_seg_no, in_vehicle_id, in_modi_person, recCreatorName,in_business_type,null);
        if (eiInfo.getStatus()<0){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * 车辆登记向预排序表插数据
     * 存储过程：prog.wl_vehicle_quene_mgr.add_pre_vehicle_quene
     * @param in_seg_no 系统账套号
     * @param in_check_id  登记流水号
     * @param in_modi_person  修改人
     * @return
     */
    private EiInfo addPreVehicleQuene(String in_seg_no, String in_check_id, String in_modi_person, String recCreatorName, String in_business_type,String pageFlag) {
        String preFlag = "0";//预排序标记
        //装货时间累加
        int preLoadTime = 0;
        String stringDate = DateUtil.curDateTimeStr14();//当前时间
        EiInfo outInfo = new EiInfo();
        List<String> handPointId=new ArrayList<>();

        //先调用检查重复登记的过程
        /*EiInfo eiInfo = checkWlVehicleCheckin(in_seg_no, in_check_id);
        if (eiInfo.getStatus()<0){
            return eiInfo;
        }*/
        String lv_xs_advice_flag = "0"; //形式提单物流计划可进预排序标记
        String lv_gqg_pre_flag = "0"; //高强钢可以进预排序的标记

        // 查询进厂登记表获取信息

        Map queryLIRL0302 = new HashMap();
        queryLIRL0302.put("segNo", in_seg_no);
        queryLIRL0302.put("status", 20);
        queryLIRL0302.put("checkId", in_check_id);
        List<LIRL0302> lirl0302s = this.dao.query(LIRL0302.QUERY, queryLIRL0302);
        if (lirl0302s.size() > 0) {
            LIRL0302 lirl0302 = lirl0302s.get(0);
            String vehicleNo = lirl0302.getVehicleNo();//车牌号
            String idCard = lirl0302.getIdCard();//身份证
            String reservationNumber = lirl0302.getReservationNumber();//车辆预约单号
            String carTraceNo = lirl0302.getCarTraceNo();//车辆跟踪号
            String factoryArea = lirl0302.getFactoryArea();//厂区
            String handType = lirl0302.getHandType();//装卸类型(10 装 20卸 30装卸)
            String voucherNum = lirl0302.getVoucherNum();//提单号


            //如果是无提单  根据装卸业务确定装卸点 如果存在-- 预排序标记pre_flag=1
            //有提单的需要判断是普通提单还是形式提单
            String ladingSpotId = "";//始发站(仓库)代码
            String ladingSpotName = "";//始发站(仓库)代码名称
            String destSpotId = "";//终到站(仓库)代码
            String consigneeType = "";//提货委托类型
            String businessBillType = "";//业务单据类型
            String ladingBillType = "";//提单类型
            String billingMethod = "";//开单方式 10-按捆包 20-按重量 30-按件数
            String packId = "";//捆包号
            String matInnerId = "";//材料管理号
            String targetHandPointId = "";//目标装卸点
            //装卸点
            String handPointIdN = "";
            String locationId = "";//库位代码
            List<String> targetHandPointIdList = new ArrayList<>();
            List<String> handTypeList = new ArrayList<>();
            //组装预计装货时间参数
            List<HashMap> preLoadTimeList = new ArrayList<>();
            String priorityLevel = "99"; //优先级
            //判断是否有提单
            if (StringUtils.isBlank(voucherNum)) {
                //查询装卸点对照表
                HashMap queryMap = new HashMap<>();
                queryMap.put("segNo", in_seg_no);
                queryMap.put("businessType", in_business_type);
                queryMap.put("factoryArea", factoryArea);
                queryMap.put("status", 30);
                queryMap.put("delFlag", 0);
                queryMap.put("statusFlag", 10);
                if ("10".equals(in_business_type)){
                    Map queryLIRL0305 = new HashMap<>();
                    queryLIRL0305.put("segNo", in_seg_no);
                    queryLIRL0305.put("carTraceNo", carTraceNo);
                    queryLIRL0305.put("vehicleId", vehicleNo);
                    //钢材卸货根据站点分流，匹配不到还是原来的逻辑
                    //先根据起始地分流，起始地不存在查询所有的能进的卸货点
                    List<HashMap> queryHashMap = this.dao.query(LIRL0301.QUERY_SITE_NAME, queryLIRL0305);
                    if (CollectionUtils.isNotEmpty(queryHashMap)){
                        String startOfTransport = MapUtils.getString(queryHashMap.get(0), "startOfTransport");
                        if (StringUtils.isNotBlank(startOfTransport)) {
                            HashMap<String, String> objectObjectHashMap = new HashMap<>();
//                            objectObjectHashMap.put("siteNameLike", startOfTransport);
                            objectObjectHashMap.put("segNo", in_seg_no);
                            objectObjectHashMap.put("status", "30");
                            List<LIRL0315> queryLIRL0315 = this.dao.query(LIRL0315.QUERY, objectObjectHashMap);
                            if (CollectionUtils.isNotEmpty(queryLIRL0315)){
                                for (LIRL0315 lirl0315 : queryLIRL0315) {
                                    String siteName = lirl0315.getSiteName();
                                    String handPointId1 = lirl0315.getHandPointId();
                                    if (startOfTransport.contains(siteName)){
                                        targetHandPointIdList.add(handPointId1);
                                    }
                                }
                                if (CollectionUtils.isEmpty(targetHandPointIdList)) {

                                    List<LIRL0309> lirl0309s = this.dao.query(LIRL0309.QUERY, queryMap);
                                    if (lirl0309s.size() > 0) {
                                        //查询对应装卸点是否存在
                                        List<String> stringList = new ArrayList<>();
                                        for (LIRL0309 lirl0309 : lirl0309s) {
                                            stringList.add(lirl0309.getHandPointId());
                                            targetHandPointId = lirl0309.getHandPointId();
                                            String handTypeTwo = lirl0309.getHandType();
                                            targetHandPointIdList.add(targetHandPointId);
                                            handTypeList.add(handTypeTwo);
                                        }
                                        queryMap.put("status", 30);
                                        queryMap.put("handPointIdList", stringList);
                                        List<LIRL0304> lirl0304s = this.dao.query(LIRL0304.QUERY, queryMap);
                                        if (lirl0304s.size() > 0) {
                                            preFlag = "1";
                                        }
                                    } else {
                                        String massage = "没匹配到有效的装卸点，请联系工作人员进行处理！";
                                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        outInfo.setMsg(massage);
                                        return outInfo;
                                    }
                                }
                            }else {
                                List<LIRL0309> lirl0309s = this.dao.query(LIRL0309.QUERY, queryMap);
                                if (lirl0309s.size() > 0) {
                                    //查询对应装卸点是否存在
                                    List<String> stringList = new ArrayList<>();
                                    for (LIRL0309 lirl0309 : lirl0309s) {
                                        stringList.add(lirl0309.getHandPointId());
                                        targetHandPointId = lirl0309.getHandPointId();
                                        String handTypeTwo = lirl0309.getHandType();
                                        targetHandPointIdList.add(targetHandPointId);
                                        handTypeList.add(handTypeTwo);
                                    }
                                    queryMap.put("status", 30);
                                    queryMap.put("handPointIdList", stringList);
                                    List<LIRL0304> lirl0304s = this.dao.query(LIRL0304.QUERY, queryMap);
                                    if (lirl0304s.size() > 0) {
                                        preFlag = "1";
                                    }
                                } else {
                                    String massage = "没匹配到有效的装卸点，请联系工作人员进行处理！";
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg(massage);
                                    return outInfo;
                                }
                            }
                        }else {
                            List<LIRL0309> lirl0309s = this.dao.query(LIRL0309.QUERY, queryMap);
                            if (lirl0309s.size() > 0) {
                                //查询对应装卸点是否存在
                                List<String> stringList = new ArrayList<>();
                                for (LIRL0309 lirl0309 : lirl0309s) {
                                    stringList.add(lirl0309.getHandPointId());
                                    targetHandPointId = lirl0309.getHandPointId();
                                    String handTypeTwo = lirl0309.getHandType();
                                    targetHandPointIdList.add(targetHandPointId);
                                    handTypeList.add(handTypeTwo);
                                }
                                queryMap.put("status", 30);
                                queryMap.put("handPointIdList", stringList);
                                List<LIRL0304> lirl0304s = this.dao.query(LIRL0304.QUERY, queryMap);
                                if (lirl0304s.size() > 0) {
                                    preFlag = "1";
                                }
                            } else {
                                String massage = "没匹配到有效的装卸点，请联系工作人员进行处理！";
                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                outInfo.setMsg(massage);
                                return outInfo;
                            }
                        }

                    }
                }else {
                    queryMap.put("flag2", "1");
                    queryMap.put("statusFlag","");
                    List<LIRL0309> lirl0309s = this.dao.query(LIRL0309.QUERY, queryMap);
                    if (lirl0309s.size() > 0) {
                        //查询对应装卸点是否存在
                        List<String> stringList = new ArrayList<>();
                        for (LIRL0309 lirl0309 : lirl0309s) {
                            stringList.add(lirl0309.getHandPointId());
                            targetHandPointId = lirl0309.getHandPointId();
                            String handTypeTwo = lirl0309.getHandType();
                            targetHandPointIdList.add(targetHandPointId);
                            handTypeList.add(handTypeTwo);
                        }
                        queryMap.put("status", 30);
                        queryMap.put("handPointIdList", stringList);
                        List<LIRL0304> lirl0304s = this.dao.query(LIRL0304.QUERY, queryMap);
                        if (lirl0304s.size() > 0) {
                            preFlag = "1";
                        }
                    } else {
                        String massage = "没匹配到有效的装卸点，请联系工作人员进行处理！";
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg(massage);
                        return outInfo;
                    }
                }
                //TODO 状态：启用
            } else {
                String[] split = voucherNum.split(",");
                for (String voucherNumAdd : split) {
                    voucherNum=voucherNumAdd;
                    //判断是是否是转库单
                    String substring = voucherNum.substring(0, 2);
                    if ("ZK".equals(substring)) {
                        Map insertLIRL0307 = new HashMap();//车辆登记提单信息表
                        //调IMC服务拿到捆包信息
                        EiInfo eiInfo1 = new EiInfo();
                        eiInfo1.set("segNo", in_seg_no);
                        eiInfo1.set("transBillId", voucherNum);
                        eiInfo1.set("transBillType", "10");
                        eiInfo1.set("packPutOutFlag", "0");
                        eiInfo1.set(EiConstant.serviceId, "S_UC_EP_0045");
                        //调post请求
                        outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == -1) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                            throw new RuntimeException(outInfo.getMsg());
                        }
                        List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
                        if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("未匹配到转库单信息，不允许登记!");
                            return outInfo;
                        }
                        if (rusult.size() > 0) {
                            for (HashMap hashMap : rusult) {
                                //装卸点、装卸点名、厂区编码、厂区名称
                                packId = MapUtils.getString(hashMap, "packId", "");//捆包号
                                matInnerId = MapUtils.getString(hashMap, "matInnerId", "");//材料管理号
                                String dUserName = MapUtils.getString(hashMap, "dUserName", "");//材料管理号
                                String dUserNum = MapUtils.getString(hashMap, "dUserNum", "");//材料管理号
                                String recCreator = MapUtils.getString(hashMap, "recCreator", "");//创建人

                                insertLIRL0307.put("segNo", in_seg_no);
                                insertLIRL0307.put("unitCode", in_seg_no);
                                insertLIRL0307.put("checkId", in_check_id);
                                insertLIRL0307.put("voucherNum", voucherNum);
                                insertLIRL0307.put("dUserNum", dUserNum);
                                insertLIRL0307.put("dUserName",dUserName);
                                insertLIRL0307.put("deliverType", "");
                            /*if ("10".equals(deliveryType)) {
                                insertLIRL0307.put("deliverTypeName", "自提");
                            } else {
                                insertLIRL0307.put("deliverTypeName", "代运");
                            }*/
                                //查询捆包库位
                                EiInfo inInfo = new EiInfo();
                                inInfo.set("segNo", in_seg_no);
                                inInfo.set("packId", packId);
                                inInfo.set(EiConstant.serviceId, "S_UE_WR_1002");
                                inInfo.set("clientId", ImcGlobalUtils.CLIENT_ID);
                                inInfo.set("clientSecret", ImcGlobalUtils.CLIENT_SECRET);
                                outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                                String outJSON = outInfo.getString("messageBody");
                                if (EiConstant.STATUS_FAILURE == outInfo.getStatus()) {
                                    inInfo.setMsg("查询渠道库存材料信息，原因：" + outInfo.getMsg());
                                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    return inInfo;
                                }
                                if (StringUtils.isEmpty(outJSON)) {
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg("提单无匹配库存，不允许登记!");
                                    return outInfo;
                                }
                                //打印日志到elk
                                log(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(inInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                                //输出到应用日志
                                System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(inInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                                if (StringUtils.isNotEmpty(outJSON)) {
                                    Map map2 = JSONObject.fromObject(outJSON);
                                    List<Map> list1 = (List) map2.get("result");
                                    if (list1 != null && list1.size() > 0) {
                                        Map map = list1.get(0);
                                        locationId = MapUtils.getString(map, "locationId", "");
                                        String warehouseCode = MapUtils.getString(map, "warehouseCode", "");
                                        String specsDesc = MapUtils.getString(map, "specsDesc", "");
                                        String prodTypeId = MapUtils.getString(map, "prodTypeId", "");

                                        //根据库位代码查询库位附属信息表获取装卸点
                                        Map queryLIDS0601 = new HashMap();
                                        queryLIDS0601.put("segNo", in_seg_no);
                                        queryLIDS0601.put("warehouseCode", warehouseCode);
                                        queryLIDS0601.put("locationId", locationId);
                                        queryLIDS0601.put("useStatus", 10);
                                        //查询库位附属信息表
                                        List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                        if (lids0601s.size() > 0) {
                                            LIDS0601 lids0601 = lids0601s.get(0);
                                            targetHandPointId = lids0601.getLoadingPointNo();
                                            if (StringUtils.isBlank(targetHandPointId)) {
                                                String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                outInfo.setMsg(massage);
                                                return outInfo;
                                            }
                                            //判断装卸点是否启用
                                            Map queryLIRL0304 = new HashMap();
                                            queryLIRL0304.put("segNo", in_seg_no);
                                            queryLIRL0304.put("handPointId", targetHandPointId);
                                            queryLIRL0304.put("status", "30");
                                            int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                            if (count < 0) {
                                                String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                outInfo.setMsg(massage);
                                                return outInfo;
                                            }
                                            HashMap<String, Object> preLoadTimeMap = new HashMap<>();
                                            preLoadTimeMap.put("specsDesc", specsDesc);
                                            preLoadTimeMap.put("prodTypeId", prodTypeId);
                                            preLoadTimeMap.put("targetHandPointId", targetHandPointId);
                                            preLoadTimeMap.put("locationId", locationId);
                                            preLoadTimeList.add(preLoadTimeMap);
                                        } else {
                                            String massage = "查询不到库位附属信息";
                                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            outInfo.setMsg(massage);
                                            return outInfo;
                                        }
                                    } else {
                                        inInfo.setMsg("未查询到渠道库存材料信息，原因：" + outInfo.getMsg());
                                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        return inInfo;
                                    }
                                }
                                insertLIRL0307.put("documenterPerson", recCreator);
                                insertLIRL0307.put("uuid", StrUtil.getUUID());// uuid
                                insertLIRL0307.put("handPointId", targetHandPointId);
                                // 创建人工号
                                insertLIRL0307.put("recCreator", recCreatorName);
                                // 创建人姓名
                                insertLIRL0307.put("recCreatorName", recCreatorName);
                                // 创建时间
                                insertLIRL0307.put("recCreateTime", DateUtil.curDateTimeStr14());
                                // 修改人工号
                                insertLIRL0307.put("recRevisor", recCreatorName);
                                // 修改人姓名
                                insertLIRL0307.put("recRevisorName", recCreatorName);
                                // 修改时间
                                insertLIRL0307.put("recReviseTime", DateUtil.curDateTimeStr14());
                                //装卸点放到集合中
                                targetHandPointIdList.add(targetHandPointId);
                                handTypeList.add(handType);
                                //插入车辆登记提单信息表
                                LIRL0307 lirl0307 = new LIRL0307();
                                lirl0307.fromMap(insertLIRL0307);
                                insertLIRL0307 = lirl0307.toMap();
                                dao.insert(LIRL0307.INSERT, insertLIRL0307);
                            }
                        }

                    } else {
                        //调IMC服务拿到捆包信息
                        Map insertLIRL0307 = new HashMap();//车辆登记提单信息表
                        EiInfo eiInfo1 = new EiInfo();
                        eiInfo1.set("segNo", in_seg_no);
                        eiInfo1.set("ladingBillId", voucherNum);
                        eiInfo1.set("ladingBillStatus", "50");//生效
                        eiInfo1.set(EiConstant.serviceId, "S_UV_SL_9018");
                        //调post请求
                        outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == -1) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                            throw new RuntimeException(outInfo.getMsg());
                        }
                        List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
                        // List<HashMap> result1 = (List<HashMap>) outInfo.get("result1");
                        List<HashMap> result2 = (List<HashMap>) outInfo.get("result2");
                        if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("未匹配到提单信息，不允许登记!");
                            return outInfo;
                        }
                        if (rusult.size() > 0) {
                            //判断开单方式
                            HashMap hashMapRusult = rusult.get(0);
                            String deliveryType = MapUtils.getString(hashMapRusult, "deliveryType", "");//交货方式（10：自提、20：代运）
                            //提单创建人
                            String recCreator = MapUtils.getString(hashMapRusult, "recCreator", "");//交货方式（10：自提、20：代运）
                            String userNum = MapUtils.getString(hashMapRusult, "userNum", "");
                            String userName = MapUtils.getString(hashMapRusult, "userName", "");
                            String d_userNum = MapUtils.getString(hashMapRusult, "d_userNum", "");
                            String d_userName = MapUtils.getString(hashMapRusult, "d_userName", "");
                            billingMethod = MapUtils.getString(hashMapRusult, "billingMethod", "");//开单方式 10-按捆包 20-按重量 30-按件数


                            //按捆包普通提单 按量形式提单
                            if (!"10".equals(billingMethod)) {
                                ladingSpotId = MapUtils.getString(hashMapRusult, "ladingSpotId", "");//仓库代码

                                //调物流服务拿到查询提货单重量件数信息
                                eiInfo1 = new EiInfo();
                                eiInfo1.set("segNo", in_seg_no);
                                eiInfo1.set("warehouseCode", ladingSpotId);
                                List<String> ladingBillIdList = new ArrayList<>();
                                ladingBillIdList.add(voucherNum);
                                eiInfo1.set("ladingBillIdList", ladingBillIdList);
                                Boolean isRefreshToken = Boolean.FALSE;
                                String tokenRefresh = "";
                                if (outInfo.getStatus() == STATUS_UPDATE_TOKEN) {
                                    isRefreshToken = Boolean.TRUE;
                                    tokenRefresh = outInfo.get("accessToken").toString();
                                }
                                eiInfo1.set(EiConstant.serviceId, "S_UC_PR_0411");
                                //调post请求
                                outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                                if (outInfo.getStatus() == -1) {
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                                    throw new RuntimeException(outInfo.getMsg());
                                }

                                Map attr = outInfo.getAttr();
                                Map result = MapUtils.getMap(attr, "result");
                                List<Map> packList = (List) result.get("packList");
                                if (CollectionUtils.isEmpty(packList) && packList.size() <= 0) {
                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                    outInfo.setMsg("未匹配到提单信息，不允许登记!");
                                    return outInfo;
                                }
                                for (int i = 0; i < packList.size(); i++) {
                                    for (Map map2 : packList) {
                                        //装卸点、装卸点名、厂区编码、厂区名称
                                        packId = MapUtils.getString(map2, "packId", "");//捆包号
                                        matInnerId = MapUtils.getString(map2, "matInnerId", "");//材料管理号
                                        locationId = MapUtils.getString(map2, "locationId", "");//库位代码
                                        String specsDesc = MapUtils.getString(map2, "specsDesc", "");
                                        String prodTypeId = MapUtils.getString(map2, "prodTypeId", "");
                                        String warehouseCode = MapUtils.getString(map2, "warehouseCode", "");//仓库代码
                                        insertLIRL0307.put("shopsign", MapUtils.getString(map2, "shopsign", ""));//牌号
                                        insertLIRL0307.put("specDesc", MapUtils.getString(map2, "specsDesc", ""));//规格
                                        insertLIRL0307.put("weight", MapUtils.getString(map2, "netWeight", ""));//重量
                                        insertLIRL0307.put("pieceNum", MapUtils.getString(map2, "pieceNum", ""));//件数
                                        insertLIRL0307.put("quantity", MapUtils.getString(map2, "quantity", ""));//数量
                                        insertLIRL0307.put("locationId", locationId);
                                        insertLIRL0307.put("locationName", locationId);
                                        insertLIRL0307.put("segNo", in_seg_no);
                                        insertLIRL0307.put("unitCode", in_seg_no);
                                        insertLIRL0307.put("checkId", in_check_id);
                                        insertLIRL0307.put("voucherNum", voucherNum);
                                        insertLIRL0307.put("deliverType", deliveryType);
                                        if ("10".equals(deliveryType)) {
                                            insertLIRL0307.put("deliverTypeName", "自提");
                                        } else {
                                            insertLIRL0307.put("deliverTypeName", "代运");
                                        }
                                        insertLIRL0307.put("packId", packId);
                                        insertLIRL0307.put("matInnerId", matInnerId);
                                        insertLIRL0307.put("customerId", userNum);
                                        insertLIRL0307.put("customerName", userName);
                                        insertLIRL0307.put("prodTypeId", MapUtils.getString(map2, "prodTypeId", ""));
                                        insertLIRL0307.put("warehouseCode", MapUtils.getString(map2, "warehouseCode", ""));
                                        insertLIRL0307.put("prodTypeName", MapUtils.getString(map2, "prodTypeDesc", ""));
                                        insertLIRL0307.put("shopsign", MapUtils.getString(map2, "shopsign", ""));//牌号
                                        insertLIRL0307.put("specDesc", MapUtils.getString(map2, "specsDesc", ""));//规格
                                        insertLIRL0307.put("weight", MapUtils.getString(map2, "netWeight", ""));//重量
                                        insertLIRL0307.put("quantity", MapUtils.getString(map2, "pieceNum", ""));//数量
                                        insertLIRL0307.put("locationId", locationId);
                                        insertLIRL0307.put("locationName", locationId);
                                        insertLIRL0307.put("documenterPerson", recCreator);
                                        insertLIRL0307.put("dUserNum", d_userNum);
                                        insertLIRL0307.put("dUserName", d_userName);
                                        //根据库位代码查询库位附属信息表获取装卸点
                                        Map queryLIDS0601 = new HashMap();
                                        queryLIDS0601.put("segNo", in_seg_no);
                                        queryLIDS0601.put("warehouseCode", warehouseCode);
                                        queryLIDS0601.put("locationId", locationId);
                                        queryLIDS0601.put("useStatus", 10);

                                        //查询库位附属信息表
                                        List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                        if (lids0601s.size() > 0) {
                                            LIDS0601 lids0601 = lids0601s.get(0);
                                            targetHandPointId = lids0601.getLoadingPointNo();
                                            if (StringUtils.isBlank(targetHandPointId)) {
                                                String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                outInfo.setMsg(massage);
                                                return outInfo;
                                            }
                                            //判断装卸点是否启用
                                            Map queryLIRL0304 = new HashMap();
                                            queryLIRL0304.put("segNo", in_seg_no);
                                            queryLIRL0304.put("handPointId", targetHandPointId);
                                            queryLIRL0304.put("status", "30");
                                            int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                            if (count < 0) {
                                                String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                outInfo.setMsg(massage);
                                                return outInfo;
                                            }
                                            HashMap<String, Object> preLoadTimeMap = new HashMap<>();
                                            preLoadTimeMap.put("specsDesc", specsDesc);
                                            preLoadTimeMap.put("prodTypeId", prodTypeId);
                                            preLoadTimeMap.put("targetHandPointId", targetHandPointId);
                                            preLoadTimeMap.put("locationId", locationId);
                                            preLoadTimeList.add(preLoadTimeMap);
                                        } else {
                                            String massage = "查询不到库位附属信息";
                                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            outInfo.setMsg(massage);
                                            return outInfo;
                                        }

                                        insertLIRL0307.put("uuid", StrUtil.getUUID());// uuid
                                        insertLIRL0307.put("handPointId", targetHandPointId);
                                        // 创建人工号
                                        insertLIRL0307.put("recCreator", recCreatorName);
                                        // 创建人姓名
                                        insertLIRL0307.put("recCreatorName", recCreatorName);
                                        // 创建时间
                                        insertLIRL0307.put("recCreateTime", DateUtil.curDateTimeStr14());
                                        // 修改人工号
                                        insertLIRL0307.put("recRevisor", recCreatorName);
                                        // 修改人姓名
                                        insertLIRL0307.put("recRevisorName", recCreatorName);
                                        // 修改时间
                                        insertLIRL0307.put("recReviseTime", DateUtil.curDateTimeStr14());
                                        RecordUtils.setCreator(insertLIRL0307);
                                        //装卸点放到集合中
                                        targetHandPointIdList.add(targetHandPointId);
                                        handTypeList.add(handType);
                                        //插入车辆登记提单信息表
                                        LIRL0307 lirl0307 = new LIRL0307();
                                        lirl0307.fromMap(insertLIRL0307);
                                        insertLIRL0307 = lirl0307.toMap();
                                        dao.insert(LIRL0307.INSERT, insertLIRL0307);
                                    }
                                }

                            } else {
                                for (HashMap hashMap : result2) {
                                    //装卸点、装卸点名、厂区编码、厂区名称
                                    String retrunStatus = MapUtils.getString(hashMap, "returnStatus", "");//库位代码
                                    if ("Y".equals(retrunStatus)) {
                                        //已出库的
                                        continue;
                                    }
                                    packId = MapUtils.getString(hashMap, "packId", "");//捆包号
                                    matInnerId = MapUtils.getString(hashMap, "matInnerId", "");//材料管理号
                                    locationId = MapUtils.getString(hashMap, "locationId", "");//库位代码

                                    insertLIRL0307.put("segNo", in_seg_no);
                                    insertLIRL0307.put("unitCode", in_seg_no);
                                    insertLIRL0307.put("checkId", in_check_id);
                                    insertLIRL0307.put("voucherNum", voucherNum);
                                    insertLIRL0307.put("deliverType", deliveryType);
                                    insertLIRL0307.put("dUserNum", d_userNum);
                                    insertLIRL0307.put("dUserName", d_userName);
                                    if ("10".equals(deliveryType)) {
                                        insertLIRL0307.put("deliverTypeName", "自提");
                                    } else {
                                        insertLIRL0307.put("deliverTypeName", "代运");
                                    }
                                    //查询捆包库位
                                    EiInfo inInfo = new EiInfo();
                                    inInfo.set("segNo", in_seg_no);
                                    inInfo.set("packId", packId);
                                    inInfo.set(EiConstant.serviceId, "S_UE_WR_1002");
                                    inInfo.set("clientId", ImcGlobalUtils.CLIENT_ID);
                                    inInfo.set("clientSecret", ImcGlobalUtils.CLIENT_SECRET);
                                    outInfo = EServiceManager.call(inInfo, TokenUtils.getXplatToken());
                                    String outJSON = outInfo.getString("messageBody");
                                    if (EiConstant.STATUS_FAILURE == outInfo.getStatus()) {
                                        inInfo.setMsg("查询渠道库存材料信息，原因：" + outInfo.getMsg());
                                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        return inInfo;
                                    }
                                    if (StringUtils.isEmpty(outJSON)) {
                                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        outInfo.setMsg("提单无匹配库存，不允许登记!");
                                        return outInfo;
                                    }
                                    //打印日志到elk
                                    log(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(inInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                                    //输出到应用日志
                                    System.out.println(DateUtil.getTimeNow(new Date()) + "：" + "查询渠道库存材料信息：" + JSONObject.fromObject(inInfo.toJSONString()) + "\n" + "查询渠道库存材料信息：" + outInfo.toJSONString());
                                    if (StringUtils.isNotEmpty(outJSON)) {
                                        Map map2 = JSONObject.fromObject(outJSON);
                                        List<Map> list1 = (List) map2.get("result");
                                        if (list1 != null && list1.size() > 0) {
                                            Map map = list1.get(0);
                                            locationId = MapUtils.getString(map, "locationId", "");
                                            String warehouseCode = MapUtils.getString(map, "warehouseCode", "");
                                            String specsDesc = MapUtils.getString(map, "specsDesc", "");
                                            String prodTypeId = MapUtils.getString(map, "prodTypeId", "");
                                            //根据库位代码查询库位附属信息表获取装卸点
                                            Map queryLIDS0601 = new HashMap();
                                            queryLIDS0601.put("segNo", in_seg_no);
                                            queryLIDS0601.put("warehouseCode", warehouseCode);
                                            queryLIDS0601.put("locationId", locationId);
                                            queryLIDS0601.put("useStatus", 10);
                                            //查询库位附属信息表
                                            List<LIDS0601> lids0601s = this.dao.query(LIDS0601.QUERY, queryLIDS0601);
                                            if (lids0601s.size() > 0) {
                                                LIDS0601 lids0601 = lids0601s.get(0);
                                                targetHandPointId = lids0601.getLoadingPointNo();
                                                if (StringUtils.isBlank(targetHandPointId)) {
                                                    String massage = "库位名称：" + lids0601.getLocationName() + "查询不到对应的装卸点，请维护装卸点！";
                                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                    outInfo.setMsg(massage);
                                                    return outInfo;
                                                }
                                                //判断装卸点是否启用
                                                Map queryLIRL0304 = new HashMap();
                                                queryLIRL0304.put("segNo", in_seg_no);
                                                queryLIRL0304.put("handPointId", targetHandPointId);
                                                queryLIRL0304.put("status", "30");
                                                int count = super.count(LIRL0304.COUNT, queryLIRL0304);
                                                if (count < 0) {
                                                    String massage = "根据提单未找到对应的装卸点，请及时联系仓库人员！";
                                                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                    outInfo.setMsg(massage);
                                                    return outInfo;
                                                }
                                                HashMap<String, Object> preLoadTimeMap = new HashMap<>();
                                                preLoadTimeMap.put("specsDesc", specsDesc);
                                                preLoadTimeMap.put("prodTypeId", prodTypeId);
                                                preLoadTimeMap.put("targetHandPointId", targetHandPointId);
                                                preLoadTimeMap.put("locationId", locationId);
                                                preLoadTimeList.add(preLoadTimeMap);
                                            } else {
                                                String massage = "查询不到库位附属信息";
                                                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                                outInfo.setMsg(massage);
                                                return outInfo;
                                            }
                                        } else {
                                            inInfo.setMsg("未查询到渠道库存材料信息，原因：" + outInfo.getMsg());
                                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            return inInfo;
                                        }
                                    }
                                    insertLIRL0307.put("documenterPerson", recCreator);
                                    insertLIRL0307.put("uuid", StrUtil.getUUID());// uuid
                                    insertLIRL0307.put("handPointId", targetHandPointId);
                                    // 创建人工号
                                    insertLIRL0307.put("recCreator", recCreatorName);
                                    // 创建人姓名
                                    insertLIRL0307.put("recCreatorName", recCreatorName);
                                    // 创建时间
                                    insertLIRL0307.put("recCreateTime", DateUtil.curDateTimeStr14());
                                    // 修改人工号
                                    insertLIRL0307.put("recRevisor", recCreatorName);
                                    // 修改人姓名
                                    insertLIRL0307.put("recRevisorName", recCreatorName);
                                    // 修改时间
                                    insertLIRL0307.put("recReviseTime", DateUtil.curDateTimeStr14());
                                    //装卸点放到集合中
                                    targetHandPointIdList.add(targetHandPointId);
                                    handTypeList.add(handType);
                                    //插入车辆登记提单信息表
                                    LIRL0307 lirl0307 = new LIRL0307();
                                    lirl0307.fromMap(insertLIRL0307);
                                    insertLIRL0307 = lirl0307.toMap();
                                    dao.insert(LIRL0307.INSERT, insertLIRL0307);
                                }
                            }
                        }
                    }
                }
            }
            //计算预计装货时间
//            if (CollectionUtils.isNotEmpty(preLoadTimeList)){
//                preLoadTimeList = preLoadTimeList.stream().distinct().collect(Collectors.toList());//去重
//                //每个单包计算时间
//                int singleTime=0;
//                for (HashMap hashMap : preLoadTimeList) {
//                    String targetHandPointId1 = MapUtils.getString(hashMap, "targetHandPointId");
//                    String locationId1 = MapUtils.getString(hashMap, "locationId");
//                    String specsDesc = MapUtils.getString(hashMap, "specsDesc");
//                    String prodTypeId = MapUtils.getString(hashMap, "prodTypeId");
//                    //根据装卸点查询装卸点名称
//                    hashMap.put("segNo", in_seg_no);
//                    hashMap.put("status", "30");
//                    hashMap.put("handPointId", targetHandPointId1);
//                    List<HashMap> queryLIRL0304 = this.dao.query(LIRL0304.QUERY_HAND_POINT_NAME, hashMap);
//                    String handPointName = (String) queryLIRL0304.get(0).get("handPointName");
//                    if ("1号门".equals(handPointName)||"2号门".equals(handPointName)||"3号门".equals(handPointName)||"4号门".equals(handPointName)||"9号门".equals(handPointName)||"10号门".equals(handPointName)){
//                        //1,2,3,4,9,10 按照库位起点为终点计算库位间隔数
//                        //查询库位附属信息获取中终点标记
//                        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//                        objectObjectHashMap.put("locationId",locationId1.substring(0,locationId1.length()-2));
//                        objectObjectHashMap.put("segNo",in_seg_no);
//                        objectObjectHashMap.put("status","10");
//                        List<String> queryLIDS0601 = this.dao.query(LIDS0601.QUERY_MIN_LOCATION, objectObjectHashMap);
//                        if (CollectionUtils.isNotEmpty(queryLIDS0601)){
//                            String minLocationId = queryLIDS0601.get(0);
//                            //间隔数
//                            int mun = calculateInterval(minLocationId, locationId1);
//                            ///计算公式：单包合计时间=基准时间134s+库位间隔数*单位库位间隔运行时间8s
//                            int time = 134 + (mun * 8);
//                            //装货时间累加
//                            singleTime =time;
//                        }
//                    }else if ("5号门".equals(handPointName)||"6号门".equals(handPointName)||"7号门".equals(handPointName)||"8号门".equals(handPointName)||"11号门".equals(handPointName)||"12号门".equals(handPointName)){
//                        //5 ,6,7,8,11,12 按照库位终点计算库位间隔数
//                        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//                        objectObjectHashMap.put("locationId",locationId1.substring(0,locationId1.length()-2));
//                        objectObjectHashMap.put("segNo",in_seg_no);
//                        objectObjectHashMap.put("status","10");
//                        List<String> queryLIDS0601 = this.dao.query(LIDS0601.QUERY_MAX_LOCATION, objectObjectHashMap);
//                        if (CollectionUtils.isNotEmpty(queryLIDS0601)){
//                            String maxLocationId = queryLIDS0601.get(0);
//                            //计算库位间隔数
//                            int mun = calculateInterval(locationId1, maxLocationId);
//                            ///计算公式：单包合计时间=基准时间134s+库位间隔数*单位库位间隔运行时间8s
//                            int time = 134 + (mun * 8);
//                            //装货时间累加
//                            singleTime =time;
//                        }
//                    }
//
//                    //根据三级品种附属码查询规则配置表
//                    String prodTypeIdStr = prodTypeId.substring(0, prodTypeId.length() - 1);
//                    HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//                    objectObjectHashMap.put("prodTypeId",prodTypeIdStr);
//                    objectObjectHashMap.put("segNo",in_seg_no);
//                    objectObjectHashMap.put("status","20");
//                    if (specsDesc.contains("*C")){
//                        //板
//                        objectObjectHashMap.put("coilSheetFlag","20");
//                    }else {
//                        //卷
//                        objectObjectHashMap.put("coilSheetFlag","10");
//                    }
//                    List<LIRL0318> queryLIRL0318 = this.dao.query(LIRL0318.QUERY, objectObjectHashMap);
//                    if (CollectionUtils.isNotEmpty(queryLIRL0318)){
//                        LIRL0318 lirl0318 = queryLIRL0318.get(0);
//                        Integer reverseDurationSeconds = lirl0318.getReverseDurationSeconds();//倒车时间
//                        Integer searchDurationSeconds = lirl0318.getSearchDurationSeconds();//找货时间
//                        Integer toolChangeDurationSeconds = lirl0318.getToolChangeDurationSeconds();//切换吊具时间
//                        //装货时间计算公式
//                       int toolReTime = reverseDurationSeconds+searchDurationSeconds+toolChangeDurationSeconds+singleTime;
//                        preLoadTime+=toolReTime;
//                    }
//                }
//            }
            if ("10".equals(handType) || "20".equals(handType) || "30".equals(handType)||"40".equals(handType)) {
                List<HashMap> listLIRL0305=new ArrayList<>();

                //查询车辆排序主表数量
                Map countLIRL0303 = new HashMap();
                countLIRL0303.put("segNo", in_seg_no);
                List<HashMap> maxQueueNumber = this.dao.query(LIRL0401.QUERY_MAX_NUMBER, countLIRL0303);
                double maxQueueNumber1 = MapUtils.getDouble(maxQueueNumber.get(0), "maxQueueNumber");
                int count = (int) maxQueueNumber1+1;
                //装卸业务需要查询所有的原料卸货点
                if ("30".equals(handType)) {
                    //有装卸业务的要看装卸业务具体的装卸点
                    //查询装卸点

                    Map queryLIRL0305 = new HashMap<>();
                    queryLIRL0305.put("segNo", in_seg_no);
                    queryLIRL0305.put("carTraceNo", carTraceNo);
                    queryLIRL0305.put("vehicleId", vehicleNo);
                    queryLIRL0305.put("statusFlag", "10");
                    if (StringUtils.isNotBlank(lirl0302.getVoucherNum())) {
                        // queryLIRL0305.put("businessType", "10");//原料卸货
                        //找卸货的装卸点
                        //先根据起始地分流，起始地不存在查询所有的能进的卸货点
                        List<LIRL0301> queryHashMap = this.dao.query(LIRL0301.QUERY, queryLIRL0305);
                        if (CollectionUtils.isNotEmpty(queryHashMap)){
                            String startOfTransport = queryHashMap.get(0).getStartOfTransport();
                            if (StringUtils.isNotBlank(startOfTransport)) {
                                HashMap<String, String> objectObjectHashMap = new HashMap<>();
//                                objectObjectHashMap.put("siteNameLike", startOfTransport);
                                objectObjectHashMap.put("segNo", in_seg_no);
                                objectObjectHashMap.put("status", "30");
                                List<LIRL0315> queryLIRL0315 = this.dao.query(LIRL0315.QUERY, objectObjectHashMap);
                                if (CollectionUtils.isNotEmpty(queryLIRL0315)){
                                    for (LIRL0315 lirl0315 : queryLIRL0315) {
                                        String siteName = lirl0315.getSiteName();
                                        String handPointId1 = lirl0315.getHandPointId();
                                        if (startOfTransport.contains(siteName)) {
                                            handPointId.add(handPointId1);
                                        }
                                    }
                                    if (CollectionUtils.isEmpty(handPointId)){
                                        listLIRL0305 = dao.query("LIRL0305.queryLoadingUnloading", queryLIRL0305);
                                        if (listLIRL0305.size() <= 0 && CollectionUtils.isEmpty(listLIRL0305)) {
                                            String massage = "原料卸货点不存在，请联系工作人员进行处理！";
                                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                            outInfo.setMsg(massage);
                                            return outInfo;
                                        }
                                        handPointId  = listLIRL0305.stream().map(hashMap -> (String) hashMap.get("handPointId")).collect(Collectors.toList());
                                    }
                                }else {
                                    listLIRL0305 = dao.query("LIRL0305.queryLoadingUnloading", queryLIRL0305);
                                    if (listLIRL0305.size() <= 0 && CollectionUtils.isEmpty(listLIRL0305)) {
                                        String massage = "原料卸货点不存在，请联系工作人员进行处理！";
                                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                                        outInfo.setMsg(massage);
                                        return outInfo;
                                    }
                                   handPointId  = listLIRL0305.stream().map(hashMap -> (String) hashMap.get("handPointId")).collect(Collectors.toList());
                                }
                            }
                        }

                        handPointIdN = getHandPointId(in_seg_no, handPointIdN, handPointId);
                        handType = "30";
                        targetHandPointIdList = handPointId;
                    } else {
                        handPointIdN = getHandPointId(in_seg_no, handPointIdN, targetHandPointIdList);
                    }
                } else {
                    handPointIdN = getHandPointId(in_seg_no, handPointIdN, targetHandPointIdList);

                }

                //插入预排序主表
                Map insertLIRL0303 = new HashMap();
                insertLIRL0303.put("segNo", in_seg_no);
                insertLIRL0303.put("unitCode", in_seg_no);
                insertLIRL0303.put("queueNumber", count);
                insertLIRL0303.put("carTraceNo", carTraceNo);
                insertLIRL0303.put("vehicleNo", vehicleNo);
                insertLIRL0303.put("overtimeCount", 0);
                insertLIRL0303.put("voucherNum", lirl0302.getVoucherNum());
                insertLIRL0303.put("priorityLevel", "99");//优先级（默认99）
                insertLIRL0303.put("queueDate", stringDate);
                insertLIRL0303.put("targetHandPointId", handPointIdN);//目标装卸点
                insertLIRL0303.put("factoryArea", factoryArea);//厂区
//                RecordUtils.setCreator(insertLIRL0303);
                // 创建人工号
                insertLIRL0303.put("recCreator", recCreatorName);
                // 创建人姓名
                insertLIRL0303.put("recCreatorName", recCreatorName);
                // 创建时间
                insertLIRL0303.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                insertLIRL0303.put("recRevisor", recCreatorName);
                // 修改人姓名
                insertLIRL0303.put("recRevisorName", recCreatorName);
                // 修改时间
                insertLIRL0303.put("recReviseTime", DateUtil.curDateTimeStr14());
                insertLIRL0303.put("uuid", StrUtil.getUUID());// uuid
                insertLIRL0303.put("handType", handType);// uuid
                LIRL0303 lirl0303 = new LIRL0303();
                lirl0303.fromMap(insertLIRL0303);
                dao.insert(LIRL0303.INSERT, lirl0303);
                //判断是否有提单
                //插入车辆排队叫号装卸点子表
                int countHandType = 0;
                int queueNumber = 0;
                List<String> collectHandPointIdList = targetHandPointIdList.stream().distinct().collect(Collectors.toList());
                for (String string : collectHandPointIdList) {
                    Map insertLIRL0305 = new HashMap();
                    insertLIRL0305.putAll(insertLIRL0303);
                    insertLIRL0305.put("vehicleId", vehicleNo);
                    insertLIRL0305.put("queueNumber", queueNumber);
                    insertLIRL0305.put("handPointId", string);
                    insertLIRL0305.put("uuid", StrUtil.getUUID());// uuid
                    insertLIRL0305.put("handType", handType);// 装卸类型(10 装 20卸 30装卸)
                    LIRL0305 lirl0305 = new LIRL0305();
                    lirl0305.fromMap(insertLIRL0305);
                    if (StringUtils.isBlank(lirl0302.getVoucherNum())) {
                        dao.insert(LIRL0305.INSERT, lirl0305);
                    } else if ("10".equals(billingMethod)) {
                        //如果是普通发货通知书
                        dao.insert(LIRL0305.INSERT, lirl0305);
                    } else {
                        //如果是形式发货通知书
                        dao.insert(LIRL0305.INSERT, lirl0305);
                    }
                    countHandType++;
                    queueNumber++;
                }
            }

            //更新车辆跟踪表信息 装卸点
            Map updateLIRL0301 = new HashMap();
            updateLIRL0301.put("segNo", in_seg_no);
            updateLIRL0301.put("carTraceNo", carTraceNo);
            updateLIRL0301.put("targetHandPointId", handPointIdN);
            updateLIRL0301.put("delFlag", 0);
            // 修改人工号
            updateLIRL0301.put("recRevisor", recCreatorName);
            // 修改人姓名
            updateLIRL0301.put("recRevisorName", recCreatorName);
            // 修改时间
            updateLIRL0301.put("recReviseTime", DateUtil.curDateTimeStr14());
            if (preLoadTime>0) {
                updateLIRL0301.put("expectedLoadingTime", preLoadTime/60);
            }
//            RecordUtils.setRevisor(updateLIRL0301);
            dao.update(LIRL0301.UPDATE_LIRL0301TARGET_HAND_POINT_ID, updateLIRL0301);
        } else {
            String massage = "查询不到进厂登记信息";
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(massage);
            return outInfo;
        }

        return outInfo;
    }

    /***
     * 查找装卸点最合适的点
     * @param in_seg_no
     * @param handPointIdN
     * @param targetHandPointIdList
     * @return
     */
    private String getHandPointId(String in_seg_no, String handPointIdN, List<String> targetHandPointIdList) {
        //组装车辆可以进车的装卸点，饱和的先不考虑
        List<String> handPointIds = new ArrayList<>();
        List<String> handPointIds1 = new ArrayList<>();
        //如果有多个装卸点取最优原则
        for (String handPointId : targetHandPointIdList) {
            //查询车辆排序主表数量
            Map countLIRL03031 = new HashMap();
            countLIRL03031.put("segNo", in_seg_no);
            countLIRL03031.put("handPointId",handPointId);
            //判断所有的点是什么状态
            List<LIRL0306> queryLIRL0306= this.dao.query(LIRL0306.QUERY, countLIRL03031);
            if (CollectionUtils.isNotEmpty(queryLIRL0306)){
                String status = queryLIRL0306.get(0).getStatus();
                String handPointIdNew = queryLIRL0306.get(0).getHandPointId();
                if ("20".equals(status)){ //可以进车
                    handPointIds.add(handPointIdNew);
                }else if ("10".equals(status)){//饱和
                    handPointIds1.add(handPointIdNew);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(handPointIds)){
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", in_seg_no);
            hashMap.put("arrayList",handPointIds);
            //取最合适的点
            List<HashMap> queryLIRL0306 = this.dao.query(LIRL0306.QUERY_MAX_HAND_POINT, hashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL0306)){
                handPointIdN = (String) queryLIRL0306.get(0).get("handPointId");
            }

        }else {
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo", in_seg_no);
            hashMap.put("arrayList",handPointIds1);
            //取最合适的点，查看排队表中那个点的车辆最多
            List<HashMap> queryLIRL0306 = this.dao.query(LIRL0306.QUERY_MAX_QUEUE_VEHICLE_NO, hashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL0306)){
                handPointIdN = (String) queryLIRL0306.get(0).get("handPointId");
            }
            if (StringUtils.isBlank(handPointIdN)){
                //取最合适的点
                queryLIRL0306 = this.dao.query(LIRL0306.QUERY_MAX_HAND_POINT, hashMap);
                if (CollectionUtils.isNotEmpty(queryLIRL0306)){
                    handPointIdN = (String) queryLIRL0306.get(0).get("handPointId");
                }
            }
        }
        return handPointIdN;
    }

    /***
     * 考虑先去卸货点 再去装货点
     * 存储过程：prog.wl_vehicle_quene_mgr.proc_pre_hand_point
     * @param in_seg_no 系统账套号
     * @param in_check_id  登记流水号
     * @param in_car_trace_no  车辆跟踪单号
     * @param in_vehicle_no  车牌号
     * @param in_modi_person  修改人
     * @return
     */
    private EiInfo procPreHandPoint(String in_seg_no, String in_check_id, String in_car_trace_no, String in_vehicle_no, String in_modi_person, String recCreatorName,String voucherNum) {
        EiInfo outInfo = new EiInfo();
        String stringDate = DateUtil.curDateTimeStr14();//当前时间
        int lv_queue_number = 0;
        //查询进场登记表
        Map queryLIRL0302 = new HashMap();
        queryLIRL0302.put("segNo",in_seg_no);
        queryLIRL0302.put("checkId",in_check_id);

        List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY, queryLIRL0302);
        if (lirl0302s.size()>0){
            LIRL0302 lirl0302 = lirl0302s.get(0);
            String handType = lirl0302.getHandType();//装卸类型(10 装 20卸 30装卸)
            //装货业务  或者卸货业务  子表序列号不需处理
            //修改装卸货子表
            Map updateLIRL0305 = new HashMap();
            updateLIRL0305.put("vehicleId",in_vehicle_no);
            updateLIRL0305.put("carTraceNo",in_car_trace_no);
            updateLIRL0305.put("segNo",in_seg_no);
            // 修改人工号
            updateLIRL0305.put("recRevisor", recCreatorName);
            // 修改人姓名
            updateLIRL0305.put("recRevisorName", recCreatorName);
            // 修改时间
            updateLIRL0305.put("recReviseTime", DateUtil.curDateTimeStr14());
//            RecordUtils.setRevisor(updateLIRL0305);
            if ("10".equals(handType)){
                updateLIRL0305.put("handType","10");
            }else if ("20".equals(handType)){
                updateLIRL0305.put("handType","20");
            }else {

                //有装卸业务的要看装卸业务具体的装卸点
                //查询装卸点
                Map queryLIRL0305 = new HashMap<>();
                queryLIRL0305.put("segNo", in_seg_no);
                queryLIRL0305.put("carTraceNo", lirl0302.getCarTraceNo());
                queryLIRL0305.put("vehicleId", in_vehicle_no);
                List<HashMap> list = dao.query("LIRL0305.queryLoadingUnloadingStatus", queryLIRL0305);
                //有装卸业务的要看装卸业务具体的装卸点
                //查询装卸点
                //提单不为空,并且为卸货
                int queueNumber =0;
                if (StringUtils.isNotBlank(voucherNum)) {
                    // queryLIRL0305.put("businessType", "10");//原料卸货
                    //找卸货的装卸点
                    List<HashMap> listLIRL0305 = dao.query("LIRL0305.queryLoadingUnloading", queryLIRL0305);
                    if (listLIRL0305.size() <= 0 && CollectionUtils.isEmpty(listLIRL0305)) {
                        String massage = "原料卸货点不存在，请联系工作人员进行处理！";
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg(massage);
                        return outInfo;
                    }
                    for (HashMap hashMap : listLIRL0305) {
                        String handPointId = MapUtils.getString(hashMap, "handPointId", "");
                        Map insertLIRL0305 = new HashMap();
                        insertLIRL0305.put("segNo", in_seg_no);
                        insertLIRL0305.put("unitCode", in_seg_no);
                        insertLIRL0305.put("queueNumber", queueNumber);
                        insertLIRL0305.put("carTraceNo", lirl0302.getCarTraceNo());
                        insertLIRL0305.put("vehicleId", in_vehicle_no);
                        insertLIRL0305.put("handPointId", handPointId);
                        insertLIRL0305.put("uuid", StrUtil.getUUID());// uuid
                        insertLIRL0305.put("handType", "20");// 装卸类型(10 装 20卸 30装卸)
                        // 创建人工号
                        insertLIRL0305.put("recCreator", recCreatorName);
                        // 创建人姓名
                        insertLIRL0305.put("recCreatorName", recCreatorName);
                        // 创建时间
                        insertLIRL0305.put("recCreateTime", DateUtil.curDateTimeStr14());
                        // 修改人工号
                        insertLIRL0305.put("recRevisor", recCreatorName);
                        // 修改人姓名
                        insertLIRL0305.put("recRevisorName", recCreatorName);
                        // 修改时间
                        insertLIRL0305.put("recReviseTime", DateUtil.curDateTimeStr14());
//                        RecordUtils.setCreator(insertLIRL0305);
                        dao.insert(LIRL0305.INSERT, insertLIRL0305);
                        queueNumber++;
                    }

                }


                for (HashMap hashMap : list) {
                    String handPointId = MapUtils.getString(hashMap, "handPointId", "0");
                    String handType1 = MapUtils.getString(hashMap, "handType", "");
                    if (StringUtils.isEmpty(handType1)&&StringUtils.isBlank(voucherNum)){
                        String massage = "装卸业务：请维护装卸点与装卸业务对照关系，请联系工作人员进行处理！";
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg(massage);
                        return outInfo;
                    }

                    updateLIRL0305.put("queueNumber",queueNumber =queueNumber+1);
                    updateLIRL0305.put("handPointId", handPointId);
                    if (StringUtils.isBlank(voucherNum)){
                        updateLIRL0305.put("handType", handType1);
                    }else {
                        updateLIRL0305.put("handType", "10");
                    }
                    this.dao.update(LIRL0305.UPDATEPROCPREHANDPOINT, updateLIRL0305);
                }

            }
            if (!"30".equals(handType)){
                this.dao.update(LIRL0305.UPDATEPROCPREHANDPOINT,updateLIRL0305);
            }
        }else {
            String massage = "查询不到进厂登记信息";
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(massage);
            return outInfo;
        }

        return outInfo;
    }

    /***
     * 检查车辆是否重复登记
     * 存储过程：prog.wl_vehicle_quene_mgr.check_wl_vehicle_checkin
     * @param in_seg_no 系统账套号
     * @param in_check_id  登记流水号
     * @return
     */
    private EiInfo checkWlVehicleCheckin(String in_seg_no, String in_check_id, String driverName) {
        EiInfo outInfo = new EiInfo();
        String lv_vehicle_id = "";//车牌号
        String lv_car_trace_no = "";//车辆跟踪号
        String stringDate = DateUtil.curDateTimeStr14();//修改时间

        Map queryMap = new HashMap();
        queryMap.put("segNo", in_seg_no);
        queryMap.put("checkId", in_check_id);
        queryMap.put("delFlag", 0);
        List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY, queryMap);
        if (lirl0302s.size() > 0) {
            LIRL0302 lirl0302 = lirl0302s.get(0);
            lv_vehicle_id = lirl0302.getVehicleNo();
            lv_car_trace_no = lirl0302.getCarTraceNo();
        }

        queryMap = new HashMap();
        queryMap.put("segNo", in_seg_no);
        queryMap.put("vehicleNo", lv_vehicle_id);
        queryMap.put("notCarTraceNo", lv_car_trace_no);
        queryMap.put("delFlag", 0);
        int count = super.count("LIRL0301.countDuplicateRegistration", queryMap);
        if (count > 0) {
            List<HashMap> lirl0301s = dao.query("LIRL0301.queryDuplicateRegistration", queryMap);
            for (HashMap hashMap : lirl0301s) {
                String status = MapUtils.getString(hashMap, "status", "");
                String lirl0301carTraceNo = MapUtils.getString(hashMap, "carTraceNo", "");
                Integer statusNum = new Integer(status);
                if (statusNum == 10) {
                    //如果车辆已经登记一次，但是车辆只在排队还没有>=叫号，是可以重复登记，将之前的登记数据撤销就行，重新插入一条
                    //取消登记同步撤销预排序数据
                    //删除预排序主表
                    Map deleteLIRL0303 = new HashMap<>();
                    deleteLIRL0303.put("segNo", in_seg_no);
                    deleteLIRL0303.put("vehicleNo", lv_vehicle_id);
                    deleteLIRL0303.put("vehicleId", lv_vehicle_id);
                    deleteLIRL0303.put("carTraceNo", lirl0301carTraceNo);
                    // 修改人工号
                    deleteLIRL0303.put("recRevisor", driverName);
                    // 修改人姓名
                    deleteLIRL0303.put("recRevisorName", driverName);
                    // 修改时间
                    deleteLIRL0303.put("recReviseTime", DateUtil.curDateTimeStr14());
                    //RecordUtils.setRevisor(deleteLIRL0303);
                    dao.delete(LIRL0303.DELETE, deleteLIRL0303);
                    //从装卸子表中删除数据
                    dao.delete(LIRL0305.DELETE, deleteLIRL0303);
                    //如果在超时表 从超时表中删掉
                    dao.delete(LIRL0403.DELETE, deleteLIRL0303);
                    //从车辆排序临时表删除
                    dao.delete(LIRL0310.DELETE, deleteLIRL0303);
                    //删除车辆排序主表
                    Map deleteLIRL0401 = new HashMap();
                    deleteLIRL0401.put("segNo", in_seg_no);
                    deleteLIRL0401.put("carTraceNo", lirl0301carTraceNo);
                    deleteLIRL0401.put("vehicleNo", lv_vehicle_id);
                    // 修改人工号
                    deleteLIRL0401.put("recRevisor", driverName);
                    // 修改人姓名
                    deleteLIRL0401.put("recRevisorName", driverName);
                    // 修改时间
                    deleteLIRL0401.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.delete(LIRL0401.DELETE, deleteLIRL0401);
                    //撤销登记表
                    Map updateLIRL0302 = new HashMap();
                    updateLIRL0302.put("segNo", in_seg_no);
                    updateLIRL0302.put("status", "00");
                    updateLIRL0302.put("sysRemark", "车辆重复登记,撤销此次登记");// 备注
                    updateLIRL0302.put("remark", "车辆重复登记,撤销此次登记");// 备注
                    updateLIRL0302.put("carTraceNo", lirl0301carTraceNo);// 车辆追踪号
                    // 修改人工号
                    updateLIRL0302.put("recRevisor", driverName);
                    // 修改人姓名
                    updateLIRL0302.put("recRevisorName", driverName);
                    // 修改时间
                    updateLIRL0302.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.update("LIRL0302.updateTranslation", updateLIRL0302);

                    //撤销车辆跟踪表
                    // 修改人工号
                    hashMap.put("recRevisor", driverName);
                    // 修改人姓名
                    hashMap.put("recRevisorName", driverName);
                    // 修改时间
                    hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.delete(LIRL0301.DELETE, hashMap);

                    //检查叫号表是否有数据
                    Map queryLIRL0402 = new HashMap();
                    queryLIRL0402.put("segNo", in_seg_no);
                    queryLIRL0402.put("carTraceNo", lirl0301carTraceNo);
                    queryLIRL0402.put("vehicleNo", lv_vehicle_id);
                    List<LIRL0402> lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
                    if (lirl0402s.size() > 0) {
                        LIRL0402 lirl0402 = lirl0402s.get(0);
                        String lv_min_hand_point = lirl0402.getTargetHandPointId();
                        //如果在叫号表 从叫号表中删掉
                        dao.delete(LIRL0402.DELETE, queryLIRL0402);
                        //叫号表数据删除后，装卸点的进车跟踪要随之改变
                        outInfo = updateHandPointJobNumber(in_seg_no, lv_min_hand_point, 0, -1, 31);
                        if (outInfo.getStatus() < 0) {
                            return outInfo;
                        }
                    }
                    //删除补录的预约单号
                    Map deleteLIRL0201 = new HashMap();
                    deleteLIRL0201.put("segNo", in_seg_no);
                    deleteLIRL0201.put("reservationNumber", MapUtils.getString(hashMap, "reservationNumber", ""));
                    // 修改人工号
                    deleteLIRL0201.put("recRevisor", driverName);
                    // 修改人姓名
                    deleteLIRL0201.put("recRevisorName", driverName);
                    // 修改时间
                    deleteLIRL0201.put("recReviseTime", DateUtil.curDateTimeStr14());
                    dao.update(LIRL0201.DELETE_BY_RESERVATION_NUMBER, deleteLIRL0201);

                } else if (statusNum > 10 && statusNum < 50) {
                    String massage = "车牌号:" + lv_vehicle_id + "已进厂,不能重复登记!";
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg(massage);
                    return outInfo;
                } else if (statusNum < 10) {
                    //撤销登记表
                    Map updateLIRL0302 = new HashMap();
                    updateLIRL0302.put("segNo", in_seg_no);
                    updateLIRL0302.put("status", "00");
                    updateLIRL0302.put("sysRemark", "车辆重复登记,撤销此次登记");// 备注
                    updateLIRL0302.put("carTraceNo", lirl0301carTraceNo);// 车辆追踪号
                    RecordUtils.setRevisor(updateLIRL0302);
                    dao.update("LIRL0302.updateTranslation", updateLIRL0302);

                    //撤销车辆跟踪表
                    RecordUtils.setRevisor(hashMap);
                    dao.delete(LIRL0301.DELETE, hashMap);

                    //取消登记同步撤销预排序数据

                    //删除预排序主表
                    Map deleteLIRL0303 = new HashMap<>();
                    deleteLIRL0303.put("segNo", in_seg_no);
                    deleteLIRL0303.put("vehicleNo", lv_vehicle_id);
                    deleteLIRL0303.put("vehicleId", lv_vehicle_id);
                    deleteLIRL0303.put("carTraceNo", lirl0301carTraceNo);
                    dao.delete(LIRL0303.DELETE, deleteLIRL0303);
                    //从装卸子表中删除数据
                    dao.delete(LIRL0305.DELETE, deleteLIRL0303);
                    //如果在超时表 从超时表中删掉
                    dao.delete(LIRL0403.DELETE, deleteLIRL0303);
                    //从车辆排序临时表删除
                    dao.delete(LIRL0310.DELETE, deleteLIRL0303);

                    //检查叫号表是否有数据
                    Map queryLIRL0402 = new HashMap();
                    queryLIRL0402.put("segNo", in_seg_no);
                    deleteLIRL0303.put("vehicleNo", lv_vehicle_id);
                    queryLIRL0402.put("carTraceNo", lirl0301carTraceNo);
                    List<LIRL0402> lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
                    if (lirl0402s.size() > 0) {
                        LIRL0402 lirl0402 = lirl0402s.get(0);
                        String lv_min_hand_point = lirl0402.getTargetHandPointId();
                        //如果在叫号表 从叫号表中删掉
                        dao.delete(LIRL0402.DELETE, queryLIRL0402);
                        //叫号表数据删除后，装卸点的进车跟踪要随之改变
                        outInfo = updateHandPointJobNumber(in_seg_no, lv_min_hand_point, 0, -1, 31);
                        if (outInfo.getStatus() < 0) {
                            return outInfo;
                        }
                    }
                }
            }
        }else {
            //清空待审核
            Map queryMapLirl0302 = new HashMap();
            queryMapLirl0302.put("segNo", in_seg_no);
            queryMapLirl0302.put("vehicleNo", lv_vehicle_id);
            queryMapLirl0302.put("notCarTraceNo", lv_car_trace_no);
            queryMapLirl0302.put("delFlag", 0);
            List<HashMap> queryMapLirl0302s = dao.query(LIRL0302.QUERY_LIRL0302, queryMapLirl0302);
            if (CollectionUtils.isNotEmpty(queryMapLirl0302s)){
                String segNo = MapUtils.getString(queryMapLirl0302s.get(0), "segNo", "");
                String reservationNumber = MapUtils.getString(queryMapLirl0302s.get(0), "reservationNumber", "");
                String uuid = MapUtils.getString(queryMapLirl0302s.get(0), "uuid", "");
                //撤销登记
                //撤销登记表
                Map updateLIRL0302 = new HashMap();
                updateLIRL0302.put("segNo", segNo);
                updateLIRL0302.put("status", "00");
                updateLIRL0302.put("sysRemark", "车辆重复登记,撤销此次登记");// 备注
                updateLIRL0302.put("remark", "车辆重复登记,撤销此次登记");// 备注
                updateLIRL0302.put("reservationNumber", reservationNumber);// 备注
                updateLIRL0302.put("uuid",uuid);// 备注
                updateLIRL0302.put("recReviseTime",DateUtil.curDateTimeStr14());// 备注
                dao.update("LIRL0302.updateTranslation1", updateLIRL0302);
            }

        }

        return outInfo;
    }

    /***
     * 撤销车辆登记进厂信息
     * @return
     */
    public EiInfo revokeTheRegistrationAndEnterTheFactory(EiInfo inInfo){
        List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
        for (HashMap hashMap : listHashMap){
            EiInfo outInfo = new EiInfo();
            String vehicleNo = "";//车牌号
            String stringDate = DateUtil.curDateTimeStr14();//修改时间
            String segNo = MapUtils.getString(hashMap,"segNo","");
            String checkId = MapUtils.getString(hashMap,"checkId","");//车辆流水号
            String carTraceNo = MapUtils.getString(hashMap,"carTraceNo","");//车辆跟踪号
            String lirl0302status = MapUtils.getString(hashMap,"status","");//车辆登记单状态
            if (StringUtils.isBlank(carTraceNo)||StringUtils.isBlank(checkId)){
                String massage = "该数据未进厂登记,请使用登记驳回!";
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(massage);
                return outInfo;
            }
            if (!"20".equals(lirl0302status)){
                String massage = "只有审批状态的车辆登记，可以取消登记!";
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(massage);
                return outInfo;
            }
            Map queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("carTraceNo", carTraceNo);
            queryMap.put("checkId", checkId);
            queryMap.put("delFlag", 0);
            List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY, queryMap);
            if (lirl0302s.size() > 0) {
                LIRL0302 lirl0302 = lirl0302s.get(0);
                vehicleNo = lirl0302.getVehicleNo();
                carTraceNo = lirl0302.getCarTraceNo();
            }
            queryMap = new HashMap();
            queryMap.put("segNo", segNo);
            queryMap.put("vehicleNo", vehicleNo);
            queryMap.put("carTraceNo", carTraceNo);
            queryMap.put("delFlag", 0);
            List<HashMap> lirl0301s = this.dao.query("LIRL0301.queryDuplicateRegistration", queryMap);
            for (HashMap hashMap2 : lirl0301s) {
                String status = MapUtils.getString(hashMap2, "status", "");
                String lirl0301carTraceNo = MapUtils.getString(hashMap2, "carTraceNo", "");// 车辆追踪号
                Integer statusNum = new Integer(status);
                if (statusNum == 10) {
                    //如果车辆已经登记一次，但是车辆只在排队还没有>=叫号，是可以重复登记，将之前的登记数据撤销就行，重新插入一条
                    //取消登记同步撤销预排序数据
                    //删除预排序主表
                    Map deleteLIRL0303 = new HashMap<>();
                    deleteLIRL0303.put("segNo", segNo);
                    // deleteLIRL0303.put("vehicleNo", vehicleNo);
                    // deleteLIRL0303.put("vehicleId", vehicleNo);
                    deleteLIRL0303.put("carTraceNo", lirl0301carTraceNo);
                    RecordUtils.setRevisor(deleteLIRL0303);
                    dao.delete(LIRL0303.DELETE, deleteLIRL0303);
                    //从装卸子表中删除数据
                    dao.delete(LIRL0305.DELETE, deleteLIRL0303);
                    //如果在超时表 从超时表中删掉
                    dao.delete(LIRL0403.DELETE, deleteLIRL0303);
                    //从车辆排序临时表删除
                    dao.delete(LIRL0310.DELETE, deleteLIRL0303);

                    //撤销车辆跟踪表
                    RecordUtils.setRevisor(hashMap2);
                    dao.delete(LIRL0301.DELETE, hashMap2);

                    //删除车辆排序主表
                    Map deleteLIRL0401 = new HashMap();
                    deleteLIRL0401.put("segNo",segNo);
                    deleteLIRL0401.put("carTraceNo",lirl0301carTraceNo);
                    // deleteLIRL0401.put("vehicleNo",vehicleNo);
                    dao.delete(LIRL0401.DELETE,deleteLIRL0401);

                    //删除车辆排序主表
                    Map deleteLIRL0403 = new HashMap();
                    deleteLIRL0403.put("segNo",segNo);
                    deleteLIRL0403.put("carTraceNo",lirl0301carTraceNo);
                    // deleteLIRL0403.put("vehicleNo",vehicleNo);
                    dao.delete(LIRL0403.DELETE,deleteLIRL0403);

                    //撤销登记表
                    Map updateLIRL0302 = new HashMap();
                    updateLIRL0302.put("segNo", segNo);
                    updateLIRL0302.put("status", "00");
                    RecordUtils.setRevisor(updateLIRL0302);
                    updateLIRL0302.put("sysRemark", "车辆重复登记,撤销此次登记");// 备注
                    updateLIRL0302.put("carTraceNo", lirl0301carTraceNo);// 车辆追踪号
                    dao.update("LIRL0302.updateTranslation", updateLIRL0302);

                    //检查叫号表是否有数据
                    Map queryLIRL0402 = new HashMap();
                    queryLIRL0402.put("segNo",segNo);
                    // queryLIRL0402.put("vehicleNo",vehicleNo);
                    queryLIRL0402.put("carTraceNo",lirl0301carTraceNo);
                    List<LIRL0402> lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
                    if (lirl0402s.size() > 0) {
                        LIRL0402 lirl0402 = lirl0402s.get(0);
                        String lv_min_hand_point = lirl0402.getTargetHandPointId();
                        //如果在叫号表 从叫号表中删掉
                        dao.delete(LIRL0402.DELETE, queryLIRL0402);
                        //叫号表数据删除后，装卸点的进车跟踪要随之改变
                        outInfo = updateHandPointJobNumber(segNo, lv_min_hand_point, 0, -1, 31);
                        if (outInfo.getStatus() < 0) {
                            return outInfo;
                        }
                    }
                    //删除补录的预约单号
                    Map deleteLIRL0201 = new HashMap();
                    deleteLIRL0201.put("segNo",segNo);
                    deleteLIRL0201.put("reservationNumber",MapUtils.getString(hashMap2,"reservationNumber",""));
                    RecordUtils.setRevisor(deleteLIRL0201);
                    dao.update(LIRL0201.DELETE_BY_RESERVATION_NUMBER,deleteLIRL0201);

                } else if (statusNum > 10 && statusNum < 50) {
                    String massage = "车辆登记号:"+checkId+"车牌号:" + vehicleNo + "已进厂,不能撤销登记!";
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg(massage);
                    return outInfo;
                }
            }
        }


        return inInfo;
    }


    /***
     * 出库单查询
     */
    public EiInfo queryPutOutInfo(EiInfo eiInfo) {

        long l = System.currentTimeMillis(); //获取时间戳效率最高
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String format = dateFormat.format(l);//当前日期

        String segNo = (String) eiInfo.get("segNo");
        String idCard = (String) eiInfo.get("idCard"); //身份信息
        if (StringUtils.isBlank(segNo)||StringUtils.isBlank(idCard)){
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("账套、身份信息不能为空!");
            return eiInfo;
        }

        String driverName = (String) eiInfo.get("driverName"); //司机姓名
        String vehicleNoByBefore = (String) eiInfo.get("vehicleNo"); //车牌号
        String telNumByBefore = (String) eiInfo.get("telNum"); //手机号
        String voucherNumByBefore = (String) eiInfo.get("voucherNum"); //提单号
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("idCard",idCard);
        hashMap.put("driverName",driverName);
        String flag = (String) eiInfo.get("flag"); //下拉标记
        if ("10".equals(flag)){ //本日
            hashMap.put("putoutDate",format);
        }
        //根据条件筛选
        if (StringUtils.isNotBlank(vehicleNoByBefore)){
            hashMap.put("vehicleNo",vehicleNoByBefore);
        }
        if (StringUtils.isNotBlank(telNumByBefore)){
            hashMap.put("telNum",telNumByBefore);
        }
        if (StringUtils.isNotBlank(voucherNumByBefore)){
            hashMap.put("voucherNum",voucherNumByBefore);
        }
        List<LIRL0308> lirl0308List = this.dao.query(LIRL0308.QUERY_POUT_INFO, hashMap);
        List<Map> hashMaps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lirl0308List)){
            for (LIRL0308 lirl0308 : lirl0308List) {
                Map map = lirl0308.toMap();
                hashMaps.add(map);
            }
        }
        eiInfo.set("list",hashMaps);
        return eiInfo;
    }

    /**
     * 出库单打印
     * @param eiInfo
     * @return
     */
    public EiInfo  saleBillPrint (EiInfo eiInfo){


        try {
            String segNo ="";
            boolean flag=false;
            //根据司机信息、车牌号查询车辆作业实绩表
            List<Map> result = (List)eiInfo.get("result");
            List<Map> resultAdd = new ArrayList<>();
            List<String> putoutIdAdd = new ArrayList<>();
            List<String>  vehicleNoAdd= new ArrayList<>();
            if (CollectionUtils.isEmpty(result)){
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("至少选中一条数据进行打印!");
                return eiInfo;
            }

            //组装list
            List<String> addList = new ArrayList<>();
            List<String> urlList = new ArrayList<>();
            List<String> addUrlList = new ArrayList<>();
            List<Map> resultList = new ArrayList<>();
            List<String> docUrlList = new ArrayList<>();
            List<String> ladingList = new ArrayList<>();
            List<String> printPutoutIdList = new ArrayList<>();
            boolean printFlag = false;

            //获取出库单号、账套调用出库单查询服务
            for (Map map : result) {
                String putoutId = MapUtils.getString(map, "putoutId", "");//出库单号
                segNo = MapUtils.getString(map, "segNo", "");//系统账套
                String packId = MapUtils.getString(map, "packId", "");//捆包号
                String vehicleNo = MapUtils.getString(map, "vehicleNo", "");//车牌号
                String ladingBillId = MapUtils.getString(map, "voucherNum", "");//提单号
                String customerId = MapUtils.getString(map, "customerId", "");//提单号
                String customerName = MapUtils.getString(map, "customerName", "");//提单号

                putoutIdAdd.add(putoutId);
                //打印只能打印一次，如果已经打印过，则不能打印
                //该出库单、装车清单已经打印过1次，不允许自助重新打印；若需打印，请联系仓库人员人工打印。
                HashMap<String, Object> hashMapLirl0308 = new HashMap<>();
                hashMapLirl0308.put("segNo", segNo);
                hashMapLirl0308.put("putoutId", putoutId);
                List<String> queryPrintCount = this.dao.query(LIRL0308.QUERY_PRINT_COUNT, hashMapLirl0308);
                if (Integer.parseInt(queryPrintCount.get(0)) > 0) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该出库单、装车清单(" + putoutId + ")已经打印过1次，不允许自助重新打印；若需打印，请联系仓库人员人工打印。");
                    return eiInfo;
                }

                //判断是否是江淮的客户，江淮客户需要打印装车清单
                //查询装车清单客户配置表
                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("segNo", segNo);
                hashMap.put("customerId", customerId);
                hashMap.put("customerName", customerName);
                List<Integer> query = this.dao.query(LIRL0302.QUERY_CUSTOMER_INFO, hashMap);
                // String is_it_a_jianghuai_customer = new SwitchUtils().getProcessSwitchValue(segNo, "IS_IT_A_JIANGHUAI_CUSTOMER", dao);
                if (query.get(0) > 0) {
                    addList.add(putoutId);
                    if (StringUtils.isNotBlank(ladingBillId)) {
                        ladingList.add(ladingBillId);
                    }
                    flag = true;
                } else {
                    //根据捆包号、材料管理号、提单号查询出库服务
                    EiInfo outInfo1 = new EiInfo();
                    outInfo1.set("segNo", segNo);
                    outInfo1.set("putoutId", putoutId);//提单号
                    // outInfo1.set("packId", packId);//捆包
                    outInfo1.set("offset", "0");
                    outInfo1.set("limit", "100");
                    outInfo1.set("redFlag", "0");
                    outInfo1.set(EiConstant.serviceId, "S_UC_EW_0287");
                    EiInfo outInfo0287 = EServiceManager.call(outInfo1, TokenUtils.getXplatToken());
                    if (outInfo0287.getStatus() == -1) {
                        eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                        eiInfo.setMsg("调用物流中心返回报错,出库单查询服务失败:" + outInfo0287.getMsg());
                        throw new RuntimeException(outInfo0287.getMsg());
                    }
                    List<Map> result0287 = (List<Map>) outInfo0287.get("result");
                    if (CollectionUtils.isNotEmpty(result0287)) {
                            for (Map map0287 : result0287) {
                                resultList.add(map0287);
                            }
                    }
                    if (StringUtils.isNotBlank(ladingBillId)) {
                        ladingList.add(ladingBillId);
                    }

                }
            }

            //出库单打印
            if (CollectionUtils.isNotEmpty(resultList)) {
                String uuids = "";
                String putOutIds = "";
                int size = resultList.size();
                int count = 1;
                for (Map map1 : resultList) {
                    String uuid = MapUtils.getString(map1, "uuid", "");//出库单子项号
                    String putOutId = MapUtils.getString(map1, "putoutId", "");//出库单子项号
                    segNo = MapUtils.getString(map1, "segNo", "");//出库单子项号
                    if (count < size) {
                        uuids += uuid.concat("','");
                        putOutIds += putOutId.concat("','");
                    } else {
                        uuids += uuid;
                        putOutIds += putOutId;
                    }
                    count++;
                }

                String reportUrl = PlatApplicationContext.getProperty("billPrint") + PlatApplicationContext.getProperty("P.printParam") + "&uuid=" + uuids + "&putoutStackingRecNum=" + putOutIds + "&segNo=" + segNo + "&format=PDF";
                addUrlList.add(reportUrl);

            }

            if (flag){
                String msg ="该客户需要打印装车清单，尚未生成。请耐心等待，稍后再尝试打印；或联系仓库人员加快制作装车清单!";
                //根据账套、出库单号打印装车清单
                EiInfo outInfo2 = new EiInfo();
                outInfo2.set("segNo", segNo);
                outInfo2.set("putoutStackingRecNumList", addList);//出库单集合
                outInfo2.set(EiConstant.serviceId, "S_UC_PR_0207"); //装车清单服务
                EiInfo outInfo0207 = EServiceManager.call(outInfo2, TokenUtils.getXplatToken());
                if (outInfo0207.getStatus() == -1) {
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("调用物流中心返回报错,调用装车清单服务查询失败:" + outInfo0207.getMsg());
                    throw new RuntimeException(outInfo0207.getMsg());
                }

                List<HashMap> result0207 = outInfo0207.getBlock("result").getRows();
                if (CollectionUtils.isEmpty(result0207)&&result0207.size()<=0){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("调用物流中心返回报错,调用装车清单服务查询失败:" + msg);
                    throw new RuntimeException(msg);
                }else {
                    List<String> putoutStackingRecNum = result0207.stream().map(map -> (String)map.get("putoutStackingRecNum")).collect(Collectors.toList());
                    List<String> collect = putoutStackingRecNum.stream().distinct().collect(Collectors.toList());
                    boolean equals = addList.equals(collect); //两个列表元素个数相等且元素内容一致
                    // if (equals){
                            //获取装车清单号，调用装车清单服务打印
                            //过滤相同的装车清单号
                            for (HashMap hashMap : result0207) {
                                Map<String, String> hashMapAdd = new HashMap<>();
                                hashMapAdd.put("billNo",MapUtils.getString(hashMap,"billNo"));
                                hashMapAdd.put("putoutStackingRecNumMom",MapUtils.getString(hashMap,"putoutStackingRecNum"));
                                hashMapAdd.put("segNo",MapUtils.getString(hashMap,"segNo"));
                                resultAdd.add(hashMapAdd);
                            }
                            EiInfo outInfoW = new EiInfo();
                            outInfoW.set("row", resultAdd.stream().distinct().collect(Collectors.toList()));
                            outInfoW.set(EiConstant.serviceId, "S_UC_PR_0206"); //装车清单服务
                            EiInfo outInfo0208 = EServiceManager.call(outInfoW, TokenUtils.getXplatToken());
                            if (outInfo0208.getStatus() == -1) {
                                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                                eiInfo.setMsg("调用物流中心返回报错,装车清单服务打印服务失败:" + outInfo0208.getMsg());
                                throw new RuntimeException(outInfo0208.getMsg());
                            }
                            docUrlList = (List<String>) outInfo0208.get("docUrlList");
                            addUrlList.addAll(docUrlList);
                            // eiInfo.set("docUrlList",addUrlList);
                }
            }
//            组装附件信息
            List<HashMap> resultFile = new ArrayList<>();
            //查询提单的附件信息
            List<String> collectLadingList = ladingList.stream().distinct().collect(Collectors.toList());
            //查询附件信息
            if (CollectionUtils.isNotEmpty(collectLadingList)) {
                HashMap<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("segNo", segNo);
                objectObjectHashMap.put("voucherNum", collectLadingList);
                List<HashMap> LIRL0316List = dao.query("LIRL0316.queryFilePath", objectObjectHashMap);
                if (CollectionUtils.isNotEmpty(LIRL0316List)) {
                    for (HashMap hashMap : LIRL0316List) {
                    String attachmentPrint = MapUtils.getString(hashMap, "attachmentPrint");
                    //根据打印机类型查询打印机IP,端口
                        if (StringUtils.isNotBlank(attachmentPrint)){
                            HashMap<Object, Object> objectObjectHashMap1 = new HashMap<>();
                            objectObjectHashMap1.put("attachmentPrint", attachmentPrint);
                            objectObjectHashMap1.put("segNo", segNo);
                            objectObjectHashMap1.put("billPrintType", 20);
                            List<LIRL0506> query = this.dao.query(LIRL0506.QUERY, objectObjectHashMap1);
                            if (CollectionUtils.isNotEmpty(query)){
                                String printerIpAddr = query.get(0).getPrinterIpAddr();
                                String printerPort = query.get(0).getPrinterPort();
                                hashMap.put("printName",printerIpAddr);
                                hashMap.put("printPort",printerPort);
                                resultFile.add(hashMap);
                            }
                        }
                    }
                }
            }
            List<String> collect = addUrlList.stream().distinct().collect(Collectors.toList());
            //组装URL
            //查询生效的出库单打印机配置
            String printName1 = findPrintName(segNo,"20");
            for (String s : collect) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("printName",printName1);
                hashMap.put("uploadFilePath",s);
                resultFile.add(hashMap);
            }


            // String remoteUrlString="http://**********:9100/";
             // String remoteUrlParam="{\"orientation\":\"0\",\"marginLeft\":\"0\",\"marginRight\":\"0\",\"marginTop\":\"0\",\"marginBottom\":\"0\",\"height\":\"120\",\"width\":\"100\",\"printServiceName\":\"NPIE1166C (HP LaserJet M403dw)\"}";
            //判断自动打印开关是否开启
            String ifRemotePrint = new SwitchUtils().getProcessSwitchValue(result.get(0).get("segNo").toString(), "IF_REMOTE_PRINT", dao);
            String printName="";
            String printPort="";
            if ("1".equals(ifRemotePrint)){
                for (HashMap url : resultFile) {
                    printName= (String) url.get("printName");
                    printPort= (String) url.get("printPort");
                    if (StringUtils.isNotBlank(printName)) {
                        printName = removeLastHyphen(printName);
                        System.out.println("出库单打印打印机名称：" + printName);
                    }
                    uploadPrintFile(MapUtils.getString(url, "uploadFilePath"), "/"+segNo + ".pdf", segNo, "URLTC",printName,printPort);
                    eiInfo.set("docUrlList",resultFile);
                }
            }else {
                eiInfo.set("docUrlList",resultFile);
            }
//            更新打印次数
//            HashMap<Object, Object> hashMapLirl0308 = new HashMap<>();
//            hashMapLirl0308.put("printCount",1);
//            hashMapLirl0308.put("segNo",segNo);
//            hashMapLirl0308.put("putoutId",putoutIdAdd);
//            //修改打印次数
//            this.dao.update(LIRL0308.UPDATE_PRINT_COUNT, hashMapLirl0308);
        } catch (RuntimeException e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }

        return eiInfo;
    }

    /***
     * 打印机配置查询
     * @param segNo
     * @return
     */
    private String findPrintName(String segNo,String billPrintType) {
        String printerName="";
        String printerPort="";
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("segNo", segNo);
        objectObjectHashMap.put("billPrintType",billPrintType);
        objectObjectHashMap.put("status","20");
        List<LIRL0506> queryLIRL0506 = this.dao.query(LIRL0506.QUERY, objectObjectHashMap);
        if (CollectionUtils.isNotEmpty(queryLIRL0506)){
            printerName = queryLIRL0506.get(0).getPrinterIpAddr();
            printerPort = queryLIRL0506.get(0).getPrinterPort();
        }
        return printerName+"-"+printerPort;
    }

    /***
     * 获取出库单子项信息
     * @param eiInfo
     * @param putoutId 出库单ID
     * @param segNo 账套
     * @param packId 捆包
     * @param ladingBillId 提单号
     * @return
     */
    private List<HashMap> getPutOutSubInfo(EiInfo eiInfo, String putoutId, String segNo, String packId, String ladingBillId) {
        EiInfo outInfo0288;
        EiInfo outInfo2 = new EiInfo();
        outInfo2.set("segNo", segNo);
        outInfo2.set("ladingBillId", ladingBillId);//提单号
        outInfo2.set("packId", packId);//捆包号
        outInfo2.set("putoutId", putoutId);//出库单号
        outInfo2.set("offset", "0");
        outInfo2.set("limit", "100");
        outInfo2.set("redFlag", "0");
        outInfo2.set(EiConstant.serviceId, "S_UC_EW_0288");
        outInfo0288 = EServiceManager.call(outInfo2, TokenUtils.getXplatToken());
        if (outInfo0288.getStatus() == -1) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg("调用物流中心返回报错:" + outInfo0288.getMsg());
            throw new RuntimeException(eiInfo.getMsg());
        }
        List<HashMap> subResult = (List<HashMap>) outInfo0288.get("result");
        return subResult;
    }

    /***
     * 新增车辆作业实绩表
     * @param segNo 账套
     * @param voucherNum 提单号
     * @param queryLIRL0307 数据集
     * @param map 集合
     * @return
     */
    private Map<String, Object> insertLirl0308(String segNo, String voucherNum,List<LIRL0307> queryLIRL0307, HashMap map) {
        String prodTypeId = queryLIRL0307.get(0).getProdTypeId();//品种附属码
        String specDesc = queryLIRL0307.get(0).getSpecDesc();//规格
        String shopsign = queryLIRL0307.get(0).getShopsign();//牌号
        BigDecimal weight = queryLIRL0307.get(0).getWeight();//重量
        BigDecimal quantity = queryLIRL0307.get(0).getQuantity();//数量
        String customerId = queryLIRL0307.get(0).getCustomerId();//客户代码
        String customerName = queryLIRL0307.get(0).getCustomerName();//客户名称
        String locationId = queryLIRL0307.get(0).getLocationId();//库位代码
        String locationName = queryLIRL0307.get(0).getLocationName();//库位名称

        Map<String, Object> hashMaptlirl0308 = new HashMap<>();
        hashMaptlirl0308.put("segNo", segNo);
        hashMaptlirl0308.put("unitCode", segNo);
        hashMaptlirl0308.put("voucherNum", voucherNum);
        hashMaptlirl0308.put("putinId",MapUtils.getString(map,"putinId",""));
        String deliverType = MapUtils.getString(map, "deliverType", "");
        if ("10".equals(deliverType)){
            hashMaptlirl0308.put("deliverName","自提");
        }else if ("20".equals(deliverType)){
            hashMaptlirl0308.put("deliverName","代运");
        }
        hashMaptlirl0308.put("prodTypeId", prodTypeId);
        hashMaptlirl0308.put("specDesc", specDesc);
        hashMaptlirl0308.put("shopsign", shopsign);
        hashMaptlirl0308.put("weight",weight);
        hashMaptlirl0308.put("quantity", quantity);
        hashMaptlirl0308.put("customerId", customerId);
        hashMaptlirl0308.put("customerName", customerName);
        hashMaptlirl0308.put("locationId", locationId);
        hashMaptlirl0308.put("locationName", locationName);

        // //判断出库单是否存在车辆作业实绩表
        // // int count = super.count(LIRL0308.QUERY_COUNT, hashMaptlirl0308);
        // if (count<=0){
        //     this.dao.insert(LIRL0308.INSERT,hashMaptlirl0308);
        // }
        return hashMaptlirl0308;
    }

    /***
     * 车辆进厂
     * 存储过程：prog.wl_vehicle_plan_mgr.vehicle_into_factory_flag
     * @return
     */
    public EiInfo vehicleIntoFactoryFlag(EiInfo inInfo) {
        List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
        EiInfo outInfo = new EiInfo();
        String stringDate = DateUtil.curDateTimeStr14();//修改时间
        Map in_modi_person = new HashMap();
        for (HashMap hashMap:listHashMap){
            String in_seg_no = MapUtils.getString(hashMap, "segNo", "");//系统账套号
            String in_vehicle_no = MapUtils.getString(hashMap, "vehicleNo", "");//车牌号
            String in_car_trace_no = MapUtils.getString(hashMap, "carTraceNo", "");//车辆跟踪号
            String factoryType = MapUtils.getString(hashMap, "factoryType", "");//10：首次进厂 20：厂内周转
            String userId = UserSession.getUserId();//创建人
            String loginCName = UserSession.getLoginCName();// 创建人姓名
            in_modi_person.put("userId",userId);
            in_modi_person.put("loginCName",loginCName);
            if (!"20".equals(factoryType)){
                String intoFactoryId = "";//车辆进厂流水号
                //根据传过来的车辆跟踪号来判断是常驻车还是平常进来的车
                //如果车辆跟踪号为空，为常驻车
                //车辆跟踪号不为空说明是正常的车辆
                if (StringUtils.isEmpty(in_car_trace_no)) {
                    //先根据传过来的车牌号来看下这辆车是否已经进厂但是未出厂
                    Map queryLIRL0301 = new HashMap();
                    queryLIRL0301.put("segNo", in_seg_no);
                    queryLIRL0301.put("vehicleNo", in_vehicle_no);
                    queryLIRL0301.put("statusListStr", "'20','30','40'");
                    queryLIRL0301.put("delFlag", "0");
                    int count = super.count(LIRL0301.COUNT, queryLIRL0301);
                    if (count > 0) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("该车辆已经进厂未出厂，无法再次进厂");
                        throw new RuntimeException(outInfo.getMsg());
                    }

                } else {
                    //先根据传过来的车牌号来看下这辆车是否已经进厂但是未出厂
                    Map queryLIRL0301 = new HashMap();
                    queryLIRL0301.put("segNo", in_seg_no);
                    queryLIRL0301.put("vehicleNo", in_vehicle_no);
                    queryLIRL0301.put("carTraceNo", in_car_trace_no);
                    queryLIRL0301.put("statusListStr", "'20','30','40'");
                    queryLIRL0301.put("delFlag", "0");
                    int count = super.count(LIRL0301.COUNT, queryLIRL0301);
                    if (count > 0) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("该车辆已经进厂未出厂，无法再次进厂");
                        throw new RuntimeException(outInfo.getMsg());
                    }
                    //车辆跟踪号不为空说明为一般的车   则直接生成数据
                    //查询的跟踪表的数据添加到进厂表中
                    queryLIRL0301.put("statusListStr", "");
                    queryLIRL0301.put("statusNotListStr", "'20','30','40'");
                    List<LIRL0301> lirl0301s = dao.query(LIRL0301.QUERY, queryLIRL0301);
                    if (lirl0301s.size() > 0) {
                        LIRL0301 lirl0301 = lirl0301s.get(0);
                        String segNo = lirl0301.getSegNo();
                        String vehicleNo = lirl0301.getVehicleNo();
                        //查询车辆进场登记表获取数据来源
                        Map queryLIRL0302 = new HashMap<>();
                        queryLIRL0302.put("segNo", segNo);
                        queryLIRL0302.put("vehicleNo", vehicleNo);
                        queryLIRL0302.put("carTraceNo", in_car_trace_no);
                        queryLIRL0302.put("delFlag", "0");
                        List<LIRL0302> lirl0302s = dao.query(LIRL0302.QUERY, queryLIRL0302);
                        String checkSource = "";
                        if (lirl0302s.size() > 0) {
                            LIRL0302 lirl0302 = lirl0302s.get(0);
                            checkSource = lirl0302.getCheckSource();
                        }
                        Map insertLIRL0405 = new HashMap();
                        String strSeqTypeId = "TLIRL_SEQ0405";//车辆进厂流水号
                        Date date = new Date(System.currentTimeMillis());
                        String[] args = {segNo.substring(0, 2), date.toString(), ""};
                        intoFactoryId = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                        insertLIRL0405.put("segNo", segNo);
                        insertLIRL0405.put("unitCode", lirl0301.getUnitCode());
                        insertLIRL0405.put("intoFactoryId", intoFactoryId);
                        insertLIRL0405.put("vehicleNo", lirl0301.getVehicleNo());
                        insertLIRL0405.put("status", "10");
                        insertLIRL0405.put("intoFactoryDate", stringDate);
                        insertLIRL0405.put("cancelIntoFactoryDate", "");
                        insertLIRL0405.put("carTraceNo", lirl0301.getCarTraceNo());
                        insertLIRL0405.put("dateSource", checkSource);
                        insertLIRL0405.put("targetHandPointId", lirl0301.getTargetHandPointId());
                        insertLIRL0405.put("factoryArea", lirl0301.getFactoryArea());
                        insertLIRL0405.put("forceFlag", "0");
                        insertLIRL0405.put("handType", lirl0301.getHandType());
                        RecordUtils.setCreator(insertLIRL0405);
                        insertLIRL0405.put("delFlag", "0");// 记录删除标记
                        insertLIRL0405.put("uuid", StrUtil.getUUID());// uuid
                        insertLIRL0405.put("factoryType", "10");// 10：首次进厂 20：厂内周转
                        LIRL0405 lirl0405 = new LIRL0405();
                        lirl0405.fromMap(insertLIRL0405);
                        dao.insert(LIRL0405.INSERT, lirl0405);
                    } else {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("查询不到车辆追踪单信息");
                        throw new RuntimeException(outInfo.getMsg());
                    }

                }

                //修改跟踪表的状态
                Map updateLIRL0301 = new HashMap();
                updateLIRL0301.put("segNo",in_seg_no);
                updateLIRL0301.put("vehicleNo",in_vehicle_no);
                updateLIRL0301.put("carTraceNo",in_car_trace_no);
                updateLIRL0301.put("status","20");
                updateLIRL0301.put("delFlag","0");
                RecordUtils.setRevisor(updateLIRL0301);
                updateLIRL0301.put("recReviseTime", stringDate);// 修改时间
                updateLIRL0301.put("enterFactory", stringDate);// 入厂时间
                dao.update(LIRL0301.UPDATE_LIRL0301JC,updateLIRL0301);
                outInfo = proc_quene_call_by_intofactory(in_seg_no, intoFactoryId, in_modi_person);
                if (outInfo.getStatus() < 0) {
                    return outInfo;
                }
            }else{
                //20厂内周转
                //修改临时进厂表数据
                hashMap.put("status","00");
                hashMap.put("delFlag","1");
                RecordUtils.setRevisor(hashMap);
                dao.update(LIRL0415.UPDATE_STATUS,hashMap);
            }
        }
        // 返回成功状态和消息
        inInfo.setStatus(EiConstant.STATUS_DEFAULT);
        inInfo.setMsg("车辆进厂: 车辆进厂成功!");
        return outInfo;
    }


    /***
     * 车辆进厂后处理叫号数据过程
     * 存储过程：prog.wl_vehicle_quene_mgr.proc_quene_call_by_intofactory
     * @param in_seg_no 系统账套号
     * @param in_into_factory_id  车辆进厂流水号
     * @param in_modi_person  登录人信息
     * @return
     */
    public EiInfo proc_quene_call_by_intofactory(String in_seg_no, String in_into_factory_id, Map in_modi_person) {
        EiInfo outInfo = new EiInfo();
        String stringDate = DateUtil.curDateTimeStr14();//修改时间
        String userId = MapUtils.getString(in_modi_person, "userId", "");
        String loginCName = MapUtils.getString(in_modi_person, "loginCName", "");
        Map queryLIRL0405 = new HashMap();
        queryLIRL0405.put("segNo", in_seg_no);
        queryLIRL0405.put("intoFactoryId", in_into_factory_id);
        queryLIRL0405.put("status", "10");
        int count = super.count(LIRL0405.COUNT, queryLIRL0405);
        if (count > 0) {
            //查询车辆进厂表

            List<LIRL0405> lirl0405s = dao.query(LIRL0405.QUERY, queryLIRL0405);
            LIRL0405 lirl0405 = lirl0405s.get(0);
            /*String forceFlag = lirl0405.getForceFlag();//强制进厂标记（1代表强制进厂）*/

            //检查是否叫号过
            Map queryLIRL0402 = new HashMap();
            queryLIRL0402.put("segNo", lirl0405.getSegNo());
            queryLIRL0402.put("vehicleNo", lirl0405.getVehicleNo());
            queryLIRL0402.put("vehicleId", lirl0405.getVehicleNo());
            queryLIRL0402.put("carTraceNo", lirl0405.getCarTraceNo());
            int count1 = super.count(LIRL0402.COUNT, queryLIRL0402);
            //检查是否叫号过
            if (count1 == 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("车辆" + lirl0405.getVehicleNo() + "没有叫号,不能进厂!");
                throw new RuntimeException(outInfo.getMsg());
            }
            //重庆没有进厂登记信息
            if("JC000000".equals(in_seg_no)||"JD000000".equals(in_seg_no)){

            }else {
                //查询进场登记表
                Map queryLIRL0302 = new HashMap();
                queryLIRL0302.put("segNo", lirl0405.getSegNo());
                queryLIRL0302.put("vehicleNo", lirl0405.getVehicleNo());
                queryLIRL0402.put("vehicleId", lirl0405.getVehicleNo());
                queryLIRL0302.put("carTraceNo", lirl0405.getCarTraceNo());
                int count2 = super.count(LIRL0302.COUNT, queryLIRL0302);
                if (count2 == 0) {
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    outInfo.setMsg("车辆" + lirl0405.getVehicleNo() + "没有登记,不能进厂!");
                    throw new RuntimeException(outInfo.getMsg());
                }
            }

            //查询叫号主表
            List<LIRL0402> lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
            LIRL0402 lirl0402 = lirl0402s.get(0);
            //同步更新装卸点进车跟踪表和通道作业跟踪表
            outInfo = updateHandPointJobNumber(in_seg_no, lirl0402.getTargetHandPointId(), 0, 0, 20);
            if (outInfo.getStatus() < 0) {
                return outInfo;
            }
            //查看此数据是否为超时数据，如果之前超时过，则从超时表中删除数据，备份到叫号备份表中
            Map queryLIRL0403 = new HashMap();
            queryLIRL0403.put("segNo",in_seg_no);
            queryLIRL0403.put("vehicleNo", lirl0405.getVehicleNo());
            queryLIRL0403.put("vehicleId", lirl0405.getVehicleNo());
            queryLIRL0403.put("carTraceNo", lirl0405.getCarTraceNo());
            int count2 = super.count(LIRL0403.COUNT, queryLIRL0403);
            //超时表中存在 记录下超时次数
            String overtimeCount = "0";
            if (count2>0){
                List<LIRL0403> lirl0403s = dao.query(LIRL0403.QUERY, queryLIRL0403);
                LIRL0403 lirl0403 = lirl0403s.get(0);
                overtimeCount = lirl0403.getOvertimeCount();
            }
            //查询叫号主表
            lirl0402s = dao.query(LIRL0402.QUERY, queryLIRL0402);
            lirl0402 = lirl0402s.get(0);
            //插入备份主表
            Map insertLIRL0409 = lirl0402.toMap();
            insertLIRL0409.put("overtimeCount", overtimeCount);// 超时次数
            /*insertLIRL0409.put("recCreator", userId);// 创建人工号
            insertLIRL0409.put("recCreatorName", loginCName);// 创建人姓名
            insertLIRL0409.put("recCreateTime", stringDate);// 创建时间
            insertLIRL0409.put("recRevisor", userId);// 修改人工号
            insertLIRL0409.put("recRevisorName", loginCName);// 修改人姓名
            insertLIRL0409.put("recReviseTime", stringDate);// 修改时间*/
            RecordUtils.setCreator(insertLIRL0409);
            LIRL0409 lirl0409 = new LIRL0409();
            lirl0409.fromMap(insertLIRL0409);
            dao.insert(LIRL0409.INSERT,insertLIRL0409);
            //查询车辆排队叫号装卸点子表
            List<LIRL0305> lirl0305s = dao.query(LIRL0305.QUERY, queryLIRL0403);
            for (LIRL0305 lirl0305:lirl0305s){
                Map insertLIRL0404 = lirl0305.toMap();
                insertLIRL0404.put("vehicleNo", lirl0305.getVehicleId());// 车牌号
                insertLIRL0404.put("overtimeCount", overtimeCount);// 超时次数
                insertLIRL0404.put("backupDate", stringDate);// 备份日期
                /*insertLIRL0404.put("recCreator", userId);// 创建人工号
                insertLIRL0404.put("recCreatorName", loginCName);// 创建人姓名
                insertLIRL0404.put("recCreateTime", stringDate);// 创建时间
                insertLIRL0404.put("recRevisor", userId);// 修改人工号
                insertLIRL0404.put("recRevisorName", loginCName);// 修改人姓名
                insertLIRL0404.put("recReviseTime", stringDate);// 修改时间*/
                RecordUtils.setCreator(insertLIRL0404);
                LIRL0404 lirl0404 = new LIRL0404();
                lirl0404.fromMap(insertLIRL0404);
                dao.insert(LIRL0404.INSERT,lirl0404);
            }
            //把叫号表中的数据删除
            dao.delete(LIRL0402.DELETE,queryLIRL0402);
            //删除叫号子表
            dao.delete(LIRL0305.DELETE,queryLIRL0402);
            //从叫号超时表中删除数据
            dao.delete(LIRL0403.DELETE,queryLIRL0402);

        }

        return outInfo;
    }

    /***
     * PDA 强制叫号
     */
    public EiInfo forcedCall(EiInfo eiInfo){

        try {
            //获取账套、车牌号、车辆跟踪号
            String segNo = (String) eiInfo.get("segNo"); //账套
            String vehicleNo = (String) eiInfo.get("vehicleNo"); //车牌号
            String carTraceNo = (String) eiInfo.get("carTraceNo"); //车辆跟踪号
            //检查是否已经叫号了
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("segNo",segNo);
            hashMap.put("vehicleNo",vehicleNo);
            hashMap.put("carTraceNo",carTraceNo);
            List<LIRL0401> LIRL0401List = this.dao.query(LIRL0401.QUERY, hashMap);
            if (LIRL0401List.size()>0&&CollectionUtils.isEmpty(LIRL0401List)){
                eiInfo.setStatus(-1);
                eiInfo.setMsg("此车辆排队序列中已不存在,请重新查询数据!");
                return eiInfo;
            }
            for (LIRL0401 lirl0401 : LIRL0401List) {
                HashMap<Object, Object> hashMaplirl0402 = new HashMap<>();
                Map map = lirl0401.toMap();
                hashMaplirl0402.putAll(map);
                RecordUtils.setCreator(hashMap);
                hashMaplirl0402.put("uuid",UUIDUtils.getUUID());
                //不为空直接插入叫号表
                this.dao.insert(LIRL0402.INSERT,hashMaplirl0402);
                //更新装卸点进车跟踪数据
                updateHandPointJobNumber(segNo, lirl0401.getTargetHandPointId(), 0, 1, 10);
            }
        } catch (Exception e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }

    /***
     * PDA 查询车牌信息
     */

    public EiInfo findVehicleByPda(EiInfo eiInfo){


        String segNo = (String)eiInfo.get("segNo"); //目标装卸点
        String targetHandPointId = (String)eiInfo.get("targetHandPointId"); //目标装卸点
        //根据目标装卸点查询车辆跟踪表
        HashMap<Object, Object> hashMap = new HashMap<>();
        List<String> statusList = new ArrayList<>();
        statusList.add("20");
        statusList.add("40");
        hashMap.put("segNo",segNo);
        hashMap.put("handPointId",targetHandPointId);
        hashMap.put("statusList",statusList);
        List listLIRL0301 = this.dao.query(LIRL0301.QUERY, hashMap);
        eiInfo.getBlock("result").setRows(listLIRL0301);
        return eiInfo;


    }


    /**
     * 查询签章通知标题、正文
     * @param inInfo
     * @return
     * S_LI_RL_0065
     */
    public EiInfo querySignatureMarking(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        HashMap<String, String> hashMap = new HashMap<>();
        try {
            List<LIRL0109> queryLIRL0109=new ArrayList<>();
            String segNo = (String) inInfo.get("segNo");//账套
            String driverTel = (String) inInfo.get("driverTel");//账套
            String driverName = (String) inInfo.get("driverName");//账套
            String driverIdentity = (String) inInfo.get("driverIdentity");//账套
            hashMap.put("segNo",segNo);
            hashMap.put("signatureMarking","1");//签章标记
            hashMap.put("status","20");//签章标记
            hashMap.put("driverTel",driverTel);//签章标记
            hashMap.put("driverName",driverName);//签章标记
            hashMap.put("driverIdentity",driverIdentity);//签章标记
            //判断是否有6个月内的签章，有直接跳过没有新增
            List<String> queryLIRL0312 = this.dao.query(LIRL0312.QUERY_RELEVANCE_ID, hashMap);
            if (CollectionUtils.isNotEmpty(queryLIRL0312)){
                outInfo.set("list",queryLIRL0109);
            }else {
                queryLIRL0109 = dao.query(LIRL0109.QUERY_TITLE, hashMap);
                outInfo.set("list",queryLIRL0109);
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return outInfo;
    }

    /**
     * 保存签章通知标题、正文+签收图片为PDF
     *
     * @param inInfo
     * @return S_LI_RL_0068
     */
    public EiInfo signatureMarkingPDF(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        String segNo = (String) inInfo.get("segNo");// 账套
        //macbook 本地测试签名开关关闭
        if ("1".equals(new SwitchUtils().getProcessSwitchValue(segNo, "SIGNATURE_MARKING", dao))) {
            //承诺人姓名、身份证号、手机号
            String driverName = (String) inInfo.get("driverName");//承诺人姓名
            String idCard = (String) inInfo.get("idCard");//身份证号
            String tel = (String) inInfo.get("tel");//手机号
            String vehicleNo = (String) inInfo.get("vehicleNo");//车牌号

            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("segNo", segNo);
            queryParams.put("signatureMarking", "1");
            // 查询一体机签章标记的
            List<LIRL0109> lirl0109s = dao.query(LIRL0109.QUERY, queryParams);
            outInfo.setMsg("查询一体机签章标记");
            if (lirl0109s.size() > 0) {
                String currentWorkingDirectory = System.getProperty("user.dir");
                System.out.println("当前工作目录: " + currentWorkingDirectory);
                outInfo.setMsg("当前工作目录: " + currentWorkingDirectory);
                LIRL0109 lirl0109 = lirl0109s.get(0);
                // 通知标题
                String title = lirl0109.getNotificationTitle();
                // 通知正文
                String content = lirl0109.getNotificationText();
                // 获取当前时间并格式化为指定格式
                LocalDateTime now = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String signatureDateTime = now.format(formatter);
                try {
                    // 手写签名图片路径，这里需要替换成真实有效的路径
                    String base64String = (String) inInfo.get("file");
                    String base64String1 = (String) inInfo.get("file1");
                    CommonsMultipartFile cFile = Base64ToMultipartFileConverter.convertToMultipartFile(base64String);
                    // 创建 Document 对象，设置页面大小为 A4
                    Document document = new Document(PageSize.A4);
                    // 目录创建
                    String directoryPath = "/apps/upload";
                    File directory = new File(directoryPath);
                    if (!directory.exists()) {
                        if (directory.mkdirs()) {
                            System.out.println("目录创建成功");
                        } else {
                            System.out.println("目录创建失败");
                            outInfo.setMsg("目录创建失败：");
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            return outInfo;
                        }
                    }
                    // 相对路径示例，假设要在当前工作目录下的 pdfs 文件夹中创建一个名为 output.pdf 的文件
                    String relativePath = "/outPDF.pdf";
                    String storePath = ROOT_PATH+relativePath;
                    // 确保父目录存在，如果不存在则创建
                    Path filePath = Paths.get(storePath);
                    Path parentDir = filePath.getParent();
                    if (Files.notExists(parentDir)) {
                        try {
                            Files.createDirectories(parentDir);
                        } catch (IOException e) {
                            System.out.println("父目录创建失败：" + e.getMessage());
                            outInfo.setMsg("父目录创建失败：" + e.getMessage());
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            return outInfo;
                        }
                    }
                    // 创建文件而不是目录
                    File pdfFile = filePath.toFile();
                    if (pdfFile.exists() && pdfFile.isDirectory()) {
                        // 若文件已经存在且为目录，删除目录或修改存储路径
                        // 使用 java.nio.file.Files 来删除目录
                        try {
                            if (pdfFile.listFiles() == null || pdfFile.listFiles().length == 0) {
                                // 仅当目录为空时删除
                                Files.delete(pdfFile.toPath());
                                Files.createFile(filePath);
                            } else {
                                throw new PlatException("/apps/upload/outPDF.pdf 目录不为空，无法删除。");
                            }
                        } catch (IOException e) {
                            System.out.println("删除目录或创建文件失败：" + e.getMessage());
                            outInfo.setMsg("删除目录或创建文件失败：" + e.getMessage());
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            return outInfo;
                        }
                    }
                    try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
                        // 创建 PdfWriter 实例，指定输出的 PDF 文件路径
                        PdfWriter.getInstance(document, fos);
                        // 打开文档，准备写入内容
                        document.open();
                        outInfo.setMsg("已打开 PDF 文档");
                        // 设置中文字体，用于显示标题和正文（这里使用宋体，需确保系统有相应字体文件支持，也可使用其他字体）
                        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                        Font titleFont = new Font(baseFont, 18, Font.BOLD);
                        Font contentFont = new Font(baseFont, 12, Font.NORMAL);
                        // 添加标题
                        Paragraph titleParagraph = new Paragraph(title, titleFont);
                        titleParagraph.setAlignment(Element.ALIGN_CENTER);
                        titleParagraph.setSpacingAfter(15);  // 设置标题与正文的间距
                        document.add(titleParagraph);
                        // 添加正文
                        Paragraph contentParagraph = new Paragraph(content, contentFont);
                        contentParagraph.setAlignment(Element.ALIGN_JUSTIFIED);  // 两端对齐
                        contentParagraph.setSpacingAfter(20);  // 设置正文与签名的间距
                        document.add(contentParagraph);
                        // 添加手写签名图片
                        Image signatureImage = Base64ToMultipartFileConverter.base64StringToITextImage(base64String);
                        // 设置图片在页面中的位置和大小（以下示例为简单示意，可根据实际调整）
                        signatureImage.setAlignment(Element.ALIGN_RIGHT);
                        signatureImage.scalePercent(15);  // 缩放图片为原始大小的 50%

                        Image signatureImage1 = Base64ToMultipartFileConverter.base64StringToITextImage(base64String1);
                        signatureImage1.setAlignment(Element.ALIGN_CENTER);
                        signatureImage1.scalePercent(15);  // 缩放图片为原始大小的 50%

                        // 创建一个包含承诺人信息和签名图片的表格
                        float[] columnWidths = {100f, 100f,100f};
                        PdfPTable table = new PdfPTable(3);
                        table.setWidths(columnWidths);
                        table.setWidthPercentage(100);

                        // 创建左侧信息表格
                        PdfPTable leftTable = new PdfPTable(1);
                        leftTable.setWidthPercentage(100);

                        // 添加承诺人姓名
                        Paragraph driverNamePara = new Paragraph("承诺人姓名： " + driverName , contentFont);
                        leftTable.addCell(createNoBorderCell(driverNamePara));
                        // 添加身份证号
                        Paragraph vPara = new Paragraph("车牌号： " + vehicleNo, contentFont);
                        leftTable.addCell(createNoBorderCell(vPara));
                        // 添加身份证号
                        Paragraph idCardPara = new Paragraph("身份证号： " + idCard, contentFont);
                        leftTable.addCell(createNoBorderCell(idCardPara));

                        // 添加签名日期
                        Paragraph signatureDatePara = new Paragraph("签名日期： " + signatureDateTime, contentFont);
                        leftTable.addCell(createNoBorderCell(signatureDatePara));

                        // 将左侧信息表格添加到主表格的左侧单元格
                        PdfPCell leftCell = new PdfPCell(leftTable);
                        leftCell.setBorder(Rectangle.NO_BORDER);
                        table.addCell(leftCell);

                        //创建中间人脸照片
                        PdfPCell centerCell1 = new PdfPCell(signatureImage1, false);
                        centerCell1.setBorder(Rectangle.NO_BORDER);
                        centerCell1.setHorizontalAlignment(Element.ALIGN_CENTER);
                        table.addCell(centerCell1);

                        // 创建右侧签名图片单元格
                        PdfPCell rightCell = new PdfPCell(signatureImage, false);
                        rightCell.setBorder(Rectangle.NO_BORDER);
                        rightCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                        table.addCell(rightCell);


                        document.add(table);
                        // 关闭文档
                        document.close();
                        // 获取前端参数
                        CommonsMultipartFile file = Base64ToMultipartFileConverter.convertPDFToMultipartFile(storePath);
                        String id = inInfo.getString("id");
                        List<String> id2 = (List) inInfo.get("id2");
                        String relevanceType = "";
                        // 上传文件
                        String fileName = file.getOriginalFilename();
                        if (StrUtil.isBlank(fileName)) {
                            fileName = "";
                        }
                        // 生成文件 ID
                        String fileId = StrUtil.getUUID();
                        // 获取文件后缀
                        String suffix = fileName.substring(fileName.lastIndexOf("."));
                        // 转换文件名防止文件重复
                        // String storeName = fileId + suffix;
                        // 上传文件 - 本地
                        String downloadUrl = FileUtils.uploadFile(file, "/integratedMachine/" + segNo);
                        if (downloadUrl == null) {
                            throw new PlatException("文件上传失败");
                        }
                        // 待新增的附件记录
                        LIRL0312 lirl0312 = new LIRL0312();
                        lirl0312.setRelevanceId(id);
                        // 文件信息
                        lirl0312.setUploadFileName(fileName);
                        lirl0312.setFifleType(suffix);
                        lirl0312.setFifleSize(new BigDecimal(file.getSize()));
                        lirl0312.setFileId(fileId);
                        // 设置文件下载路径
                        lirl0312.setUploadFilePath(downloadUrl);
                        lirl0312.setRecCreator("System");
                        lirl0312.setRecCreatorName("System");
                        lirl0312.setRecRevisor("System");
                        lirl0312.setRecRevisorName("System");
                        lirl0312.setSignatureMark("2");
                        lirl0312.setSegNo(segNo);
                        lirl0312.setUnitCode(segNo);
                        lirl0312.setRecCreateTime(DateUtil.curDateTimeStr14());
                        lirl0312.setRecReviseTime(DateUtil.curDateTimeStr14());
                        // 新增到数据库
                        if (CollectionUtils.isNotEmpty(id2)) {
                            for (String string : id2) {
                                lirl0312.setUuid(StrUtil.getUUID());
                                lirl0312.setRelevanceType(string);
                                Map insMap = lirl0312.toMap();
                                dao.insert(LIRL0312.INSERT, insMap);
                            }
                        } else {
                            lirl0312.setUuid(StrUtil.getUUID());
                            lirl0312.setRelevanceType(id);
                            Map insMap = lirl0312.toMap();
                            dao.insert(LIRL0312.INSERT, insMap);
                        }
                        System.out.println("PDF 文件已成功生成！");
                        outInfo.setMsg("PDF 文件已成功生成！");
                        outInfo.set("fileUUID", fileId);
                        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                    } catch (DocumentException | IOException e) {
                        e.printStackTrace();
                        System.out.println("生成 PDF 文件时出现错误！");
                        outInfo.setMsg(e.getMessage());
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("处理签名标记 PDF 时出现错误");
                    outInfo.setMsg(e.getMessage());
                    outInfo.setStatus(EiConstant.STATUS_FAILURE);
                }

            } else {
                String massage = "签章通知为空!";
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(massage);
            }
        }
        return outInfo;
    }
    // 添加 createNoBorderCell 方法
    private PdfPCell createNoBorderCell(Paragraph paragraph) {
        PdfPCell cell = new PdfPCell(paragraph);
        cell.setBorder(Rectangle.NO_BORDER);
        return cell;
    }

    /**
     * 校验验证码是否正确
     *
     */
    public EiInfo checkVerificationCode(EiInfo inInfo)  {
        String messageCode = (String) inInfo.get("messageCode");
        String tel = (String) inInfo.get("driverTel");
        if (StrUtil.isBlank(messageCode)||StrUtil.isBlank(tel)){
            inInfo.setStatus(-1);
            inInfo.setMsg("手机号或者验证码不能为空!");
            return inInfo;
        }
        try {
            if (com.baosight.iplat4j.core.util.StringUtils.isNotEmpty(messageCode)){
                //获取数据库中的验证码
                Map queryLIRL0104 = new HashMap();
                queryLIRL0104.put("tel", tel);
                List<LIRL0104> lirl0104s = dao.query(LIRL0104.QUERY, queryLIRL0104);
                for (LIRL0104 lirl0104:lirl0104s){
                    String messageCode1 = lirl0104.getMessageCode();
                    long l = System.currentTimeMillis(); //获取时间戳效率最高
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyymmddhhmmss");
                    String format = dateFormat.format(l);
                    Integer effectiveTime = lirl0104.getEffectiveTime();//有效时间
                    Calendar cal = Calendar.getInstance();
                    Date recCreateTime = dateFormat.parse(lirl0104.getRecCreateTime());
                    cal.setTime(dateFormat.parse(lirl0104.getRecCreateTime()));
                    cal.add(Calendar.MINUTE, effectiveTime);
                    String format1 = dateFormat.format(cal.getTime());//验证码过期时间
                    Date parse = dateFormat.parse(format1);
                    Date now = dateFormat.parse(format);
                    if (messageCode.equals(messageCode1)){
                        //判断是否超时
                        int i = now.compareTo(parse);
                        if (i > 0) {
                            String massage = "验证码过期请重新获取!";
                            inInfo.setStatus(EiConstant.STATUS_FAILURE);
                            inInfo.setMsg(massage);
                            return inInfo;
                        }
                    }else {
                        String massage = "验证码不正确请重新输入!";
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }

                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("验证码正确.");
        } catch (ParseException e) {
            e.printStackTrace();
        }
            return inInfo;
    }

    /**
     * 下载并上传文件
     *
     * @param url
     * @param fileName
     */
    private void uploadPrintFile(String url, String fileName, String segNo, String itemCode,String printName,String printPort) {
        try {
            String otherProjectUrl = "";
            String printerName = "";
            //查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo",segNo);
            querytlirl0314.put("itemCode",itemCode);
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s.size()>0){
                LIRL0314 lirl0314 = lirl0314s.get(0);
                otherProjectUrl = lirl0314.getItemCname();
                printerName = lirl0314.getWarehouseName();
                System.out.println("打印打印机名称1："+printerName);
                if (StringUtils.isNotBlank(printName)&&StringUtils.isNotBlank(printPort)){ //附近制定打印机
                    printerName=printName;
                    System.out.println("打印打印机名称2："+printerName);
                }
            }

            if (otherProjectUrl.length()>0&&StringUtils.isBlank(printPort)){
                // 调用其他项目的 POST 接口，假设接口地址为 otherProjectUrl
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                // 构建请求体，这里传入 URL
                Map<String, String> requestBody = new HashMap<>();
                requestBody.put("url", url);
                requestBody.put("fileName", fileName);
                requestBody.put("printerName", printerName);
                if ("URLTC".equals(itemCode)){
                    requestBody.put("putFlag", "10");
                }else {
                    requestBody.put("putFlag", null);
                }
                HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
                ResponseEntity<String> response = restTemplate.exchange(otherProjectUrl, HttpMethod.POST, requestEntity, String.class);
                if (response.getStatusCode() == HttpStatus.OK) {
                    System.out.println("成功调用其他项目的 POST 接口，响应内容: " + response.getBody());
                } else {
                    System.err.println("调用其他项目的 POST 接口失败，状态码: " + response.getStatusCode());
                }
            }
            //通过IP 访问办公室打印机
            if (StringUtils.isNotBlank(printPort)){
                //调用方法
                PrintIPUtils.printMethod(url, printerName, Integer.valueOf(printPort),10000);
            }
            // 发起 HTTP 请求获取文件内容
            /*ResponseEntity<byte[]> rsp = restTemplate.getForEntity(url, byte[].class);*/


            /*byte[] fileContent = rsp.getBody();
            // 获取当前工作目录，并使用 File.separator 作为文件分隔符
            String directoryPath = "/apps/upload";
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                if (directory.mkdirs()) {
                    System.out.println("目录创建成功");
                } else {
                    System.out.println("目录创建失败");
                }
            }
            // 构建完整的文件存储路径
            fileName = directoryPath + fileName;
            // 将文件内容保存到本地
            saveFile(fileName, rsp.getBody());
            String printerName = "Brother HL-2260D";
            //printPDF(fileName, printerName,LANDSCAPE);
            //查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo",segNo);
            querytlirl0314.put("itemCode",itemCode);
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s.size()>0){
                LIRL0314 lirl0314 = lirl0314s.get(0);
                printPDF(fileName, lirl0314.getItemCname(), 9100,10000);
            }*/
        } catch (HttpClientErrorException e) {
            // 处理客户端错误，比如 400、404 等错误码对应的情况
            System.err.println("客户端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
        } catch (HttpServerErrorException e) {
            // 处理服务器端错误，比如 500 等错误码对应的情况
            System.err.println("服务器端错误: " + e.getStatusCode() + ", 错误信息: " + e.getMessage());
        } catch (Exception e) {
            // 处理其他通用异常情况
            System.err.println("其他异常: " + e.getMessage());
        }
    }


    public static String removeLastHyphen(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        int lastHyphenIndex = input.lastIndexOf("-");
        if (lastHyphenIndex == -1) {
            return input; // 没有找到连字符
        }

        return input.substring(0, lastHyphenIndex);
    }

    private void uploadPrintFile2(String url, String fileName, String segNo, String itemCode) {

        try {
            String otherProjectUrl = "";
            String printerName = "";
            //查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo",segNo);
            querytlirl0314.put("itemCode",itemCode);
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s.size()>0){
                LIRL0314 lirl0314 = lirl0314s.get(0);
                otherProjectUrl = lirl0314.getItemCname();
                printerName = lirl0314.getWarehouseName();
            }
            if (otherProjectUrl.length()>0){
                // 调用其他项目的 POST 接口，假设接口地址为 otherProjectUrl
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                // 构建请求体，这里传入 URL
                Map<String, String> requestBody = new HashMap<>();
                requestBody.put("url", url);
                requestBody.put("fileName", fileName);
                requestBody.put("printerName", printerName);
                HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
                ResponseEntity<String> response = restTemplate.exchange(otherProjectUrl, HttpMethod.POST, requestEntity, String.class);
                if (response.getStatusCode() == HttpStatus.OK) {
                    System.out.println("成功调用其他项目的 POST 接口，响应内容: " + response.getBody());
                } else {
                    System.err.println("调用其他项目的 POST 接口失败，状态码: " + response.getStatusCode());
                }
            }


            /*// 获取当前工作目录，并使用 File.separator 作为文件分隔符
            String directoryPath = "/apps/upload";
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                if (directory.mkdirs()) {
                    System.out.println("目录创建成功");
                } else {
                    System.out.println("目录创建失败");
                }
            }
            // 构建完整的文件存储路径
            fileName = directoryPath + fileName;
            // 保存文件
            try (BufferedInputStream in = new BufferedInputStream(new URL(url).openStream());
                 FileOutputStream fileOutputStream = new FileOutputStream(fileName)) {
                byte[] dataBuffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(dataBuffer, 0, 1024))!= -1) {
                    fileOutputStream.write(dataBuffer, 0, bytesRead);
                }
            } catch (IOException e) {
                // 处理文件下载过程中的异常，如网络错误、文件操作错误等
                System.err.println("文件下载异常: " + e.getMessage());
            }

            String printerName = "Brother HL-2260D";
            //printPDF(fileName, printerName,LANDSCAPE);
            // 查询配置表 获取打印机地址
            Map querytlirl0314 = new HashMap();
            querytlirl0314.put("segNo", segNo);
            querytlirl0314.put("itemCode", itemCode);
            List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
            if (lirl0314s!= null && lirl0314s.size() > 0) {
                LIRL0314 lirl0314 = lirl0314s.get(0);
                printPDF(fileName, lirl0314.getItemCname(), 9100, 10000);
            }*/
        } catch (Exception e) {
            // 处理其他通用异常情况
            System.err.println("其他异常: " + e.getMessage());
        }
    }

    private void saveFile(String fileName, byte[] content) {
        try (OutputStream outputStream = new FileOutputStream(new File(fileName))) {
            outputStream.write(content);
        } catch (IOException e) {
            System.err.println("保存文件时出错: " + e.getMessage());
        }
    }

    public static final int A4_WIDTH = 595;
    public static final int A4_HEIGHT = 842;
    public static final int LANDSCAPE = PageFormat.LANDSCAPE;
    public static final int PORTRAIT = PageFormat.PORTRAIT;
    public static final int PRINT_MARGIN = 36;

    public static void printPDF(String filePath, String printerName, int orientation) {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PrinterJob job = PrinterJob.getPrinterJob();
            PrintService[] printServices = PrintServiceLookup.lookupPrintServices(null, null);
            PrintService selectedPrinter = null;
            for (PrintService service : printServices) {
                if (service.getName().equals(printerName)) {
                    selectedPrinter = service;
                    break;
                }
            }
            if (selectedPrinter == null) {
                System.err.println("未找到指定的打印机：" + printerName);
                return;
            }
            job.setPrintService(selectedPrinter);
            PageFormat pageFormat = job.defaultPage();
            pageFormat.setOrientation(orientation);
            Paper paper = pageFormat.getPaper();
            paper.setSize(A4_WIDTH, A4_HEIGHT);
            double leftMargin = PRINT_MARGIN;
            double rightMargin = PRINT_MARGIN;
            double topMargin = PRINT_MARGIN;
            double bottomMargin = PRINT_MARGIN;
            double imageableWidth = A4_WIDTH - leftMargin - rightMargin;
            double imageableHeight = A4_HEIGHT - topMargin - bottomMargin;
            paper.setImageableArea(leftMargin, topMargin, imageableWidth, imageableHeight);
            pageFormat.setPaper(paper);

            // 调整缩放比例以适应页面大小
            PDFPrintable pdfPrintable = getScaledPDFPrintable(document, pageFormat);
            job.setPrintable(pdfPrintable, pageFormat);
            job.print();
        } catch (IOException | PrinterException e) {
            System.err.println("打印时出现异常：" + e.getMessage());
        }
    }
    public static void main(String[] args) throws IOException {
        // 下载文件
        String urlString = "http://**********:8081/upload/miniPrograms/KF000000/73F3961A5FD8420FB562A45832F69318.xlsx";
        String fileName = UUIDUtils.getUUID() +".xlsx";

        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        int responseCode = connection.getResponseCode();

        if (responseCode == 200) {
            InputStream inputStream = connection.getInputStream();
            FileOutputStream fileOutputStream = new FileOutputStream(fileName);

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }

            fileOutputStream.close();
            inputStream.close();

            // 连接到打印机
            String printerHost = "**********"; // 替换为你的打印机IP
            int printerPort = 9100; // 根据协议调整端口号

            try (Socket socket = new Socket(printerHost, printerPort)) {
                System.out.println("连接到打印机...");

                // 发送打印命令（根据协议不同，命令可能需要调整）
                String command = "\n" + fileName + "\n";
                byte[] commandBytes = command.getBytes();

                try (BufferedInputStream bis = new BufferedInputStream(socket.getInputStream())) {
                    socket.getOutputStream().write(commandBytes);
                    System.out.println("发送打印命令...");

                    // 发送文件内容
                    FileInputStream fileInputStream = new FileInputStream(fileName);
                    while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                        socket.getOutputStream().write(buffer, 0, bytesRead);
                    }
                    System.out.println("发送完成，等待打印...");
                }
            } catch (IOException e) {
                e.printStackTrace();
                System.out.println("打印失败");
            }
        } else {
            System.out.println("Failed to download file. Response code: " + responseCode);
        }
    }




    public static void printPDF(String filePath, String ip, int port, int timeout) {
        boolean connected = false;
        int retries = 3; // 重试次数
        while (!connected && retries > 0) {
            try {
                // 使用 iText 对 PDF 进行缩放处理
                PdfReader reader = new PdfReader(filePath);
                // 创建一个新的 Document，页面大小设置为横向 A4
                Document document = new Document(PageSize.A4.rotate());

                PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream("/apps/upload/scaledPDF.pdf"));
                document.open();

                for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                    document.newPage();
                    // 修正 getImportedPage 的使用方式
                    PdfImportedPage importedPage = writer.getImportedPage(reader, i);
                    // 将 PdfImportedPage 转换为 Image 元素
                    Image image = Image.getInstance(importedPage);
                    // 计算缩放比例，使内容适应 A4 尺寸
                    float originalWidth = importedPage.getWidth();
                    float originalHeight = importedPage.getHeight();
                    float scaleX = PageSize.A4.getWidth() / originalWidth;
                    float scaleY = PageSize.A4.getHeight() / originalHeight;
                    float scaleFactor = Math.min(scaleX, scaleY);
                    // 对 Image 元素进行缩放
                    image.scalePercent(scaleFactor * 130);
                    document.add(image);
                }
                document.close();
                writer.close();
                reader.close();

                // 连接打印机并发送缩放后的 PDF
                File scaledFile = new File("/apps/upload/scaledPDF.pdf");
                Socket socket = new Socket();
                socket.connect(new InetSocketAddress(ip, port), timeout);
                if (socket.isConnected()) {
                    OutputStream out = socket.getOutputStream();
                    FileInputStream fis = new FileInputStream(scaledFile);
                    byte[] buf = new byte[1024];
                    int len;
                    long totalBytes = scaledFile.length();
                    long bytesSent = 0;
                    // 读取文件并发送
                    while ((len = fis.read(buf))!= -1) {
                        out.write(buf, 0, len);
                        bytesSent += len;
                        System.out.println("已发送 " + bytesSent + " / " + totalBytes + " 字节");
                    }
                    // 告诉服务端，文件已传输完毕
                    socket.shutdownOutput();
                    connected = true; // 成功连接并发送文件后标记为已连接
                    out.close();
                } else {
                    System.err.println("连接失败，未能连接到打印机。");
                }
            } catch (IOException | DocumentException e) {
                e.printStackTrace();
            }
            retries--;
        }
        if (!connected) {
            System.err.println("多次重试后仍未能连接到打印机。");
        }
    }

    private static boolean isReachable(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.isReachable(5000); // 尝试在 5 秒内检查是否可达，可根据实际情况调整
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }


    private static PDFPrintable getScaledPDFPrintable(PDDocument document, PageFormat pageFormat) {
        double pageWidth = pageFormat.getPaper().getWidth();
        double pageHeight = pageFormat.getPaper().getHeight();
        double documentWidth = document.getPage(0).getMediaBox().getWidth();
        double documentHeight = document.getPage(0).getMediaBox().getHeight();
        double scaleFactorWidth = pageWidth / documentWidth;
        double scaleFactorHeight = pageHeight / documentHeight;
        // 取较小的缩放比例，确保整个页面都能打印
        double scaleFactor = Math.min(scaleFactorWidth, scaleFactorHeight);
        // 使用合适的枚举常量，例如 SHRINK_TO_FIT
        return new PDFPrintable(document, Scaling.SHRINK_TO_FIT);
    }

    /***
     * 校验司机是否存在
     * 	S_LI_RL_0073
     */
    public EiInfo checkDriver(EiInfo eiInfo) {
        //如有登记时间中的预约单，可以直接带入。（业务+车牌号+司机+时间匹配）
        String segNo = (String) eiInfo.get("segNo");//账套
        String businessType = (String) eiInfo.get("businessType");//业务类型
        String driverIdentity = (String) eiInfo.get("driverIdentity");//司机身份
        String driverTel = (String) eiInfo.get("driverTel");//司机手机号
        String driverName = (String) eiInfo.get("driverName");//司机姓名

        //1、针对周转架、废料提货、资材卸货、欧冶电商密码提货4种业务，不强控登记的司机信息在MES中有维护且生效
        if ("10".equals(businessType)||StringUtils.isBlank(businessType)) {
            //根据司机身份证查询司机信息
            Map queryLIRL0102 = new HashMap();
            queryLIRL0102.put("segNo", segNo);
            queryLIRL0102.put("status", "20");
            queryLIRL0102.put("driverIdentity", driverIdentity);
            queryLIRL0102.put("tel", driverTel);
            queryLIRL0102.put("driverName", driverName);
            queryLIRL0102.put("delFlag", "0");
            List<HashMap> hashMapList = dao.query(LIRL0102.QUERY, queryLIRL0102);
            // String driverTel = "";
            if (CollectionUtils.isEmpty(hashMapList)) {
                if ("10".equals(businessType)){
                    String massage = "该司机信息在安徽宝钢数智物流系统中不存在或不为生效状态，本次允许登记待人工审核。但为了后续进厂可以事先预约，避免等待，请联系所属承运商或客户的管理员（调度员）进行维护。";
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg(massage);
                }else {
                    String massage = "该司机信息在安徽宝钢数智物流系统中不存在或不为生效状态，请联系所属承运商或客户的管理员（调度员）进行维护！";
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg(massage);
                    return eiInfo;
                }

            }
        }
        return eiInfo;
    }

    /***
     * 查询司机对应的客户/承运商信息
     * S_LI_RL_0086
     */
    public EiInfo queryDriverCustomer(EiInfo eiInfo) {
    //如有登记时间中的预约单，可以直接带入。（业务+车牌号+司机+时间匹配）
        String segNo = (String) eiInfo.get("segNo");//账套
        String businessType = (String) eiInfo.get("businessType");//业务类型
        String driverIdentity = (String) eiInfo.get("driverIdentity");//司机身份
        String driverTel = (String) eiInfo.get("driverTel");//司机手机号
        String driverName = (String) eiInfo.get("driverName");//司机姓名
        String reservationNumber = (String) eiInfo.get("reservationNumber");//预约单号
        List<String> voucherNumList = (List<String>) eiInfo.get("voucherNumList");//提单号
        String voucherNum="";
        if (CollectionUtils.isNotEmpty(voucherNumList)){
            voucherNum=String.join(",",voucherNumList);
        }

        List<HashMap> listAdd = new ArrayList<>();
        // 有预约
        if (StringUtils.isBlank(reservationNumber)) {
            if (StringUtils.isNotBlank(voucherNum)) {
                for (String s : voucherNumList) {
                    voucherNum = s;
                    //取提单上的
                    EiInfo outInfo = new EiInfo();
                    String substring = voucherNum.substring(0, 2);
                    if ("ZK".equals(substring)) {
                        //调IMC服务拿到捆包信息
                        EiInfo eiInfo1 = new EiInfo();
                        eiInfo1.set("segNo", segNo);
                        eiInfo1.set("transBillId", voucherNum);
                        eiInfo1.set(EiConstant.serviceId, "S_UC_EP_0044");
                        //调post请求
                        outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == -1) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                            throw new RuntimeException(outInfo.getMsg());
                        }
                        List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
                        if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("未匹配到转库单信息，不允许登记!");
                            return outInfo;
                        }
                        if (rusult.size() > 0) {
                            HashMap hashMapRusult = rusult.get(0);
                            String tproviderId = MapUtils.getString(hashMapRusult, "tproviderId", "");
                            String tproviderName = MapUtils.getString(hashMapRusult, "tproviderName", "");
                            HashMap<String, Object> hashMapC = new HashMap<>();
                            hashMapC.put("customerId", tproviderId);
                            hashMapC.put("customerName", tproviderName);
                            listAdd.add(hashMapC);
                            eiInfo.set("list", listAdd);
                        }
                    } else {
                        //调IMC服务拿到捆包信息
                        EiInfo eiInfo1 = new EiInfo();
                        eiInfo1.set("segNo", segNo);
                        eiInfo1.set("ladingBillId", voucherNum);
                        eiInfo1.set("ladingBillStatus", "50");//生效
                        eiInfo1.set(EiConstant.serviceId, "S_UV_SL_9018");
                        //调post请求
                        outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
                        if (outInfo.getStatus() == -1) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
                            throw new RuntimeException(outInfo.getMsg());
                        }
                        List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
                        if (CollectionUtils.isEmpty(rusult) && rusult.size() <= 0) {
                            outInfo.setStatus(EiConstant.STATUS_FAILURE);
                            outInfo.setMsg("未匹配到提单信息，不允许登记!");
                            return outInfo;
                        }
                        if (rusult.size() > 0) {
                            HashMap hashMapRusult = rusult.get(0);
                            String userNum = MapUtils.getString(hashMapRusult, "userNum", "");
                            String userName = MapUtils.getString(hashMapRusult, "userName", "");
                            String tproviderId = MapUtils.getString(hashMapRusult, "tproviderId", "");
                            String tproviderName = MapUtils.getString(hashMapRusult, "tproviderName", "");
                            String deliverType = MapUtils.getString(hashMapRusult, "deliveryType", "");
                            if (!"10".equals(deliverType)) {
                                HashMap<String, Object> hashMapC = new HashMap<>();
                                hashMapC.put("customerId", tproviderId);
                                hashMapC.put("customerName", tproviderName);
                                listAdd.add(hashMapC);
                            } else {
                                HashMap<String, Object> hashMapC = new HashMap<>();
                                hashMapC.put("customerId", userNum);
                                hashMapC.put("customerName", userName);
                                listAdd.add(hashMapC);
                            }
                            List<HashMap> collect = listAdd.stream().distinct().collect(Collectors.toList());
                            eiInfo.set("list", collect);

                        }
                    }
                }
                if (StringUtils.isBlank(voucherNum)) {
                    HashMap<Object, Object> hashMap1 = new HashMap<>();
                    hashMap1.put("segNo", segNo);
                    hashMap1.put("driverName", driverName);
                    hashMap1.put("driverIdentity1", driverIdentity);
                    hashMap1.put("tel", driverTel);
                    //查询司机信息表
                    List<HashMap> queryLIRL0102 = this.dao.query(LIRL0102.SUB_QUERY, hashMap1);
                    if (CollectionUtils.isNotEmpty(queryLIRL0102)) {
                        List<HashMap> newLIRL0102 = queryLIRL0102.stream().map(lirl0102 -> {
                            HashMap<String, Object> newMap = new HashMap<>();
                            newMap.put("customerId", lirl0102.get("customerId"));
                            newMap.put("customerName", lirl0102.get("customerName"));
                            return newMap;
                        }).collect(Collectors.toList());
                        eiInfo.set("list", newLIRL0102);
                    }
                }
            }
        }
        return eiInfo;
    }

    /***
     * 更新司机对应的运输公司
     * 	S_LI_RL_0089
     */
    public EiInfo updateDriverCompany(EiInfo eiInfo) {
        String segNo = (String) eiInfo.get("segNo");//账套
        String driverIdentity = (String) eiInfo.get("driverIdentity");//司机身份
        String driverTel = (String) eiInfo.get("driverTel");//司机手机号
        String driverName = (String) eiInfo.get("driverName");//司机姓名
        String reservationNumber = (String) eiInfo.get("reservationNumber");//预约单号
        String customerId = (String) eiInfo.get("customerId");//客户代码
        String customerName = (String) eiInfo.get("customerName");//客户名称

        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("segNo",segNo);
        hashMap.put("reservationNumber",reservationNumber);
        hashMap.put("driverIdentity",driverIdentity);
        hashMap.put("driverTel",driverTel);
        hashMap.put("driverName",driverName);
        hashMap.put("customerId",customerId);
        hashMap.put("customerName",customerName);
        if (StringUtils.isNotBlank(customerId)){
            this.dao.update(LIRL0201.UPDATE_BY_CUSTOMER_INFO,hashMap);
            this.dao.update(LIRL0302.UPDATE_BY_CUSTOMER_INFO,hashMap);
        }
        return eiInfo;
    }


    /**
     * 提单打印
     *
     * 根据身份证信息查找提单信息
     * @param eiInfo
     * 	S_LI_RL_0096
     * @return
     */
    public EiInfo queryVoucherInfoByMachine(EiInfo eiInfo){
        try {

            String segNo = (String) eiInfo.get("segNo");//账套
            String idCard = (String) eiInfo.get("idCard"); //身份信息
            String driverName = (String) eiInfo.get("driverName"); //司机姓名
            String ladingBillId = (String) eiInfo.get("voucherNum"); //提单号
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("segNo", segNo);
            hashMap.put("driverId", idCard);
            hashMap.put("driver", driverName);
            hashMap.put("ladingBillStatus","50");
            hashMap.put("ladingBillIdEq", ladingBillId);

            //先查询提单是否存在，不存在调服务写入车辆登记提单信息表
            String xplatToken = TokenUtils.getXplatToken();
            eiInfo.set(EiConstant.serviceId, "S_UV_SL_9020");//查询提单服务
            eiInfo.set("main", hashMap);
            EiInfo outInfo = EServiceManager.call(eiInfo,xplatToken);
            if (outInfo.getStatus() == -1) {
                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                eiInfo.setMsg("开单中心返回报错:" + outInfo.getMsg());
                throw new RuntimeException(eiInfo.getMsg());
            }
            List<HashMap> listAdd = new ArrayList<>();
            List<HashMap> list = (List<HashMap>) outInfo.get("result");
            if (CollectionUtils.isNotEmpty(list)){
                String ladingBillStatus = (String) list.get(0).get("ladingBillStatus");
                if ("10".equals(ladingBillStatus)){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单新增未生效！");
                }else if ("00".equals(ladingBillStatus)){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单已被撤销！");
                }else if ("90".equals(ladingBillStatus)){
                    eiInfo.setStatus(EiConstant.STATUS_FAILURE);
                    eiInfo.setMsg("该提单已提货完成！");
                }
            }

            //调IMC服务拿到捆包信息
            EiInfo eiInfo1 = new EiInfo();
            eiInfo1.set("segNo", segNo);
            eiInfo1.set("driverId", idCard);
            eiInfo1.set("transBillType","10");
            eiInfo1.set("transBillId", ladingBillId);
            eiInfo1.set("driverName", driverName);
            eiInfo1.set(EiConstant.serviceId, "S_UC_EP_0044");
            //调post请求
            outInfo = EServiceManager.call(eiInfo1, TokenUtils.getXplatToken());
            // if (outInfo.getStatus() == -1) {
            //     outInfo.setStatus(EiConstant.STATUS_FAILURE);
            //     outInfo.setMsg("调用开单中心返回报错:" + outInfo.getMsg());
            //     throw new RuntimeException(outInfo.getMsg());
            // }
            List<HashMap> rusult = (List<HashMap>) outInfo.get("result");
            if (CollectionUtils.isEmpty(rusult)&&CollectionUtils.isEmpty(list)) {
                eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                // outInfo.setMsg("未查询到提货委托的提单或转库单!");
                eiInfo.set("list",listAdd);
                return eiInfo;
            }
            if (CollectionUtils.isNotEmpty(rusult)){
                for (HashMap map : rusult) {
                    map.put("ladingBillId", MapUtils.getString(map,"transBillId"));
                    map.put("dateStr", MapUtils.getString(map,"billingTime"));
                    map.put("userName", MapUtils.getString(map,"packSettleUserName"));
                    String totalQty = MapUtils.getString(map, "sumNtotalQty");
                    if ("0E-8".equals(totalQty)){
                        map.put("totalPackQty", "0.000");
                    }else {
                        map.put("totalPackQty", totalQty);
                    }
                    String planNetWeight = MapUtils.getString(map, "sumNetWeight");
                    if ("0E-8".equals(planNetWeight)){
                        map.put("totalWeight", "0.000");
                    }else {
                        map.put("totalWeight", planNetWeight);
                    }

                    map.put("userName", " ");
                }
            }
            if (CollectionUtils.isNotEmpty(list)&&CollectionUtils.isEmpty(rusult)){
                eiInfo.set("list",list);
            }else if (CollectionUtils.isEmpty(list)&&CollectionUtils.isNotEmpty(rusult)){
                eiInfo.set("list",rusult);
            }else if (CollectionUtils.isNotEmpty(list)&&CollectionUtils.isNotEmpty(rusult)){
                List<HashMap> mergedList = new ArrayList<>(list);
                mergedList.addAll(rusult);
                eiInfo.set("list",mergedList);
            }else {
                eiInfo.set("list",listAdd);
            }

        } catch (RuntimeException e) {
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
            eiInfo.setMsg(e.getMessage());
            return eiInfo;
        }
        return eiInfo;
    }
    /***
     * 车联作业超过60分钟给予短息提醒
     */
    public EiInfo overTimeSendMessage(EiInfo eiInfo) {
        //发送短信通知仓管人员
        Map sendSms = new HashMap();
        sendSms.put("segNo","KF000000");
        sendSms.put("sendType","20");//超时通知
        sendSms.put("status","20");//启用
        sendSms.put("flag","20");//启用

        //查询配置
        Map querytlirl0314 = new HashMap();
        querytlirl0314.put("segNo","KF000000");
        querytlirl0314.put("itemCode","OVER_TIME");
        List<LIRL0314> lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
        int count=0;
        if (lirl0314s.size()>0){
            count = Integer.parseInt(lirl0314s.get(0).getItemCname());
            sendSms.put("timer",count);//启用
        }
        querytlirl0314.put("itemCode","SYN_TIME");
        lirl0314s = dao.query(LIRL0314.QUERY, querytlirl0314);
        int synTime=0;
        if (lirl0314s.size()>0){
            synTime = Integer.parseInt(lirl0314s.get(0).getItemCname());
        }
        //判断是否超时
        List<HashMap> queryLIRL0406 = this.dao.query(LIRL0406.QUERY_OVERT_VEHICLENO, sendSms);
        if (CollectionUtils.isNotEmpty(queryLIRL0406)){
            for (HashMap hashMap : queryLIRL0406) {
                String handPointName = MapUtils.getString(hashMap, "handPointName");//目标装卸点
                String currentHandPointName = MapUtils.getString(hashMap, "currentHandPointName");//当前装卸点
                String targetHandPointId = MapUtils.getString(hashMap, "targetHandPointId").trim();//当前装卸点
                String currentHandPointId = MapUtils.getString(hashMap, "currentHandPointId").trim();//当前装卸点
                String businessType = MapUtils.getString(hashMap, "businessType");//业务类型
                String customerName = MapUtils.getString(hashMap, "customerName");//客户分户号
                String startOfTransport = MapUtils.getString(hashMap, "startOfTransport");//运输起始地
                String waitingTime = MapUtils.getString(hashMap, "waitingTime");//超时时间
                String businessTypeCode = MapUtils.getString(hashMap, "businessTypeCode");//装卸类型
                if (StringUtils.isNotBlank(handPointName) || StringUtils.isNotBlank(currentHandPointName)){
                    if (StringUtils.isBlank(handPointName)){
                        handPointName=currentHandPointName;
                    }
                    if (StringUtils.isNotBlank(currentHandPointId)){
                        targetHandPointId=currentHandPointId;
                        if (currentHandPointId.equals("KFZXD25020016")||currentHandPointId.equals("KFZXD25020015")||currentHandPointId.equals("KFZXD25020014")){
                            continue;
                        }
                    }
                    if (StringUtils.isNotBlank(targetHandPointId)){
                        if (targetHandPointId.equals("KFZXD25020016")||targetHandPointId.equals("KFZXD25020015")||targetHandPointId.equals("KFZXD25020014")){
                            continue;
                        }
                    }

                    String vehicleNo = MapUtils.getString(hashMap, "vehicleNo");
                    sendSms.put("targetHandPointId",targetHandPointId); //责任到门
                    int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();//当前天是星期几
                    sendSms.put("dayOfWeek",dayOfWeek);
                    List<LIRL0505> query = this.dao.query(LIRL0505.QUERY, sendSms);
                    if (CollectionUtils.isNotEmpty(query)){
                        for (LIRL0505 lirl0505:query){
                            //通知仓管人员进行提醒
                            String content="";
                            String perTel = lirl0505.getPerTel();
                            String perName = lirl0505.getPerName();
                            Integer timeoutThreshold = lirl0505.getTimeoutThreshold();
                            String typeOfHandling = lirl0505.getTypeOfHandling();
                            if (StringUtils.isNotBlank(typeOfHandling)) {
                                if (typeOfHandling.equals(businessTypeCode)) {
                                    if (timeoutThreshold > 0) {
                                        if (Integer.parseInt(waitingTime)>timeoutThreshold&&Integer.parseInt(waitingTime)<=(timeoutThreshold+synTime)) {
                                            if (StringUtils.isBlank(customerName)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                            }else if (StringUtils.isBlank(customerName)&&StringUtils.isBlank(startOfTransport)){
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                            }
                                        }
                                    } else {
                                        if (StringUtils.isBlank(customerName)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        }else if (StringUtils.isBlank(customerName)&&StringUtils.isBlank(startOfTransport)){
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        }
                                    }
                                }else {
                                    if (StringUtils.isBlank(customerName)) {
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                    } else if (StringUtils.isBlank(startOfTransport)) {
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                    } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                    }else if (StringUtils.isBlank(customerName)&&StringUtils.isBlank(startOfTransport)){
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";

                                    }
                                }
                            }else{
                                if (timeoutThreshold > 0) {
                                    if (Integer.parseInt(waitingTime)>timeoutThreshold&&Integer.parseInt(waitingTime)<=(timeoutThreshold+synTime)) {
                                        if (StringUtils.isBlank(customerName)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        }else if (StringUtils.isBlank(customerName)&&StringUtils.isBlank(startOfTransport)){
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                        }
                                    }
                                } else {
                                    if (StringUtils.isBlank(customerName)) {
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                    } else if (StringUtils.isBlank(startOfTransport)) {
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                    } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                    }else if (StringUtils.isBlank(customerName)&&StringUtils.isBlank(startOfTransport)){
                                        content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + waitingTime + "分钟，请及时关注，谢谢！";
                                    }
                                }
                            }
//                            EiInfo outInfo = new EiInfo();
//                            outInfo.set("content",content);
//                            outInfo.set("mobileNum",perTel);
                            if (StringUtils.isNotBlank(content)) {
                                HashMap<String, Object> messageMap = new HashMap<>();
                                messageMap.put("param1",perTel);
                                messageMap.put("param2",perName);
                                messageMap.put("param3",handPointName);
                                messageMap.put("param4",vehicleNo);
                                messageMap.put("param5",businessType);
                                messageMap.put("param6",customerName);
                                messageMap.put("param7",startOfTransport);
                                messageMap.put("param8",waitingTime);
                                MessageUtils.sendMessage(messageMap,"MT0000001011");
                            }
                        }
                    }else {
                        sendSms.put("targetHandPointId", ""); //责任到门
                        sendSms.put("notTargetHandPointId", "10"); //责任到门
                        query = this.dao.query(LIRL0505.QUERY, sendSms);
                        if (CollectionUtils.isNotEmpty(query)) {
                            //通知所有人
                            for (LIRL0505 lirl0505 : query) {
                                //通知仓管人员进行提醒
                                String content = "";
                                String perTel = lirl0505.getPerTel();
                                String perName = lirl0505.getPerName();
                                Integer timeoutThreshold = lirl0505.getTimeoutThreshold();//阈值
                                String typeOfHandling = lirl0505.getTypeOfHandling();
                                if (StringUtils.isNotBlank(typeOfHandling)) {
                                    if (typeOfHandling.equals(businessTypeCode)) {
                                        if (timeoutThreshold > 0) {
                                            if (Integer.parseInt(waitingTime) > timeoutThreshold && Integer.parseInt(waitingTime) <= (timeoutThreshold + synTime)) {
                                                if (StringUtils.isBlank(customerName)) {
                                                    content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                                } else if (StringUtils.isBlank(startOfTransport)) {
                                                    content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                                } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                                    content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                                } else if (StringUtils.isBlank(customerName) && StringUtils.isBlank(startOfTransport)) {
                                                    content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                                }
                                            }
                                        } else {
                                            if (StringUtils.isBlank(customerName)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isBlank(customerName) && StringUtils.isBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";
                                            }
                                        }
                                    } else {
                                        if (StringUtils.isBlank(customerName)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isBlank(customerName) && StringUtils.isBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + ((timeoutThreshold > 0) ? timeoutThreshold : count) + "分钟，请及时关注，谢谢！";

                                        }
                                    }
                                } else {
                                    if (timeoutThreshold > 0) {
                                        if (Integer.parseInt(waitingTime) > timeoutThreshold && Integer.parseInt(waitingTime) <= (timeoutThreshold + synTime)) {
                                            if (StringUtils.isBlank(customerName)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                            } else if (StringUtils.isBlank(customerName) && StringUtils.isBlank(startOfTransport)) {
                                                content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + timeoutThreshold + "分钟，请及时关注，谢谢！";
                                            }
                                        }
                                    } else {
                                        if (StringUtils.isBlank(customerName)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，起始地：" + startOfTransport + "等待时长已超过" + count + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "等待时长已超过" + count + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：" + customerName + "，起始地：" + startOfTransport + "等待时长已超过" + count + "分钟，请及时关注，谢谢！";
                                        } else if (StringUtils.isBlank(customerName) && StringUtils.isBlank(startOfTransport)) {
                                            content = perName + "，当前装卸点：" + handPointName + "，当前车辆：" + vehicleNo + "，业务类型：" + businessType + "，客户名称：，起始地：等待时长已超过" + count + "分钟，请及时关注，谢谢！";
                                        }
                                    }
                                }
                                EiInfo outInfo = new EiInfo();
                                outInfo.set("content", content);
                                outInfo.set("mobileNum", perTel);
                                if (StringUtils.isNotBlank(content)) {
                                    HashMap<String, Object> messageMap = new HashMap<>();
                                    messageMap.put("param1",perTel);
                                    messageMap.put("param2",perName);
                                    messageMap.put("param3",handPointName);
                                    messageMap.put("param4",vehicleNo);
                                    messageMap.put("param5",businessType);
                                    messageMap.put("param6",customerName);
                                    messageMap.put("param7",startOfTransport);
                                    messageMap.put("param8",waitingTime);
                                    MessageUtils.sendMessage(messageMap,"MT0000001011");
                                }
                            }
                        }
                    }
                }
            }
        }
        return eiInfo;
    }

    /**
     * 计算两个库位编号之间的间隔数
     * @param start 起始编号（如"A01"）
     * @param end 结束编号（如"A40"）
     * @return 间隔的数字个数
     */
    public static int calculateInterval(String start, String end) {
        return extractNumber(end) - extractNumber(start);
    }

    /**
     * 从库位编号中提取数字部分
     * @param locationId 库位编号（如"A01"）
     * @return 数字部分（如1）
     */
    private static int extractNumber(String locationId) {
        // 使用正则表达式提取纯数字部分
        String numberStr = locationId.replaceAll("[^0-9]", "");
        return Integer.parseInt(numberStr);
    }



    /**
     * 打印PDF文档
     */
    private static void printPdfDocument(File pdfFile) throws Exception {
        try (PDDocument document = PDDocument.load(pdfFile)) {
            PrinterJob job = PrinterJob.getPrinterJob();

            // 查找网络打印机
            PrintService[] services = PrintServiceLookup.lookupPrintServices(null, null);
            PrintService targetPrinter = null;
            for (PrintService service : services) {
                if (service.getName().contains("**********")) {
                    targetPrinter = service;
                    break;
                }
            }

            if (targetPrinter == null) {
                throw new PrinterException("找不到指定打印机: " + "**********");
            }

            job.setPrintService(targetPrinter);

            // 设置打印属性
            HashPrintRequestAttributeSet attributes = new HashPrintRequestAttributeSet();
            attributes.add(MediaSizeName.ISO_A4);

            // 设置缩放比例
            PDFPrintable printable = new PDFPrintable(document, Scaling.SHRINK_TO_FIT);

            // 创建打印页面格式
            PageFormat pageFormat = PrinterJob.getPrinterJob().defaultPage();
            Paper paper = new Paper();

// 设置A4纸张尺寸（单位：1/72英寸）
            paper.setSize(595, 842);  // A4尺寸：595pt × 842pt
            paper.setImageableArea(0, 0, 595, 842); // 全幅可打印区域

// 使用横向打印（根据需求可选）
            pageFormat.setOrientation(PageFormat.LANDSCAPE);

// 创建打印书籍对象
            Book book = new Book();
            book.append(printable, pageFormat, document.getNumberOfPages());

            job.setPageable(book);
            job.print(attributes);

            System.out.println("打印任务已提交到打印机");
        }
    }

}
