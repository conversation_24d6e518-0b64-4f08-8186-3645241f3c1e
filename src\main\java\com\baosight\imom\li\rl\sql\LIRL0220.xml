<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">


<sqlMap namespace="LIRL0220">

<!--    <select id="query" parameterClass="java.util.HashMap"-->
<!--            resultClass="java.util.HashMap">-->
<!--        select * from-->
<!--        (select a.SEG_NO                                          as       "segNo",-->
<!--        a.SEG_NO,-->
<!--        a.UNIT_CODE                                       as       "unitCode",-->
<!--        a.UNIT_CODE,-->
<!--        a.CAR_TRACE_NO                                    as       "carTraceNo",-->
<!--        a.CAR_TRACE_NO,-->
<!--        a.STATUS                                          as       "status",-->
<!--        a.HAND_TYPE                                       as       "handType",-->
<!--        a.HAND_TYPE,-->
<!--        a.VEHICLE_NO                                      as       "vehicleNo",-->
<!--        a.VEHICLE_NO,-->
<!--        a.ID_CARD                                         as       "idCard",-->
<!--        a.ID_CARD,-->
<!--        a.DRIVER_NAME                                     as       "driverName",-->
<!--        a.DRIVER_NAME,-->
<!--        a.TEL_NUM                                         as       "telNum",-->
<!--        a.TEL_NUM,-->
<!--        a.RESERVATION_NUMBER                              as       "reservationNumber",-->
<!--        a.RESERVATION_NUMBER,-->
<!--        a.CHECK_DATE                                      as       "checkDate",-->
<!--        a.CHECK_DATE,-->
<!--        ENTER_FACTORY                                     as       "enterFactory",-->
<!--        ENTER_FACTORY,-->
<!--        (select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)                                         as       "beginEntruckingTime",-->
<!--        case-->
<!--        when a.STATUS = '20' || a.STATUS = '30' || (a.STATUS = '40' &amp;&amp; a.TARGET_HAND_POINT_ID = ' ')-->
<!--        then a.BEGIN_ENTRUCKING_TIME-->
<!--        else ' ' end                                  as       "currentBeginEntruckingTime",-->
<!--        case-->
<!--        when a.STATUS = '20' || a.STATUS = '30' || (a.STATUS = '40' &amp;&amp; a.TARGET_HAND_POINT_ID = ' ')-->
<!--        then a.COMPLETE_UNINSTALL_TIME-->
<!--        else ' ' end                                  as       "currentcompleteUninstallTime",-->
<!--        (select max(tlirl0407.FINISH_LOAD_DATE)-->
<!--        from MELI.tlirl0407 tlirl0407-->
<!--        where tlirl0407.SEG_NO = a.SEG_NO-->
<!--        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0407.NEXT_TATGET = '20'-->
<!--        and tlirl0407.STATUS != '00'-->
<!--        limit 1)                                         as       "completeUninstallTime",-->
<!--        COMPLETE_UNINSTALL_TIME,-->
<!--        LEAVE_FACTORY_DATE                                as       "leaveFactoryDate",-->
<!--        LEAVE_FACTORY_DATE,-->
<!--        CUSTOMER_SIGNING_TIME                             as       "customerSigningTime",-->
<!--        CUSTOMER_SIGNING_TIME,-->
<!--        TARGET_HAND_POINT_ID                              as       "targetHandPointId",-->
<!--        TARGET_HAND_POINT_ID,-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "targetHandPointName",-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "target_hand_point_name",-->
<!--        CURRENT_HAND_POINT_ID                             as       "currentHandPointId",-->
<!--        CURRENT_HAND_POINT_ID,-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "currentHandPointName",-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "current_hand_point_name",-->
<!--        a.FACTORY_AREA                                    as       "factoryArea",-->
<!--        a.FACTORY_AREA,-->
<!--        a.FACTORY_AREA_NAME                               as       "factoryAreaName",-->
<!--        a.FACTORY_AREA_NAME,-->
<!--        (select tlirl0407.NEXT_TATGET-->
<!--        from MELI.tlirl0407 tlirl0407-->
<!--        where tlirl0407.SEG_NO = a.SEG_NO-->
<!--        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0407.STATUS != '00'-->
<!--        order by tlirl0407.REC_CREATE_TIME desc-->
<!--        limit 1)                                         as       "nextTarget",-->
<!--        (select tlirl0407.NEXT_TATGET-->
<!--        from MELI.tlirl0407 tlirl0407-->
<!--        where tlirl0407.SEG_NO = a.SEG_NO-->
<!--        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0407.STATUS != '00'-->
<!--        order by tlirl0407.REC_CREATE_TIME desc-->
<!--        limit 1)                                         as       "next_target",-->
<!--        UNLOAD_LEAVE_FLAG                                 as       "unloadLeaveFlag",-->
<!--        UNLOAD_LEAVE_FLAG,-->
<!--        a.REC_CREATOR                                     as       "recCreator",-->
<!--        a.REC_CREATOR,-->
<!--        a.REC_CREATOR_NAME                                as       "recCreatorName",-->
<!--        a.REC_CREATOR_NAME,-->
<!--        a.REC_CREATE_TIME                                 as       "recCreateTime",-->
<!--        a.REC_CREATE_TIME,-->
<!--        a.REC_REVISOR                                     as       "recRevisor",-->
<!--        a.REC_REVISOR,-->
<!--        a.REC_REVISOR_NAME                                as       "recRevisorName",-->
<!--        a.REC_REVISOR_NAME,-->
<!--        a.REC_REVISE_TIME                                 as       "recReviseTime",-->
<!--        a.REC_REVISE_TIME,-->
<!--        a.DEL_FLAG                                        as       "delFlag",-->
<!--        a.DEL_FLAG,-->
<!--        a.REMARK                                          as       "remark",-->
<!--        a.SYS_REMARK                                      as       "sysRemark",-->
<!--        a.SYS_REMARK,-->
<!--        a.UUID                                            as       "uuid",-->
<!--        tlirl0201.IS_RESERVATION                                   "isReservation",-->
<!--        (select if(count(*) > 0, 1, 0)-->
<!--        from MELI.tlirl0201 b-->
<!--        where 1 = 1-->
<!--        and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--        and b.DEL_FLAG = '0')                                   "whet_appointment",-->
<!--        tlirl0201.START_OF_TRANSPORT                               "startOfTransport",-->
<!--        tlirl0201.START_OF_TRANSPORT                               "start_of_transport",-->
<!--        tlirl0201.PURPOSE_OF_TRANSPORT                             "purposeOfTransport",-->
<!--        tlirl0201.PURPOSE_OF_TRANSPORT                             "purpose_of_transport",-->
<!--        tlirl0201.CUSTOMER_NAME                                    "customerName",-->
<!--        tlirl0201.CUSTOMER_NAME                                    "customer_name",-->
<!--        tlirl0201.STATUS                                           "appointmentStatus",-->
<!--        tlirl0201.STATUS                                           "appointment_status",-->
<!--        tlirl0201.RESERVATION_DATE                                 "reservationDate",-->
<!--        tlirl0201.RESERVATION_DATE                                 "reservation_date",-->
<!--        tlirl0201.RESERVATION_TIME                                 "reservationTime",-->
<!--        tlirl0201.RESERVATION_TIME                                 "reservation_time",-->
<!--        ifnull(tlirl0302.LATE_EARLY_FLAG, 0)                       "lateEarlyFlag",-->
<!--        ifnull(tlirl0302.LATE_EARLY_FLAG, 0)                       "late_early_flag",-->
<!--        (case-->
<!--        when tlirl0302.VOUCHER_NUM = ' ' then case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '20'-->
<!--        when tlirl0302.BUSINESS_TYPE = '20' then '60'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30' then '40'-->
<!--        when tlirl0302.BUSINESS_TYPE = '40' then '50'-->
<!--        when tlirl0302.BUSINESS_TYPE = '50' then '70'-->
<!--        when tlirl0302.BUSINESS_TYPE = '60' then '80' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null THEN case-->
<!--        when a.HAND_TYPE = '10'-->
<!--        then '10'-->
<!--        when a.HAND_TYPE = '30'-->
<!--        then '30' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '10'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30'-->
<!--        then '30' end end end) as       "businessType",-->
<!--        (case-->
<!--        when tlirl0302.VOUCHER_NUM = ' ' then case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '20'-->
<!--        when tlirl0302.BUSINESS_TYPE = '20' then '60'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30' then '40'-->
<!--        when tlirl0302.BUSINESS_TYPE = '40' then '50'-->
<!--        when tlirl0302.BUSINESS_TYPE = '50' then '70'-->
<!--        when tlirl0302.BUSINESS_TYPE = '60' then '80' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null THEN case-->
<!--        when a.HAND_TYPE = '10'-->
<!--        then '10'-->
<!--        when a.HAND_TYPE = '30'-->
<!--        then '30' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '10'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30'-->
<!--        then '30' end end end) as       "business_type",-->
<!--        tlirl0302.VOUCHER_NUM                                      "voucherNum",-->
<!--        tlirl0302.VOUCHER_NUM                                      "voucher_num",-->
<!--        ifnull((select QUEUE_DATE-->
<!--        from MELI.tlirl0402 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0'), (select QUEUE_DATE-->
<!--        from MELI.tlirl0409 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0')) "callDate",-->
<!--        ifnull((select QUEUE_DATE-->
<!--        from MELI.tlirl0402 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0'), (select QUEUE_DATE-->
<!--        from MELI.tlirl0409 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0')) "call_date",-->
<!--        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theTimeFromRegistrationToEntry",-->
<!--        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_time_from_registration_to_entry",-->
<!--        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'))) >-->
<!--        0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s')),-->
<!--        0),-->
<!--        '')                                                     "theTimeFromEnteringTheFactoryToTheStartOfTheJob",-->
<!--        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'))) >-->
<!--        0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s')),-->
<!--        0),-->
<!--        '')                                                     "the_time_from_entering_the_factory_to_the_start_of_the_job",-->
<!--        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.BEGIN_ENTRUCKING_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",-->
<!--        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theTimeFromTheCompletionOfTheJobToTheFactory",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_time_from_the_completion_of_the_job_to_the_factory",-->
<!--        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),-->
<!--        "")                                                     "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",-->
<!--        if(COMPLETE_UNINSTALL_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >-->
<!--        0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),-->
<!--        0),-->
<!--        "")                                                     "the_time_from_entering_the_factory_to_the_completion_of_the_job",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theTimeFromEnteringTheFactoryToLeavingTheFactory",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_time_from_entering_the_factory_to_leaving_the_factory",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "registeredToTheFactoryTime",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "registered_to_the_factory_time",-->
<!--        tlirl0308.SIGN_OFF_TIME                           as       "sign_off_time",-->
<!--        tlirl0308.SIGN_OFF_TIME                           as       "signOffTime",-->
<!--        CASE-->
<!--        WHEN (TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0 THEN CONCAT(-->
<!--        FLOOR(TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')) / 60), '时', MOD(-->
<!--        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')), 60), '分')-->
<!--        WHEN a.LEAVE_FACTORY_DATE != '' and tlirl0308.SIGN_OFF_TIME != '' then '0时0分'-->
<!--        ELSE '' END                                            "theTimeFromTheFactoryToTheTimeOfReceipt",-->
<!--        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')),-->
<!--        0)                                                      "the_time_from_the_factory_to_the_time_of_receipt",-->
<!--        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signing_location_longitude",-->
<!--        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signingLocationLongitude",-->
<!--        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signing_location_latitude",-->
<!--        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signingLocationLatitude",-->
<!--        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitude_latitude_check",-->
<!--        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitudeLatitudeCheck",-->
<!--        tlirl0308.FINAL_STATION_LONGITUDE                 as       "final_station_longitude",-->
<!--        tlirl0308.FINAL_STATION_LONGITUDE                 as       "finalStationLongitude",-->
<!--        tlirl0308.FINAL_STATION_LATITUDE                  as       "final_station_latitude",-->
<!--        tlirl0308.FINAL_STATION_LATITUDE                  as       "finalStationLatitude",-->
<!--        ifnull(tlirl0308.outWeight, 0)                    as       "outWeight",-->
<!--        ifnull(tlirl0308.outQuantity, 0)                  as       "outQuantity",-->
<!--        ifnull(tlirl0308.inWeight, 0)                     as       "inWeight",-->
<!--        ifnull(tlirl0308.inQuantity, 0)                   as       "inQuantity",-->
<!--        tlirl0308.SIGNING_ADDRESS                         AS       "signingAddress",-->
<!--        tlirl0308.SIGNING_ADDRESS                         AS       "signing_address",-->
<!--        tlirl0308.DEST_SPOT_ADDR                          AS       "destSpotAddr",-->
<!--        tlirl0308.DEST_SPOT_ADDR                          AS       "dest_spot_addr",-->
<!--        a.EXPECTED_LOADING_TIME as "expectedLoadingTime"-->
<!--        from MELI.tlirl0301 a-->
<!--        left join meli.tlirl0201 tlirl0201-->
<!--        on tlirl0201.SEG_NO = a.SEG_NO-->
<!--        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--        and tlirl0201.STATUS != '00'-->
<!--        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO-->
<!--        left join (select tlirl0308.SEG_NO,-->
<!--        tlirl0308.VEHICLE_NO,-->
<!--        tlirl0308.CAR_TRACE_NO,-->
<!--        max(tlirl0308.SIGNING_LOCATION_LONGITUDE) as "SIGNING_LOCATION_LONGITUDE",-->
<!--        max(tlirl0308.SIGNING_LOCATION_LATITUDE)  as "SIGNING_LOCATION_LATITUDE",-->
<!--        max(tlirl0308.LONGITUDE_LATITUDE_CHECK)   as "LONGITUDE_LATITUDE_CHECK",-->
<!--        max(tlirl0308.FINAL_STATION_LONGITUDE)    as "FINAL_STATION_LONGITUDE",-->
<!--        max(tlirl0308.FINAL_STATION_LATITUDE)     as "FINAL_STATION_LATITUDE",-->
<!--        max(tlirl0308.SIGN_OFF_TIME)              as "SIGN_OFF_TIME",-->
<!--        max(tlirl0308.SIGNING_ADDRESS)            as "SIGNING_ADDRESS",-->
<!--        max(tlirl0308.DEST_SPOT_ADDR)             as "DEST_SPOT_ADDR",-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN (select count(1)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS outQuantity,-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN (select count(1)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS inQuantity,-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN (select sum(tlirl03082.WEIGHT)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS outWeight,-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN (select sum(tlirl03082.WEIGHT)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS inWeight-->
<!--        from MELI.tlirl0308 tlirl0308-->
<!--        where tlirl0308.SEG_NO = #segNo#-->
<!--        and tlirl0308.DEL_FLAG = '0'-->
<!--        group by tlirl0308.SEG_NO, tlirl0308.VEHICLE_NO, tlirl0308.CAR_TRACE_NO,-->
<!--        PUT_IN_OUT_FLAG) tlirl0308-->
<!--        on tlirl0308.SEG_NO = a.SEG_NO and tlirl0308.CAR_TRACE_NO = a.CAR_TRACE_NO and-->
<!--        tlirl0308.VEHICLE_NO = a.VEHICLE_NO-->
<!--        left join meli.tlirl0302 tlirl0302-->
<!--        on tlirl0302.SEG_NO = a.SEG_NO and tlirl0302.CAR_TRACE_NO = a.CAR_TRACE_NO and-->
<!--        tlirl0302.STATUS != '00'-->
<!--        where 1 = 1-->
<!--        <isNotEmpty prepend=" AND " property="status">-->
<!--            a.STATUS in ($status$)-->
<!--        </isNotEmpty>-->
<!--        <isEmpty prepend=" AND " property="status">-->
<!--            a.STATUS != '00'-->
<!--        </isEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="inHouse">-->
<!--            a.STATUS in ('20','30','40')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="outHouse">-->
<!--            a.STATUS in ('50','60')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="noHouse">-->
<!--            a.STATUS in ('10')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="segNo">-->
<!--            a.SEG_NO = #segNo#-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="telNum">-->
<!--            a.TEL_NUM like concat('%',#telNum#,'%')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="driverName">-->
<!--            a.DRIVER_NAME like concat('%',#driverName#,'%')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="vehicleNo">-->
<!--            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始预约日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="reservationDateStart">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止预约日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="reservationDateEnd">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始登记日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="checkDateStart">-->
<!--            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止登记日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="checkDateEnd">-->
<!--            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始进厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="enterFactoryStart">-->
<!--            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止进厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="enterFactoryEnd">-->
<!--            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始出厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">-->
<!--            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止出厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">-->
<!--            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;出厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="leaveFactoryDate">-->
<!--            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="businessType">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0302 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.BUSINESS_TYPE = #businessType#-->
<!--            <isNotEmpty prepend=" and " property="voucherNumN">-->
<!--                b.VOUCHER_NUM = ' '-->
<!--            </isNotEmpty>-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="handType">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0302 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.HAND_TYPE = #handType#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="voucherNum">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0308 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="packId">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0308 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.PACK_ID like concat('%',#packId#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="startOfTransport">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="purposeOfTransport">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerId">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_ID = #customerId#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerName">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_NAME = #customerName#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerId2">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_ID = #customerId2#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerName2">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_NAME = #customerName2#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="carTraceNo">-->
<!--            a.CAR_TRACE_NO = #carTraceNo#-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="statusListStr">-->
<!--            a.STATUS in ( $statusListStr$ )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="statusNotListStr">-->
<!--            a.STATUS NOT in ( $statusNotListStr$ )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="delFlag">-->
<!--            a.DEL_FLAG = #delFlag#-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;<isNotEmpty prepend=" AND " property="status">-->
<!--            STATUS <![CDATA[<]]> #status#-->
<!--        </isNotEmpty>&ndash;&gt;-->
<!--        <isNotEmpty prepend=" AND " property="handPointId">-->
<!--            a.TARGET_HAND_POINT_ID = #handPointId#-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="statusList">-->
<!--            a.STATUS in-->
<!--            <iterate property="statusList" open="("-->
<!--                     close=")" conjunction=" , ">-->
<!--                #statusList[]#-->
<!--            </iterate>-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="isReservation">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.IS_RESERVATION = #isReservation#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        union-->
<!--        select a.SEG_NO                                          as       "segNo",-->
<!--        a.SEG_NO,-->
<!--        a.UNIT_CODE                                       as       "unitCode",-->
<!--        a.UNIT_CODE,-->
<!--        a.CAR_TRACE_NO                                    as       "carTraceNo",-->
<!--        a.CAR_TRACE_NO,-->
<!--        a.STATUS                                          as       "status",-->
<!--        a.HAND_TYPE                                       as       "handType",-->
<!--        a.HAND_TYPE,-->
<!--        a.VEHICLE_NO                                      as       "vehicleNo",-->
<!--        a.VEHICLE_NO,-->
<!--        a.ID_CARD                                         as       "idCard",-->
<!--        a.ID_CARD,-->
<!--        a.DRIVER_NAME                                     as       "driverName",-->
<!--        a.DRIVER_NAME,-->
<!--        a.TEL_NUM                                         as       "telNum",-->
<!--        a.TEL_NUM,-->
<!--        a.RESERVATION_NUMBER                              as       "reservationNumber",-->
<!--        a.RESERVATION_NUMBER,-->
<!--        a.CHECK_DATE                                      as       "checkDate",-->
<!--        a.CHECK_DATE,-->
<!--        ENTER_FACTORY                                     as       "enterFactory",-->
<!--        ENTER_FACTORY,-->
<!--        (select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)                                         as       "beginEntruckingTime",-->
<!--        case-->
<!--        when a.STATUS = '20' || a.STATUS = '30' || (a.STATUS = '40' &amp;&amp; a.TARGET_HAND_POINT_ID = ' ')-->
<!--        then a.BEGIN_ENTRUCKING_TIME-->
<!--        else ' ' end                                  as       "currentBeginEntruckingTime",-->
<!--        case-->
<!--        when a.STATUS = '20' || a.STATUS = '30' || (a.STATUS = '40' &amp;&amp; a.TARGET_HAND_POINT_ID = ' ')-->
<!--        then a.COMPLETE_UNINSTALL_TIME-->
<!--        else ' ' end                                  as       "currentcompleteUninstallTime",-->
<!--        (select max(tlirl0407.FINISH_LOAD_DATE)-->
<!--        from MELI.tlirl0407 tlirl0407-->
<!--        where tlirl0407.SEG_NO = a.SEG_NO-->
<!--        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0407.NEXT_TATGET = '20'-->
<!--        and tlirl0407.STATUS != '00'-->
<!--        limit 1)                                         as       "completeUninstallTime",-->
<!--        COMPLETE_UNINSTALL_TIME,-->
<!--        LEAVE_FACTORY_DATE                                as       "leaveFactoryDate",-->
<!--        LEAVE_FACTORY_DATE,-->
<!--        CUSTOMER_SIGNING_TIME                             as       "customerSigningTime",-->
<!--        CUSTOMER_SIGNING_TIME,-->
<!--        TARGET_HAND_POINT_ID                              as       "targetHandPointId",-->
<!--        TARGET_HAND_POINT_ID,-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "targetHandPointName",-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.TARGET_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "target_hand_point_name",-->
<!--        CURRENT_HAND_POINT_ID                             as       "currentHandPointId",-->
<!--        CURRENT_HAND_POINT_ID,-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "currentHandPointName",-->
<!--        (select tlirl0304.HAND_POINT_NAME-->
<!--        from MELI.tlirl0304 tlirl0304-->
<!--        where tlirl0304.SEG_NO = a.SEG_NO-->
<!--        and tlirl0304.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID-->
<!--        and tlirl0304.STATUS = '30'-->
<!--        and tlirl0304.DEL_FLAG = '0')                  as       "current_hand_point_name",-->
<!--        a.FACTORY_AREA                                    as       "factoryArea",-->
<!--        a.FACTORY_AREA,-->
<!--        a.FACTORY_AREA_NAME                               as       "factoryAreaName",-->
<!--        a.FACTORY_AREA_NAME,-->
<!--        (select tlirl0407.NEXT_TATGET-->
<!--        from MELI.tlirl0407 tlirl0407-->
<!--        where tlirl0407.SEG_NO = a.SEG_NO-->
<!--        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0407.STATUS != '00'-->
<!--        order by tlirl0407.REC_CREATE_TIME desc-->
<!--        limit 1)                                         as       "nextTarget",-->
<!--        (select tlirl0407.NEXT_TATGET-->
<!--        from MELI.tlirl0407 tlirl0407-->
<!--        where tlirl0407.SEG_NO = a.SEG_NO-->
<!--        and tlirl0407.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0407.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0407.STATUS != '00'-->
<!--        order by tlirl0407.REC_CREATE_TIME desc-->
<!--        limit 1)                                         as       "next_target",-->
<!--        UNLOAD_LEAVE_FLAG                                 as       "unloadLeaveFlag",-->
<!--        UNLOAD_LEAVE_FLAG,-->
<!--        a.REC_CREATOR                                     as       "recCreator",-->
<!--        a.REC_CREATOR,-->
<!--        a.REC_CREATOR_NAME                                as       "recCreatorName",-->
<!--        a.REC_CREATOR_NAME,-->
<!--        a.REC_CREATE_TIME                                 as       "recCreateTime",-->
<!--        a.REC_CREATE_TIME,-->
<!--        a.REC_REVISOR                                     as       "recRevisor",-->
<!--        a.REC_REVISOR,-->
<!--        a.REC_REVISOR_NAME                                as       "recRevisorName",-->
<!--        a.REC_REVISOR_NAME,-->
<!--        a.REC_REVISE_TIME                                 as       "recReviseTime",-->
<!--        a.REC_REVISE_TIME,-->
<!--        a.DEL_FLAG                                        as       "delFlag",-->
<!--        a.DEL_FLAG,-->
<!--        a.REMARK                                          as       "remark",-->
<!--        a.SYS_REMARK                                      as       "sysRemark",-->
<!--        a.SYS_REMARK,-->
<!--        a.UUID                                            as       "uuid",-->
<!--        tlirl0201.IS_RESERVATION                                   "isReservation",-->
<!--        (select if(count(*) > 0, 1, 0)-->
<!--        from MELI.tlirl0201 b-->
<!--        where 1 = 1-->
<!--        and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--        and b.DEL_FLAG = '0')                                   "whet_appointment",-->
<!--        tlirl0201.START_OF_TRANSPORT                               "startOfTransport",-->
<!--        tlirl0201.START_OF_TRANSPORT                               "start_of_transport",-->
<!--        tlirl0201.PURPOSE_OF_TRANSPORT                             "purposeOfTransport",-->
<!--        tlirl0201.PURPOSE_OF_TRANSPORT                             "purpose_of_transport",-->
<!--        tlirl0201.CUSTOMER_NAME                                    "customerName",-->
<!--        tlirl0201.CUSTOMER_NAME                                    "customer_name",-->
<!--        tlirl0201.STATUS                                           "appointmentStatus",-->
<!--        tlirl0201.STATUS                                           "appointment_status",-->
<!--        tlirl0201.RESERVATION_DATE                                 "reservationDate",-->
<!--        tlirl0201.RESERVATION_DATE                                 "reservation_date",-->
<!--        tlirl0201.RESERVATION_TIME                                 "reservationTime",-->
<!--        tlirl0201.RESERVATION_TIME                                 "reservation_time",-->
<!--        ifnull(tlirl0302.LATE_EARLY_FLAG, 0)                       "lateEarlyFlag",-->
<!--        ifnull(tlirl0302.LATE_EARLY_FLAG, 0)                       "late_early_flag",-->
<!--        (case-->
<!--        when tlirl0302.VOUCHER_NUM = ' ' then case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '20'-->
<!--        when tlirl0302.BUSINESS_TYPE = '20' then '60'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30' then '40'-->
<!--        when tlirl0302.BUSINESS_TYPE = '40' then '50'-->
<!--        when tlirl0302.BUSINESS_TYPE = '50' then '70'-->
<!--        when tlirl0302.BUSINESS_TYPE = '60' then '80' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null THEN case-->
<!--        when a.HAND_TYPE = '10'-->
<!--        then '10'-->
<!--        when a.HAND_TYPE = '30'-->
<!--        then '30' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '10'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30'-->
<!--        then '30' end end end) as       "businessType",-->
<!--        (case-->
<!--        when tlirl0302.VOUCHER_NUM = ' ' then case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '20'-->
<!--        when tlirl0302.BUSINESS_TYPE = '20' then '60'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30' then '40'-->
<!--        when tlirl0302.BUSINESS_TYPE = '40' then '50'-->
<!--        when tlirl0302.BUSINESS_TYPE = '50' then '70'-->
<!--        when tlirl0302.BUSINESS_TYPE = '60' then '80' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = ' ' || tlirl0302.BUSINESS_TYPE = null THEN case-->
<!--        when a.HAND_TYPE = '10'-->
<!--        then '10'-->
<!--        when a.HAND_TYPE = '30'-->
<!--        then '30' end-->
<!--        else case-->
<!--        when tlirl0302.BUSINESS_TYPE = '10' then '10'-->
<!--        when tlirl0302.BUSINESS_TYPE = '30'-->
<!--        then '30' end end end) as       "business_type",-->
<!--        tlirl0302.VOUCHER_NUM                                      "voucherNum",-->
<!--        tlirl0302.VOUCHER_NUM                                      "voucher_num",-->
<!--        ifnull((select QUEUE_DATE-->
<!--        from MELI.tlirl0402 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0'), (select QUEUE_DATE-->
<!--        from MELI.tlirl0409 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0')) "callDate",-->
<!--        ifnull((select QUEUE_DATE-->
<!--        from MELI.tlirl0402 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0'), (select QUEUE_DATE-->
<!--        from MELI.tlirl0409 b-->
<!--        where 1 = 1-->
<!--        and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and b.DEL_FLAG = '0')) "call_date",-->
<!--        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theTimeFromRegistrationToEntry",-->
<!--        if(a.ENTER_FACTORY != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_time_from_registration_to_entry",-->
<!--        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'))) >-->
<!--        0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s')),-->
<!--        0),-->
<!--        '')                                                     "theTimeFromEnteringTheFactoryToTheStartOfTheJob",-->
<!--        if(a.BEGIN_ENTRUCKING_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'))) >-->
<!--        0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s')),-->
<!--        0),-->
<!--        '')                                                     "the_time_from_entering_the_factory_to_the_start_of_the_job",-->
<!--        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",-->
<!--        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(((select min(tlirl0406.LOAD_DATE)-->
<!--        from MELI.tlirl0406 tlirl0406-->
<!--        where tlirl0406.SEG_NO = a.SEG_NO-->
<!--        and tlirl0406.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--        and tlirl0406.VEHICLE_NO = a.VEHICLE_NO-->
<!--        and tlirl0406.STATUS != '00'-->
<!--        limit 1)), '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theTimeFromTheCompletionOfTheJobToTheFactory",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_time_from_the_completion_of_the_job_to_the_factory",-->
<!--        if(a.COMPLETE_UNINSTALL_TIME != ' ', if(-->
<!--        (TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')), 0),-->
<!--        "")                                                     "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",-->
<!--        if(COMPLETE_UNINSTALL_TIME != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s'))) >-->
<!--        0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.COMPLETE_UNINSTALL_TIME, '%Y%m%d%H%i%s')),-->
<!--        0),-->
<!--        "")                                                     "the_time_from_entering_the_factory_to_the_completion_of_the_job",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "theTimeFromEnteringTheFactoryToLeavingTheFactory",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.ENTER_FACTORY, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "the_time_from_entering_the_factory_to_leaving_the_factory",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "registeredToTheFactoryTime",-->
<!--        if(a.LEAVE_FACTORY_DATE != ' ', if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.CHECK_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s')), 0),-->
<!--        '')                                                     "registered_to_the_factory_time",-->
<!--        tlirl0308.SIGN_OFF_TIME                           as       "sign_off_time",-->
<!--        tlirl0308.SIGN_OFF_TIME                           as       "signOffTime",-->
<!--        CASE-->
<!--        WHEN (TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0 THEN CONCAT(-->
<!--        FLOOR(TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')) / 60), '时', MOD(-->
<!--        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')), 60), '分')-->
<!--        WHEN a.LEAVE_FACTORY_DATE != '' and tlirl0308.SIGN_OFF_TIME != '' then '0时0分'-->
<!--        ELSE '' END                                            "theTimeFromTheFactoryToTheTimeOfReceipt",-->
<!--        if((TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s'))) > 0,-->
<!--        TIMESTAMPDIFF(minute, STR_TO_DATE(a.LEAVE_FACTORY_DATE, '%Y%m%d%H%i%s'),-->
<!--        STR_TO_DATE(tlirl0308.SIGN_OFF_TIME, '%Y%m%d%H%i%s')),-->
<!--        0)                                                      "the_time_from_the_factory_to_the_time_of_receipt",-->
<!--        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signing_location_longitude",-->
<!--        tlirl0308.SIGNING_LOCATION_LONGITUDE              as       "signingLocationLongitude",-->
<!--        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signing_location_latitude",-->
<!--        tlirl0308.SIGNING_LOCATION_LATITUDE               as       "signingLocationLatitude",-->
<!--        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitude_latitude_check",-->
<!--        tlirl0308.LONGITUDE_LATITUDE_CHECK                as       "longitudeLatitudeCheck",-->
<!--        tlirl0308.FINAL_STATION_LONGITUDE                 as       "final_station_longitude",-->
<!--        tlirl0308.FINAL_STATION_LONGITUDE                 as       "finalStationLongitude",-->
<!--        tlirl0308.FINAL_STATION_LATITUDE                  as       "final_station_latitude",-->
<!--        tlirl0308.FINAL_STATION_LATITUDE                  as       "finalStationLatitude",-->
<!--        ifnull(tlirl0308.outWeight, 0)                    as       "outWeight",-->
<!--        ifnull(tlirl0308.outQuantity, 0)                  as       "outQuantity",-->
<!--        ifnull(tlirl0308.inWeight, 0)                     as       "inWeight",-->
<!--        ifnull(tlirl0308.inQuantity, 0)                   as       "inQuantity",-->
<!--        tlirl0308.SIGNING_ADDRESS                         AS       "signingAddress",-->
<!--        tlirl0308.SIGNING_ADDRESS                         AS       "signing_address",-->
<!--        tlirl0308.DEST_SPOT_ADDR                          AS       "destSpotAddr",-->
<!--        tlirl0308.DEST_SPOT_ADDR                          AS       "dest_spot_addr",-->
<!--        a.EXPECTED_LOADING_TIME as "expectedLoadingTime"-->
<!--        from MELI.tlirl0311 a-->
<!--        left join meli.tlirl0201 tlirl0201-->
<!--        on tlirl0201.SEG_NO = a.SEG_NO-->
<!--        and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--        and tlirl0201.STATUS != '00'-->
<!--        and tlirl0201.VEHICLE_NO = a.VEHICLE_NO-->
<!--        left join (select tlirl0308.SEG_NO,-->
<!--        tlirl0308.VEHICLE_NO,-->
<!--        tlirl0308.CAR_TRACE_NO,-->
<!--        max(tlirl0308.SIGNING_LOCATION_LONGITUDE) as "SIGNING_LOCATION_LONGITUDE",-->
<!--        max(tlirl0308.SIGNING_LOCATION_LATITUDE)  as "SIGNING_LOCATION_LATITUDE",-->
<!--        max(tlirl0308.LONGITUDE_LATITUDE_CHECK)   as "LONGITUDE_LATITUDE_CHECK",-->
<!--        max(tlirl0308.FINAL_STATION_LONGITUDE)    as "FINAL_STATION_LONGITUDE",-->
<!--        max(tlirl0308.FINAL_STATION_LATITUDE)     as "FINAL_STATION_LATITUDE",-->
<!--        max(tlirl0308.SIGN_OFF_TIME)              as "SIGN_OFF_TIME",-->
<!--        max(tlirl0308.SIGNING_ADDRESS)            as "SIGNING_ADDRESS",-->
<!--        max(tlirl0308.DEST_SPOT_ADDR)             as "DEST_SPOT_ADDR",-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN (select count(1)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS outQuantity,-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN (select count(1)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS inQuantity,-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '20' THEN (select sum(tlirl03082.WEIGHT)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS outWeight,-->
<!--        MAX(CASE-->
<!--        WHEN tlirl0308.PUT_IN_OUT_FLAG = '10' THEN (select sum(tlirl03082.WEIGHT)-->
<!--        from meli.tlirl0308 tlirl03082-->
<!--        where 1 = 1-->
<!--        and tlirl0308.SEG_NO = tlirl03082.SEG_NO-->
<!--        and tlirl0308.CAR_TRACE_NO = tlirl03082.CAR_TRACE_NO-->
<!--        and tlirl03082.VEHICLE_NO = tlirl0308.VEHICLE_NO)-->
<!--        ELSE 0 END)                       AS inWeight-->
<!--        from MELI.tlirl0308 tlirl0308-->
<!--        where tlirl0308.SEG_NO = #segNo#-->
<!--        and tlirl0308.DEL_FLAG = '0'-->
<!--        group by tlirl0308.SEG_NO, tlirl0308.VEHICLE_NO, tlirl0308.CAR_TRACE_NO,-->
<!--        PUT_IN_OUT_FLAG) tlirl0308-->
<!--        on tlirl0308.SEG_NO = a.SEG_NO and tlirl0308.CAR_TRACE_NO = a.CAR_TRACE_NO and-->
<!--        tlirl0308.VEHICLE_NO = a.VEHICLE_NO-->
<!--        left join meli.tlirl0302 tlirl0302-->
<!--        on tlirl0302.SEG_NO = a.SEG_NO and tlirl0302.CAR_TRACE_NO = a.CAR_TRACE_NO and-->
<!--        tlirl0302.STATUS != '00'-->
<!--        where 1 = 1-->
<!--        <isNotEmpty prepend=" AND " property="segNo">-->
<!--            a.SEG_NO = #segNo#-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="driverName">-->
<!--            a.DRIVER_NAME like concat('%',#driverName#,'%')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="telNum">-->
<!--            a.TEL_NUM like concat('%',#telNum#,'%')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="vehicleNo">-->
<!--            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始预约日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="reservationDateStart">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止预约日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="reservationDateEnd">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始登记日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="checkDateStart">-->
<!--            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止登记日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="checkDateEnd">-->
<!--            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始进厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="enterFactoryStart">-->
<!--            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止进厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="enterFactoryEnd">-->
<!--            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;起始出厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">-->
<!--            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;截止出厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">-->
<!--            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;出厂日期&ndash;&gt;-->
<!--        <isNotEmpty prepend=" and " property="leaveFactoryDate">-->
<!--            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="businessType">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0302 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.BUSINESS_TYPE = #businessType#-->
<!--            <isNotEmpty prepend=" and " property="voucherNumN">-->
<!--                b.VOUCHER_NUM = ' '-->
<!--            </isNotEmpty>-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="handType">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0302 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.HAND_TYPE = #handType#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="voucherNum">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0308 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="packId">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0308 b-->
<!--            WHERE 1 = 1-->
<!--            and b.CAR_TRACE_NO = a.CAR_TRACE_NO-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.PACK_ID like concat('%',#packId#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="startOfTransport">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="purposeOfTransport">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerId">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_ID = #customerId#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerName">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_NAME = #customerName#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerId2">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_ID = #customerId2#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="customerName2">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.CUSTOMER_NAME = #customerName2#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="carTraceNo">-->
<!--            a.CAR_TRACE_NO = #carTraceNo#-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="status">-->
<!--            a.STATUS in ($status$)-->
<!--        </isNotEmpty>-->
<!--        <isEmpty prepend=" AND " property="status">-->
<!--            a.STATUS != '00'-->
<!--        </isEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="inHouse">-->
<!--            a.STATUS in ('20','30','40')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="outHouse">-->
<!--            a.STATUS in ('50','60')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="noHouse">-->
<!--            a.STATUS in ('10')-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="statusListStr">-->
<!--            a.STATUS in ( $statusListStr$ )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="statusNotListStr">-->
<!--            a.STATUS NOT in ( $statusNotListStr$ )-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="delFlag">-->
<!--            a.DEL_FLAG = #delFlag#-->
<!--        </isNotEmpty>-->
<!--        &lt;!&ndash;<isNotEmpty prepend=" AND " property="status">-->
<!--            STATUS <![CDATA[<]]> #status#-->
<!--        </isNotEmpty>&ndash;&gt;-->
<!--        <isNotEmpty prepend=" AND " property="handPointId">-->
<!--            a.TARGET_HAND_POINT_ID = #handPointId#-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="statusList">-->
<!--            a.STATUS in-->
<!--            <iterate property="statusList" open="("-->
<!--                     close=")" conjunction=" , ">-->
<!--                #statusList[]#-->
<!--            </iterate>-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" and " property="isReservation">-->
<!--            EXISTS (-->
<!--            SELECT-->
<!--            1-->
<!--            FROM-->
<!--            ${meliSchema}.tlirl0201 b-->
<!--            WHERE 1 = 1-->
<!--            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER-->
<!--            and b.DEL_FLAG = '0'-->
<!--            and b.IS_RESERVATION = #isReservation#-->
<!--            )-->
<!--        </isNotEmpty>-->
<!--        ) a1-->
<!--        <dynamic prepend="ORDER BY">-->
<!--            <isNotEmpty property="orderBy">-->
<!--                $orderBy$-->
<!--            </isNotEmpty>-->
<!--            <isEmpty property="orderBy">-->
<!--                a1.CHECK_DATE desc,-->
<!--                a1.CAR_TRACE_NO,-->
<!--                a1.REC_CREATE_TIME desc-->
<!--            </isEmpty>-->
<!--        </dynamic>-->

<!--    </select>-->



    <select id="query" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.SEG_NO as "segNo",
        a.SEG_NO,
        a.UNIT_CODE as "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO as "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS as "status",
        a.HAND_TYPE as "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO as "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD as "idCard",
        a.ID_CARD,
        a.DRIVER_NAME as "driverName",
        a.DRIVER_NAME,
        a.TEL_NUM as "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER as "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE as "checkDate",
        a.CHECK_DATE,
        a.ENTER_FACTORY as "enterFactory",
        a.ENTER_FACTORY,

        COALESCE(load_data.min_load_date, '') as "beginEntruckingTime",

         CASE
         WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
         THEN a.BEGIN_ENTRUCKING_TIME
         ELSE ' '
         END as "currentBeginEntruckingTime",

         CASE
         WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
         THEN a.COMPLETE_UNINSTALL_TIME
         ELSE ' '
         END as "currentcompleteUninstallTime",

         COALESCE(finish_data.max_finish_date, '') as "completeUninstallTime",
         a.COMPLETE_UNINSTALL_TIME,
         a.LEAVE_FACTORY_DATE as "leaveFactoryDate",
         a.LEAVE_FACTORY_DATE,
         a.CUSTOMER_SIGNING_TIME as "customerSigningTime",
         a.CUSTOMER_SIGNING_TIME,
         a.TARGET_HAND_POINT_ID as "targetHandPointId",
         a.TARGET_HAND_POINT_ID,

         COALESCE(hp_target.HAND_POINT_NAME, '') as "targetHandPointName",
         COALESCE(hp_target.HAND_POINT_NAME, '') as "target_hand_point_name",
         a.CURRENT_HAND_POINT_ID as "currentHandPointId",
         a.CURRENT_HAND_POINT_ID,
         COALESCE(hp_current.HAND_POINT_NAME, '') as "currentHandPointName",
         COALESCE(hp_current.HAND_POINT_NAME, '') as "current_hand_point_name",

         a.FACTORY_AREA as "factoryArea",
         a.FACTORY_AREA,
         a.FACTORY_AREA_NAME as "factoryAreaName",
         a.FACTORY_AREA_NAME,

         COALESCE(next_target_data.next_target, '') as "nextTarget",
         COALESCE(next_target_data.next_target, '') as "next_target",

         a.UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",
         a.UNLOAD_LEAVE_FLAG,
         a.REC_CREATOR as "recCreator",
         a.REC_CREATOR,
         a.REC_CREATOR_NAME as "recCreatorName",
         a.REC_CREATOR_NAME,
         a.REC_CREATE_TIME as "recCreateTime",
         a.REC_CREATE_TIME,
         a.REC_REVISOR as "recRevisor",
         a.REC_REVISOR,
         a.REC_REVISOR_NAME as "recRevisorName",
         a.REC_REVISOR_NAME,
         a.REC_REVISE_TIME as "recReviseTime",
         a.REC_REVISE_TIME,
         a.DEL_FLAG as "delFlag",
         a.DEL_FLAG,
         a.REMARK as "remark",
         a.SYS_REMARK as "sysRemark",
         a.SYS_REMARK,
         a.UUID as "uuid",

         COALESCE(reservation.IS_RESERVATION, '') as "isReservation",
         CASE WHEN reservation.RESERVATION_NUMBER IS NOT NULL THEN 1 ELSE 0 END as "whet_appointment",
         COALESCE(reservation.START_OF_TRANSPORT, '') as "startOfTransport",
         COALESCE(reservation.START_OF_TRANSPORT, '') as "start_of_transport",
         COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purposeOfTransport",
         COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purpose_of_transport",
         COALESCE(reservation.CUSTOMER_NAME, '') as "customerName",
         COALESCE(reservation.CUSTOMER_NAME, '') as "customer_name",
         COALESCE(reservation.STATUS, '') as "appointmentStatus",
         COALESCE(reservation.STATUS, '') as "appointment_status",
         COALESCE(reservation.RESERVATION_DATE, '') as "reservationDate",
         COALESCE(reservation.RESERVATION_DATE, '') as "reservation_date",
         COALESCE(reservation.RESERVATION_TIME, '') as "reservationTime",
         COALESCE(reservation.RESERVATION_TIME, '') as "reservation_time",

         COALESCE(business.LATE_EARLY_FLAG, 0) as "lateEarlyFlag",
         COALESCE(business.LATE_EARLY_FLAG, 0) as "late_early_flag",

         CASE
         WHEN COALESCE(business.VOUCHER_NUM, ' ') = ' ' THEN
         CASE business.BUSINESS_TYPE
         WHEN '10' THEN '20'
         WHEN '20' THEN '60'
         WHEN '30' THEN '40'
         WHEN '40' THEN '50'
         WHEN '50' THEN '70'
         WHEN '60' THEN '80'
         ELSE ''
         END
         ELSE
         CASE
         WHEN COALESCE(business.BUSINESS_TYPE, ' ') IN (' ', '') THEN
         CASE a.HAND_TYPE
         WHEN '10' THEN '10'
         WHEN '30' THEN '30'
         ELSE ''
         END
         ELSE
         CASE business.BUSINESS_TYPE
         WHEN '10' THEN '10'
         WHEN '30' THEN '30'
         ELSE ''
         END
         END
         END as "businessType",

         CASE
         WHEN COALESCE(business.VOUCHER_NUM, ' ') = ' ' THEN
         CASE business.BUSINESS_TYPE
         WHEN '10' THEN '20'
         WHEN '20' THEN '60'
         WHEN '30' THEN '40'
         WHEN '40' THEN '50'
         WHEN '50' THEN '70'
         WHEN '60' THEN '80'
         ELSE ''
         END
         ELSE
         CASE
         WHEN COALESCE(business.BUSINESS_TYPE, ' ') IN (' ', '') THEN
         CASE a.HAND_TYPE
         WHEN '10' THEN '10'
         WHEN '30' THEN '30'
         ELSE ''
         END
         ELSE
         CASE business.BUSINESS_TYPE
         WHEN '10' THEN '10'
         WHEN '30' THEN '30'
         ELSE ''
         END
         END
         END as "business_type",

         COALESCE(business.VOUCHER_NUM, '') as "voucherNum",
         COALESCE(business.VOUCHER_NUM, '') as "voucher_num",

         COALESCE(queue_data.QUEUE_DATE, '') as "callDate",
         COALESCE(queue_data.QUEUE_DATE, '') as "call_date",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "theTimeFromRegistrationToEntry",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "the_time_from_registration_to_entry",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "theTimeFromEnteringTheFactoryToTheStartOfTheJob",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "the_time_from_entering_the_factory_to_the_start_of_the_job",

         CASE
         WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
         AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",

         CASE
         WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
         AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",

         CASE
         WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
         AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "theTimeFromTheCompletionOfTheJobToTheFactory",

         CASE
         WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
         AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "the_time_from_the_completion_of_the_job_to_the_factory",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "the_time_from_entering_the_factory_to_the_completion_of_the_job",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "theTimeFromEnteringTheFactoryToLeavingTheFactory",

         CASE
         WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
         AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "the_time_from_entering_the_factory_to_leaving_the_factory",

         CASE
         WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
         AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "registeredToTheFactoryTime",

         CASE
         WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
         AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "registered_to_the_factory_time",

         COALESCE(sign_data.SIGN_OFF_TIME, '') as "sign_off_time",
         COALESCE(sign_data.SIGN_OFF_TIME, '') as "signOffTime",

         CASE
         WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
         AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
         CASE
         WHEN TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) > 0
         THEN CONCAT(
         FLOOR(TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) / 60),
         '时',
         MOD(TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')), 60),
         '分')
         ELSE '0时0分'
         END
         ELSE ''
         END as "theTimeFromTheFactoryToTheTimeOfReceipt",

         CASE
         WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
         AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
         GREATEST(0, TIMESTAMPDIFF(MINUTE,
         STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
         STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')))
         ELSE 0
         END as "the_time_from_the_factory_to_the_time_of_receipt",

         COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signing_location_longitude",
         COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signingLocationLongitude",
         COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signing_location_latitude",
         COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signingLocationLatitude",
         COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitude_latitude_check",
         COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitudeLatitudeCheck",
         COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "final_station_longitude",
         COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "finalStationLongitude",
         COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "final_station_latitude",
         COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "finalStationLatitude",
         COALESCE(sign_data.outWeight, 0) as "outWeight",
         COALESCE(sign_data.outQuantity, 0) as "outQuantity",
         COALESCE(sign_data.inWeight, 0) as "inWeight",
         COALESCE(sign_data.inQuantity, 0) as "inQuantity",
         COALESCE(sign_data.SIGNING_ADDRESS, '') as "signingAddress",
         COALESCE(sign_data.SIGNING_ADDRESS, '') as "signing_address",
         COALESCE(sign_data.DEST_SPOT_ADDR, '') as "destSpotAddr",
         COALESCE(sign_data.DEST_SPOT_ADDR, '') as "dest_spot_addr",
         a.EXPECTED_LOADING_TIME as "expectedLoadingTime"

         FROM (
         SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
         ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
         ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
         LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
         CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
         UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
         REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
         REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME
         FROM MELI.tlirl0301 a
         WHERE 1=1
         <isNotEmpty prepend=" AND " property="status">
             a.STATUS in ($status$)
         </isNotEmpty>
         <isEmpty prepend=" AND " property="status">
             a.STATUS != '00'
         </isEmpty>
         <isNotEmpty prepend=" AND " property="inHouse">
             a.STATUS in ('20','30','40')
         </isNotEmpty>
         <isNotEmpty prepend=" AND " property="outHouse">
             a.STATUS in ('50','60')
         </isNotEmpty>
         <isNotEmpty prepend=" AND " property="noHouse">
             a.STATUS in ('10')
         </isNotEmpty>
         <isNotEmpty prepend=" AND " property="segNo">
             a.SEG_NO = #segNo#
         </isNotEmpty>
         <isNotEmpty prepend=" AND " property="telNum">
             a.TEL_NUM like concat('%',#telNum#,'%')
         </isNotEmpty>
         <isNotEmpty prepend=" AND " property="driverName">
             a.DRIVER_NAME like concat('%',#driverName#,'%')
         </isNotEmpty>
         <isNotEmpty prepend=" AND " property="vehicleNo">
             a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
         </isNotEmpty>
         <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        UNION ALL

        SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
        ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
        ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
        CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
        UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
        REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
        REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME
        FROM MELI.tlirl0311 a
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>

        ) a

        LEFT JOIN MELI.tlirl0201 reservation
        ON reservation.SEG_NO = a.SEG_NO
        AND reservation.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        AND reservation.STATUS != '00'
        AND reservation.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MIN(LOAD_DATE) as min_load_date
        FROM MELI.tlirl0406
        WHERE SEG_NO = #segNo# AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) load_data ON load_data.SEG_NO = a.SEG_NO
        AND load_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND load_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MAX(FINISH_LOAD_DATE) as max_finish_date
        FROM MELI.tlirl0407
        WHERE SEG_NO = #segNo# AND NEXT_TATGET = '20' AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) finish_data ON finish_data.SEG_NO = a.SEG_NO
        AND finish_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND finish_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT DISTINCT
        t1.SEG_NO,
        t1.CAR_TRACE_NO,
        t1.VEHICLE_NO,
        t1.NEXT_TATGET as next_target
        FROM MELI.tlirl0407 t1
        WHERE t1.SEG_NO = #segNo# AND t1.STATUS != '00'
        AND t1.REC_CREATE_TIME = (
        SELECT MAX(t2.REC_CREATE_TIME)
        FROM MELI.tlirl0407 t2
        WHERE t2.SEG_NO = t1.SEG_NO
        AND t2.CAR_TRACE_NO = t1.CAR_TRACE_NO
        AND t2.VEHICLE_NO = t1.VEHICLE_NO
        AND t2.STATUS != '00'
        )
        ) next_target_data ON next_target_data.SEG_NO = a.SEG_NO
        AND next_target_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND next_target_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0304 hp_target
        ON hp_target.SEG_NO = a.SEG_NO
        AND hp_target.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        AND hp_target.STATUS = '30'
        AND hp_target.DEL_FLAG = '0'

        LEFT JOIN MELI.tlirl0304 hp_current
        ON hp_current.SEG_NO = a.SEG_NO
        AND hp_current.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        AND hp_current.STATUS = '30'
        AND hp_current.DEL_FLAG = '0'

        LEFT JOIN (
        SELECT
        SEG_NO, VEHICLE_NO, CAR_TRACE_NO,
        MAX(SIGNING_LOCATION_LONGITUDE) as SIGNING_LOCATION_LONGITUDE,
        MAX(SIGNING_LOCATION_LATITUDE) as SIGNING_LOCATION_LATITUDE,
        MAX(LONGITUDE_LATITUDE_CHECK) as LONGITUDE_LATITUDE_CHECK,
        MAX(FINAL_STATION_LONGITUDE) as FINAL_STATION_LONGITUDE,
        MAX(FINAL_STATION_LATITUDE) as FINAL_STATION_LATITUDE,
        MAX(SIGN_OFF_TIME) as SIGN_OFF_TIME,
        MAX(SIGNING_ADDRESS) as SIGNING_ADDRESS,
        MAX(DEST_SPOT_ADDR) as DEST_SPOT_ADDR,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN 1 ELSE 0 END) as outQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN 1 ELSE 0 END) as inQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as outWeight,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as inWeight
        FROM MELI.tlirl0308
        WHERE SEG_NO = #segNo# AND DEL_FLAG = '0'
        GROUP BY SEG_NO, VEHICLE_NO, CAR_TRACE_NO
        ) sign_data ON sign_data.SEG_NO = a.SEG_NO
        AND sign_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND sign_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0302 business
        ON business.SEG_NO = a.SEG_NO
        AND business.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND business.STATUS != '00'

        LEFT JOIN (
        SELECT CAR_TRACE_NO, MAX(QUEUE_DATE) as QUEUE_DATE
        FROM (
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0402 WHERE DEL_FLAG = '0'
        UNION ALL
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0409 WHERE DEL_FLAG = '0'
        ) queue_union
        GROUP BY CAR_TRACE_NO
        ) queue_data ON queue_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        ORDER BY a.CHECK_DATE DESC, a.CAR_TRACE_NO, a.REC_CREATE_TIME DESC
    </select>

    <select id="count"
            resultClass="int">
        select count(*) from
        ( SELECT
        a.SEG_NO as "segNo",
        a.SEG_NO,
        a.UNIT_CODE as "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO as "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS as "status",
        a.HAND_TYPE as "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO as "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD as "idCard",
        a.ID_CARD,
        a.DRIVER_NAME as "driverName",
        a.DRIVER_NAME,
        a.TEL_NUM as "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER as "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE as "checkDate",
        a.CHECK_DATE,
        a.ENTER_FACTORY as "enterFactory",
        a.ENTER_FACTORY,

        COALESCE(load_data.min_load_date, '') as "beginEntruckingTime",

        CASE
        WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
        THEN a.BEGIN_ENTRUCKING_TIME
        ELSE ' '
        END as "currentBeginEntruckingTime",

        CASE
        WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
        THEN a.COMPLETE_UNINSTALL_TIME
        ELSE ' '
        END as "currentcompleteUninstallTime",

        COALESCE(finish_data.max_finish_date, '') as "completeUninstallTime",
        a.COMPLETE_UNINSTALL_TIME,
        a.LEAVE_FACTORY_DATE as "leaveFactoryDate",
        a.LEAVE_FACTORY_DATE,
        a.CUSTOMER_SIGNING_TIME as "customerSigningTime",
        a.CUSTOMER_SIGNING_TIME,
        a.TARGET_HAND_POINT_ID as "targetHandPointId",
        a.TARGET_HAND_POINT_ID,

        COALESCE(hp_target.HAND_POINT_NAME, '') as "targetHandPointName",
        COALESCE(hp_target.HAND_POINT_NAME, '') as "target_hand_point_name",
        a.CURRENT_HAND_POINT_ID as "currentHandPointId",
        a.CURRENT_HAND_POINT_ID,
        COALESCE(hp_current.HAND_POINT_NAME, '') as "currentHandPointName",
        COALESCE(hp_current.HAND_POINT_NAME, '') as "current_hand_point_name",

        a.FACTORY_AREA as "factoryArea",
        a.FACTORY_AREA,
        a.FACTORY_AREA_NAME as "factoryAreaName",
        a.FACTORY_AREA_NAME,

        COALESCE(next_target_data.next_target, '') as "nextTarget",
        COALESCE(next_target_data.next_target, '') as "next_target",

        a.UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",
        a.UNLOAD_LEAVE_FLAG,
        a.REC_CREATOR as "recCreator",
        a.REC_CREATOR,
        a.REC_CREATOR_NAME as "recCreatorName",
        a.REC_CREATOR_NAME,
        a.REC_CREATE_TIME as "recCreateTime",
        a.REC_CREATE_TIME,
        a.REC_REVISOR as "recRevisor",
        a.REC_REVISOR,
        a.REC_REVISOR_NAME as "recRevisorName",
        a.REC_REVISOR_NAME,
        a.REC_REVISE_TIME as "recReviseTime",
        a.REC_REVISE_TIME,
        a.DEL_FLAG as "delFlag",
        a.DEL_FLAG,
        a.REMARK as "remark",
        a.SYS_REMARK as "sysRemark",
        a.SYS_REMARK,
        a.UUID as "uuid",

        COALESCE(reservation.IS_RESERVATION, '') as "isReservation",
        CASE WHEN reservation.RESERVATION_NUMBER IS NOT NULL THEN 1 ELSE 0 END as "whet_appointment",
        COALESCE(reservation.START_OF_TRANSPORT, '') as "startOfTransport",
        COALESCE(reservation.START_OF_TRANSPORT, '') as "start_of_transport",
        COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purposeOfTransport",
        COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purpose_of_transport",
        COALESCE(reservation.CUSTOMER_NAME, '') as "customerName",
        COALESCE(reservation.CUSTOMER_NAME, '') as "customer_name",
        COALESCE(reservation.STATUS, '') as "appointmentStatus",
        COALESCE(reservation.STATUS, '') as "appointment_status",
        COALESCE(reservation.RESERVATION_DATE, '') as "reservationDate",
        COALESCE(reservation.RESERVATION_DATE, '') as "reservation_date",
        COALESCE(reservation.RESERVATION_TIME, '') as "reservationTime",
        COALESCE(reservation.RESERVATION_TIME, '') as "reservation_time",

        COALESCE(business.LATE_EARLY_FLAG, 0) as "lateEarlyFlag",
        COALESCE(business.LATE_EARLY_FLAG, 0) as "late_early_flag",

        CASE
        WHEN COALESCE(business.VOUCHER_NUM, ' ') = ' ' THEN
        CASE business.BUSINESS_TYPE
        WHEN '10' THEN '20'
        WHEN '20' THEN '60'
        WHEN '30' THEN '40'
        WHEN '40' THEN '50'
        WHEN '50' THEN '70'
        WHEN '60' THEN '80'
        ELSE ''
        END
        ELSE
        CASE
        WHEN COALESCE(business.BUSINESS_TYPE, ' ') IN (' ', '') THEN
        CASE a.HAND_TYPE
        WHEN '10' THEN '10'
        WHEN '30' THEN '30'
        ELSE ''
        END
        ELSE
        CASE business.BUSINESS_TYPE
        WHEN '10' THEN '10'
        WHEN '30' THEN '30'
        ELSE ''
        END
        END
        END as "businessType",

        CASE
        WHEN COALESCE(business.VOUCHER_NUM, ' ') = ' ' THEN
        CASE business.BUSINESS_TYPE
        WHEN '10' THEN '20'
        WHEN '20' THEN '60'
        WHEN '30' THEN '40'
        WHEN '40' THEN '50'
        WHEN '50' THEN '70'
        WHEN '60' THEN '80'
        ELSE ''
        END
        ELSE
        CASE
        WHEN COALESCE(business.BUSINESS_TYPE, ' ') IN (' ', '') THEN
        CASE a.HAND_TYPE
        WHEN '10' THEN '10'
        WHEN '30' THEN '30'
        ELSE ''
        END
        ELSE
        CASE business.BUSINESS_TYPE
        WHEN '10' THEN '10'
        WHEN '30' THEN '30'
        ELSE ''
        END
        END
        END as "business_type",

        COALESCE(business.VOUCHER_NUM, '') as "voucherNum",
        COALESCE(business.VOUCHER_NUM, '') as "voucher_num",

        COALESCE(queue_data.QUEUE_DATE, '') as "callDate",
        COALESCE(queue_data.QUEUE_DATE, '') as "call_date",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromRegistrationToEntry",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_registration_to_entry",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToTheStartOfTheJob",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_the_start_of_the_job",

        CASE
        WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",

        CASE
        WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",

        CASE
        WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromTheCompletionOfTheJobToTheFactory",

        CASE
        WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_the_completion_of_the_job_to_the_factory",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_the_completion_of_the_job",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToLeavingTheFactory",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_leaving_the_factory",

        CASE
        WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "registeredToTheFactoryTime",

        CASE
        WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "registered_to_the_factory_time",

        COALESCE(sign_data.SIGN_OFF_TIME, '') as "sign_off_time",
        COALESCE(sign_data.SIGN_OFF_TIME, '') as "signOffTime",

        CASE
        WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
        AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
        CASE
        WHEN TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) > 0
        THEN CONCAT(
        FLOOR(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) / 60),
        '时',
        MOD(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')), 60),
        '分')
        ELSE '0时0分'
        END
        ELSE ''
        END as "theTimeFromTheFactoryToTheTimeOfReceipt",

        CASE
        WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
        AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_the_factory_to_the_time_of_receipt",

        COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signing_location_longitude",
        COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signingLocationLongitude",
        COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signing_location_latitude",
        COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signingLocationLatitude",
        COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitude_latitude_check",
        COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitudeLatitudeCheck",
        COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "final_station_longitude",
        COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "finalStationLongitude",
        COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "final_station_latitude",
        COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "finalStationLatitude",
        COALESCE(sign_data.outWeight, 0) as "outWeight",
        COALESCE(sign_data.outQuantity, 0) as "outQuantity",
        COALESCE(sign_data.inWeight, 0) as "inWeight",
        COALESCE(sign_data.inQuantity, 0) as "inQuantity",
        COALESCE(sign_data.SIGNING_ADDRESS, '') as "signingAddress",
        COALESCE(sign_data.SIGNING_ADDRESS, '') as "signing_address",
        COALESCE(sign_data.DEST_SPOT_ADDR, '') as "destSpotAddr",
        COALESCE(sign_data.DEST_SPOT_ADDR, '') as "dest_spot_addr",
        a.EXPECTED_LOADING_TIME as "expectedLoadingTime"

        FROM (
        SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
        ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
        ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
        CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
        UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
        REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
        REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME
        FROM MELI.tlirl0301 a
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        UNION ALL

        SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
        ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
        ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
        CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
        UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
        REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
        REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME
        FROM MELI.tlirl0311 a
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>

        ) a

        LEFT JOIN MELI.tlirl0201 reservation
        ON reservation.SEG_NO = a.SEG_NO
        AND reservation.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        AND reservation.STATUS != '00'
        AND reservation.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MIN(LOAD_DATE) as min_load_date
        FROM MELI.tlirl0406
        WHERE SEG_NO = #segNo# AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) load_data ON load_data.SEG_NO = a.SEG_NO
        AND load_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND load_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MAX(FINISH_LOAD_DATE) as max_finish_date
        FROM MELI.tlirl0407
        WHERE SEG_NO = #segNo# AND NEXT_TATGET = '20' AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) finish_data ON finish_data.SEG_NO = a.SEG_NO
        AND finish_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND finish_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT DISTINCT
        t1.SEG_NO,
        t1.CAR_TRACE_NO,
        t1.VEHICLE_NO,
        t1.NEXT_TATGET as next_target
        FROM MELI.tlirl0407 t1
        WHERE t1.SEG_NO = #segNo# AND t1.STATUS != '00'
        AND t1.REC_CREATE_TIME = (
        SELECT MAX(t2.REC_CREATE_TIME)
        FROM MELI.tlirl0407 t2
        WHERE t2.SEG_NO = t1.SEG_NO
        AND t2.CAR_TRACE_NO = t1.CAR_TRACE_NO
        AND t2.VEHICLE_NO = t1.VEHICLE_NO
        AND t2.STATUS != '00'
        )
        ) next_target_data ON next_target_data.SEG_NO = a.SEG_NO
        AND next_target_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND next_target_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0304 hp_target
        ON hp_target.SEG_NO = a.SEG_NO
        AND hp_target.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        AND hp_target.STATUS = '30'
        AND hp_target.DEL_FLAG = '0'

        LEFT JOIN MELI.tlirl0304 hp_current
        ON hp_current.SEG_NO = a.SEG_NO
        AND hp_current.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        AND hp_current.STATUS = '30'
        AND hp_current.DEL_FLAG = '0'

        LEFT JOIN (
        SELECT
        SEG_NO, VEHICLE_NO, CAR_TRACE_NO,
        MAX(SIGNING_LOCATION_LONGITUDE) as SIGNING_LOCATION_LONGITUDE,
        MAX(SIGNING_LOCATION_LATITUDE) as SIGNING_LOCATION_LATITUDE,
        MAX(LONGITUDE_LATITUDE_CHECK) as LONGITUDE_LATITUDE_CHECK,
        MAX(FINAL_STATION_LONGITUDE) as FINAL_STATION_LONGITUDE,
        MAX(FINAL_STATION_LATITUDE) as FINAL_STATION_LATITUDE,
        MAX(SIGN_OFF_TIME) as SIGN_OFF_TIME,
        MAX(SIGNING_ADDRESS) as SIGNING_ADDRESS,
        MAX(DEST_SPOT_ADDR) as DEST_SPOT_ADDR,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN 1 ELSE 0 END) as outQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN 1 ELSE 0 END) as inQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as outWeight,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as inWeight
        FROM MELI.tlirl0308
        WHERE SEG_NO = #segNo# AND DEL_FLAG = '0'
        GROUP BY SEG_NO, VEHICLE_NO, CAR_TRACE_NO
        ) sign_data ON sign_data.SEG_NO = a.SEG_NO
        AND sign_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND sign_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0302 business
        ON business.SEG_NO = a.SEG_NO
        AND business.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND business.STATUS != '00'

        LEFT JOIN (
        SELECT CAR_TRACE_NO, MAX(QUEUE_DATE) as QUEUE_DATE
        FROM (
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0402 WHERE DEL_FLAG = '0'
        UNION ALL
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0409 WHERE DEL_FLAG = '0'
        ) queue_union
        GROUP BY CAR_TRACE_NO
        ) queue_data ON queue_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        ORDER BY a.CHECK_DATE DESC, a.CAR_TRACE_NO, a.REC_CREATE_TIME DESC
        ) a1
    </select>


    <select id="queryCq" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT DISTINCT
        a.SEG_NO as "segNo",
        a.SEG_NO,
        a.UNIT_CODE as "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO as "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS as "status",
        a.HAND_TYPE as "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO as "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD as "idCard",
        a.ID_CARD,
        a.DRIVER_NAME as "driverName",
        a.DRIVER_NAME,
        a.TEL_NUM as "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER as "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE as "checkDate",
        a.CHECK_DATE,
        a.ENTER_FACTORY as "enterFactory",
        a.ENTER_FACTORY,

        COALESCE(load_data.min_load_date, '') as "beginEntruckingTime",

        CASE
        WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
        THEN a.BEGIN_ENTRUCKING_TIME
        ELSE ' '
        END as "currentBeginEntruckingTime",

        CASE
        WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
        THEN a.COMPLETE_UNINSTALL_TIME
        ELSE ' '
        END as "currentcompleteUninstallTime",

        COALESCE(finish_data.max_finish_date, '') as "completeUninstallTime",
        a.COMPLETE_UNINSTALL_TIME,
        a.LEAVE_FACTORY_DATE as "leaveFactoryDate",
        a.LEAVE_FACTORY_DATE,
        a.CUSTOMER_SIGNING_TIME as "customerSigningTime",
        a.CUSTOMER_SIGNING_TIME,
        a.TARGET_HAND_POINT_ID as "targetHandPointId",
        a.TARGET_HAND_POINT_ID,

        COALESCE(hp_target.HAND_POINT_NAME, '') as "targetHandPointName",
        COALESCE(hp_target.HAND_POINT_NAME, '') as "target_hand_point_name",
        a.CURRENT_HAND_POINT_ID as "currentHandPointId",
        a.CURRENT_HAND_POINT_ID,
        COALESCE(hp_current.HAND_POINT_NAME, '') as "currentHandPointName",
        COALESCE(hp_current.HAND_POINT_NAME, '') as "current_hand_point_name",

        a.FACTORY_AREA as "factoryArea",
        a.FACTORY_AREA,
        a.FACTORY_AREA_NAME as "factoryAreaName",
        a.FACTORY_AREA_NAME,

        COALESCE(next_target_data.next_target, '') as "nextTarget",
        COALESCE(next_target_data.next_target, '') as "next_target",

        a.UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",
        a.UNLOAD_LEAVE_FLAG,
        a.REC_CREATOR as "recCreator",
        a.REC_CREATOR,
        a.REC_CREATOR_NAME as "recCreatorName",
        a.REC_CREATOR_NAME,
        a.REC_CREATE_TIME as "recCreateTime",
        a.REC_CREATE_TIME,
        a.REC_REVISOR as "recRevisor",
        a.REC_REVISOR,
        a.REC_REVISOR_NAME as "recRevisorName",
        a.REC_REVISOR_NAME,
        a.REC_REVISE_TIME as "recReviseTime",
        a.REC_REVISE_TIME,
        a.DEL_FLAG as "delFlag",
        a.DEL_FLAG,
        a.REMARK as "remark",
        a.SYS_REMARK as "sysRemark",
        a.SYS_REMARK,
        a.UUID as "uuid",

        CASE WHEN reservation.RESERVATION_NUMBER IS NOT NULL THEN 1 ELSE 0 END as "whet_appointment",
        COALESCE(reservation.START_OF_TRANSPORT, '') as "startOfTransport",
        COALESCE(reservation.START_OF_TRANSPORT, '') as "start_of_transport",
        COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purposeOfTransport",
        COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purpose_of_transport",
        COALESCE(reservation.CUSTOMER_NAME, '') as "customerName",
        COALESCE(reservation.CUSTOMER_NAME, '') as "customer_name",
        COALESCE(reservation.STATUS, '') as "appointmentStatus",
        COALESCE(reservation.STATUS, '') as "appointment_status",
        COALESCE(reservation.RESERVATION_DATE, '') as "reservationDate",
        COALESCE(reservation.RESERVATION_DATE, '') as "reservation_date",
        COALESCE(reservation.RESERVATION_TIME, '') as "reservationTime",
        COALESCE(reservation.RESERVATION_TIME, '') as "reservation_time",

        0                                                                      as "lateEarlyFlag",
        0                                                                      as "late_early_flag",
        case
        when reservation.RESERVATION_NUMBER is not null then
        case
        when reservation.SEG_NO = 'JC000000' then
        case
        when reservation.TYPE_OF_HANDLING = '10' then '90'
        else reservation.TYPE_OF_HANDLING end
        else
        reservation.TYPE_OF_HANDLING end
        else (select ALLOC_TYPE
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = a.SEG_NO
        and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0502.ALLOCATE_VEHICLE_NO = lil0502.ALLOCATE_VEHICLE_NO
        limit 1) end                                                 as "businessType",
        a.VOUCHER_NUM                                                          as "voucherNum",
        a.VOUCHER_NUM                                                          as "voucher_num",
        COALESCE(queue_data.QUEUE_DATE, '') as "callDate",
        COALESCE(queue_data.QUEUE_DATE, '') as "call_date",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromRegistrationToEntry",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_registration_to_entry",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToTheStartOfTheJob",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_the_start_of_the_job",

        CASE
        WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",

        CASE
        WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",

        CASE
        WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromTheCompletionOfTheJobToTheFactory",

        CASE
        WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_the_completion_of_the_job_to_the_factory",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_the_completion_of_the_job",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToLeavingTheFactory",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_leaving_the_factory",

        CASE
        WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "registeredToTheFactoryTime",

        CASE
        WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "registered_to_the_factory_time",

        COALESCE(sign_data.SIGN_OFF_TIME, '') as "sign_off_time",
        COALESCE(sign_data.SIGN_OFF_TIME, '') as "signOffTime",

        CASE
        WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
        AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
        CASE
        WHEN TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) > 0
        THEN CONCAT(
        FLOOR(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) / 60),
        '时',
        MOD(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')), 60),
        '分')
        ELSE '0时0分'
        END
        ELSE ''
        END as "theTimeFromTheFactoryToTheTimeOfReceipt",

        CASE
        WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
        AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_the_factory_to_the_time_of_receipt",

        COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signing_location_longitude",
        COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signingLocationLongitude",
        COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signing_location_latitude",
        COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signingLocationLatitude",
        COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitude_latitude_check",
        COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitudeLatitudeCheck",
        COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "final_station_longitude",
        COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "finalStationLongitude",
        COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "final_station_latitude",
        COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "finalStationLatitude",
        COALESCE(sign_data.outWeight, 0) as "outWeight",
        COALESCE(sign_data.outQuantity, 0) as "outQuantity",
        COALESCE(sign_data.inWeight, 0) as "inWeight",
        COALESCE(sign_data.inQuantity, 0) as "inQuantity",
        COALESCE(sign_data.SIGNING_ADDRESS, '') as "signingAddress",
        COALESCE(sign_data.SIGNING_ADDRESS, '') as "signing_address",
        COALESCE(sign_data.DEST_SPOT_ADDR, '') as "destSpotAddr",
        COALESCE(sign_data.DEST_SPOT_ADDR, '') as "dest_spot_addr",
        a.EXPECTED_LOADING_TIME as "expectedLoadingTime",
        (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOCATE_VEHICLE_NO SEPARATOR ',')
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = a.SEG_NO
        and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0502.STATUS != '00')                                       as "allocateVehicleNo",
        reservation.RESERVATION_NUMBER,
        reservation.STATUS                                                     as "appointmentStatus",
        (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOC_TYPE SEPARATOR ',')
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = a.SEG_NO
        and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0502.STATUS != '00')                                       as "allocType",
        case when reservation.RESERVATION_NUMBER !='' then 10 else '00' end                                                                as "isReservation",
        case
        when reservation.RESERVATION_NUMBER is not null
        then reservation.VISIT_UNIT
        else
        '重庆宝钢'
        end                                                                as "visitUnit",
        ifnull(B.totalOperationTimeMinutes, ' ')                                       as "totalOperationTimeMinutes",
        ifnull(a.IF_PAUSE,'0')                                                            as "ifPause",
        a.PAUSE_TIME                                                           as "pauseTime"
        FROM (
        SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
        ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
        ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
        CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
        UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
        REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
        REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME,
        (SELECT GROUP_CONCAT(DISTINCT t3.VOUCHER_NUM SEPARATOR ',')
        FROM meli.tlirl0503 t3
        WHERE 1 = 1
        AND t3.ALLOCATE_VEHICLE_NO = a.ALLOCATE_VEHICLE_NO
        AND t3.SEG_NO = a.SEG_NO
        AND t3.STATUS != '00') as "VOUCHER_NUM",
        a.IF_PAUSE                as "IF_PAUSE",
        a.PAUSE_TIME              as "PAUSE_TIME"
        FROM MELI.tlirl0301 a
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOCATE_VEHICLE_NO SEPARATOR ',')
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = a.SEG_NO
            and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
            and tlirl0502.STATUS != '00')   like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="visitUnit">
            case
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = '' then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '10' then "重庆宝钢"
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '20' then "杭州宝伟"
            else (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) end = #visitUnit#
        </isNotEmpty>
        UNION ALL

        SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
        ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
        ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
        CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
        UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
        REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
        REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME,
        (SELECT GROUP_CONCAT(DISTINCT t3.VOUCHER_NUM SEPARATOR ',')
        FROM meli.tlirl0503 t3
        WHERE 1 = 1
        AND t3.ALLOCATE_VEHICLE_NO = a.ALLOCATE_VEHICLE_NO
        AND t3.SEG_NO = a.SEG_NO
        AND t3.STATUS != '00') as "VOUCHER_NUM",
        '0'                       as "IF_PAUSE",
        ' '                       as "PAUSE_TIME"
        FROM MELI.tlirl0311 a
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOCATE_VEHICLE_NO SEPARATOR ',')
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = a.SEG_NO
            and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
            and tlirl0502.STATUS != '00')   like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (SELECT 1
            FROM MELI.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.TYPE_OF_HANDLING = #businessType#)
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="visitUnit">
            case
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = '' then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '10' then "重庆宝钢"
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '20' then "杭州宝伟"
            else (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) end = #visitUnit#
        </isNotEmpty>

        ) a

        LEFT JOIN MELI.tlirl0201 reservation
        ON reservation.SEG_NO = a.SEG_NO
        AND reservation.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        AND reservation.STATUS != '00'
        AND reservation.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MIN(LOAD_DATE) as min_load_date
        FROM MELI.tlirl0406
        WHERE SEG_NO = #segNo# AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) load_data ON load_data.SEG_NO = a.SEG_NO
        AND load_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND load_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MAX(FINISH_LOAD_DATE) as max_finish_date
        FROM MELI.tlirl0407
        WHERE SEG_NO = #segNo# AND NEXT_TATGET = '20' AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) finish_data ON finish_data.SEG_NO = a.SEG_NO
        AND finish_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND finish_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT DISTINCT
        t1.SEG_NO,
        t1.CAR_TRACE_NO,
        t1.VEHICLE_NO,
        t1.NEXT_TATGET as next_target
        FROM MELI.tlirl0407 t1
        WHERE t1.SEG_NO = #segNo# AND t1.STATUS != '00'
        AND t1.REC_CREATE_TIME = (
        SELECT MAX(t2.REC_CREATE_TIME)
        FROM MELI.tlirl0407 t2
        WHERE t2.SEG_NO = t1.SEG_NO
        AND t2.CAR_TRACE_NO = t1.CAR_TRACE_NO
        AND t2.VEHICLE_NO = t1.VEHICLE_NO
        AND t2.STATUS != '00'
        )
        ) next_target_data ON next_target_data.SEG_NO = a.SEG_NO
        AND next_target_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND next_target_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0304 hp_target
        ON hp_target.SEG_NO = a.SEG_NO
        AND hp_target.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        AND hp_target.STATUS = '30'
        AND hp_target.DEL_FLAG = '0'

        LEFT JOIN MELI.tlirl0304 hp_current
        ON hp_current.SEG_NO = a.SEG_NO
        AND hp_current.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        AND hp_current.STATUS = '30'
        AND hp_current.DEL_FLAG = '0'

        LEFT JOIN (
        SELECT
        SEG_NO, VEHICLE_NO, CAR_TRACE_NO,
        MAX(SIGNING_LOCATION_LONGITUDE) as SIGNING_LOCATION_LONGITUDE,
        MAX(SIGNING_LOCATION_LATITUDE) as SIGNING_LOCATION_LATITUDE,
        MAX(LONGITUDE_LATITUDE_CHECK) as LONGITUDE_LATITUDE_CHECK,
        MAX(FINAL_STATION_LONGITUDE) as FINAL_STATION_LONGITUDE,
        MAX(FINAL_STATION_LATITUDE) as FINAL_STATION_LATITUDE,
        MAX(SIGN_OFF_TIME) as SIGN_OFF_TIME,
        MAX(SIGNING_ADDRESS) as SIGNING_ADDRESS,
        MAX(DEST_SPOT_ADDR) as DEST_SPOT_ADDR,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN 1 ELSE 0 END) as outQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN 1 ELSE 0 END) as inQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as outWeight,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as inWeight
        FROM MELI.tlirl0308
        WHERE SEG_NO = #segNo# AND DEL_FLAG = '0'
        GROUP BY SEG_NO, VEHICLE_NO, CAR_TRACE_NO
        ) sign_data ON sign_data.SEG_NO = a.SEG_NO
        AND sign_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND sign_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0502 lil0502
        ON lil0502.SEG_NO = a.SEG_NO AND lil0502.CAR_TRACE_NO = a.CAR_TRACE_NO AND
        lil0502.STATUS != '00'

        LEFT JOIN (
        SELECT CAR_TRACE_NO, MAX(QUEUE_DATE) as QUEUE_DATE
        FROM (
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0402 WHERE DEL_FLAG = '0'
        UNION ALL
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0409 WHERE DEL_FLAG = '0'
        ) queue_union
        GROUP BY CAR_TRACE_NO
        ) queue_data ON queue_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        LEFT JOIN (select t.CAR_TRACE_NO,
        t.SEG_NO,
        sum(t.totalOperationTimeMinutes) as "totalOperationTimeMinutes"
        from (SELECT tlirl0407.CAR_TRACE_NO,
        tlirl0407.SEG_NO,
        (TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(min(tlirl0406.LOAD_DATE), '%Y%m%d%H%i%s'),
        STR_TO_DATE(max(tlirl0407.FINISH_LOAD_DATE), '%Y%m%d%H%i%s')
        )) AS totalOperationTimeMinutes
        FROM meli.tlirl0407 tlirl0407
        JOIN meli.tlirl0406 tlirl0406
        ON tlirl0407.SEG_NO = tlirl0406.SEG_NO
        AND tlirl0407.LOAD_ID = tlirl0406.LOAD_ID
        AND tlirl0407.CAR_TRACE_NO = tlirl0406.CAR_TRACE_NO
        GROUP BY tlirl0407.SEG_NO, tlirl0407.CURRENT_HAND_POINT_ID, tlirl0407.CAR_TRACE_NO) t
        GROUP BY t.CAR_TRACE_NO, t.SEG_NO) B
        ON B.SEG_NO = a.SEG_NO AND B.CAR_TRACE_NO = a.CAR_TRACE_NO
        ORDER BY a.CHECK_DATE DESC, a.CAR_TRACE_NO, a.REC_CREATE_TIME DESC
    </select>

    <select id="queryCqCount"
            resultClass="int">
        select count(*) from
        ( SELECT DISTINCT
        a.SEG_NO as "segNo",
        a.SEG_NO,
        a.UNIT_CODE as "unitCode",
        a.UNIT_CODE,
        a.CAR_TRACE_NO as "carTraceNo",
        a.CAR_TRACE_NO,
        a.STATUS as "status",
        a.HAND_TYPE as "handType",
        a.HAND_TYPE,
        a.VEHICLE_NO as "vehicleNo",
        a.VEHICLE_NO,
        a.ID_CARD as "idCard",
        a.ID_CARD,
        a.DRIVER_NAME as "driverName",
        a.DRIVER_NAME,
        a.TEL_NUM as "telNum",
        a.TEL_NUM,
        a.RESERVATION_NUMBER as "reservationNumber",
        a.RESERVATION_NUMBER,
        a.CHECK_DATE as "checkDate",
        a.CHECK_DATE,
        a.ENTER_FACTORY as "enterFactory",
        a.ENTER_FACTORY,

        COALESCE(load_data.min_load_date, '') as "beginEntruckingTime",

        CASE
        WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
        THEN a.BEGIN_ENTRUCKING_TIME
        ELSE ' '
        END as "currentBeginEntruckingTime",

        CASE
        WHEN a.STATUS IN ('20', '30') OR (a.STATUS = '40' AND TRIM(a.TARGET_HAND_POINT_ID) = '')
        THEN a.COMPLETE_UNINSTALL_TIME
        ELSE ' '
        END as "currentcompleteUninstallTime",

        COALESCE(finish_data.max_finish_date, '') as "completeUninstallTime",
        a.COMPLETE_UNINSTALL_TIME,
        a.LEAVE_FACTORY_DATE as "leaveFactoryDate",
        a.LEAVE_FACTORY_DATE,
        a.CUSTOMER_SIGNING_TIME as "customerSigningTime",
        a.CUSTOMER_SIGNING_TIME,
        a.TARGET_HAND_POINT_ID as "targetHandPointId",
        a.TARGET_HAND_POINT_ID,

        COALESCE(hp_target.HAND_POINT_NAME, '') as "targetHandPointName",
        COALESCE(hp_target.HAND_POINT_NAME, '') as "target_hand_point_name",
        a.CURRENT_HAND_POINT_ID as "currentHandPointId",
        a.CURRENT_HAND_POINT_ID,
        COALESCE(hp_current.HAND_POINT_NAME, '') as "currentHandPointName",
        COALESCE(hp_current.HAND_POINT_NAME, '') as "current_hand_point_name",

        a.FACTORY_AREA as "factoryArea",
        a.FACTORY_AREA,
        a.FACTORY_AREA_NAME as "factoryAreaName",
        a.FACTORY_AREA_NAME,

        COALESCE(next_target_data.next_target, '') as "nextTarget",
        COALESCE(next_target_data.next_target, '') as "next_target",

        a.UNLOAD_LEAVE_FLAG as "unloadLeaveFlag",
        a.UNLOAD_LEAVE_FLAG,
        a.REC_CREATOR as "recCreator",
        a.REC_CREATOR,
        a.REC_CREATOR_NAME as "recCreatorName",
        a.REC_CREATOR_NAME,
        a.REC_CREATE_TIME as "recCreateTime",
        a.REC_CREATE_TIME,
        a.REC_REVISOR as "recRevisor",
        a.REC_REVISOR,
        a.REC_REVISOR_NAME as "recRevisorName",
        a.REC_REVISOR_NAME,
        a.REC_REVISE_TIME as "recReviseTime",
        a.REC_REVISE_TIME,
        a.DEL_FLAG as "delFlag",
        a.DEL_FLAG,
        a.REMARK as "remark",
        a.SYS_REMARK as "sysRemark",
        a.SYS_REMARK,
        a.UUID as "uuid",

        CASE WHEN reservation.RESERVATION_NUMBER IS NOT NULL THEN 1 ELSE 0 END as "whet_appointment",
        COALESCE(reservation.START_OF_TRANSPORT, '') as "startOfTransport",
        COALESCE(reservation.START_OF_TRANSPORT, '') as "start_of_transport",
        COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purposeOfTransport",
        COALESCE(reservation.PURPOSE_OF_TRANSPORT, '') as "purpose_of_transport",
        COALESCE(reservation.CUSTOMER_NAME, '') as "customerName",
        COALESCE(reservation.CUSTOMER_NAME, '') as "customer_name",
        COALESCE(reservation.STATUS, '') as "appointmentStatus",
        COALESCE(reservation.STATUS, '') as "appointment_status",
        COALESCE(reservation.RESERVATION_DATE, '') as "reservationDate",
        COALESCE(reservation.RESERVATION_DATE, '') as "reservation_date",
        COALESCE(reservation.RESERVATION_TIME, '') as "reservationTime",
        COALESCE(reservation.RESERVATION_TIME, '') as "reservation_time",

        0                                                                      as "lateEarlyFlag",
        0                                                                      as "late_early_flag",
        case
        when reservation.RESERVATION_NUMBER is not null
        then
        reservation.TYPE_OF_HANDLING
        else
        (select ALLOC_TYPE
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = a.SEG_NO
        and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0502.ALLOCATE_VEHICLE_NO = lil0502.ALLOCATE_VEHICLE_NO
        limit 1)
        end
        as "businessType",
        a.VOUCHER_NUM                                                          as "voucherNum",
        a.VOUCHER_NUM                                                          as "voucher_num",
        COALESCE(queue_data.QUEUE_DATE, '') as "callDate",
        COALESCE(queue_data.QUEUE_DATE, '') as "call_date",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromRegistrationToEntry",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.CHECK_DATE != ' '
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.CHECK_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_registration_to_entry",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToTheStartOfTheJob",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND load_data.min_load_date != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(load_data.min_load_date) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_the_start_of_the_job",

        CASE
        WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theDurationFromTheStartOfTheActivityToTheCompletionOfTheActivity",

        CASE
        WHEN load_data.min_load_date != '' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(load_data.min_load_date) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(load_data.min_load_date, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_duration_from_the_start_of_the_activity_to_the_completion_of_the_activity",

        CASE
        WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromTheCompletionOfTheJobToTheFactory",

        CASE
        WHEN a.COMPLETE_UNINSTALL_TIME != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_the_completion_of_the_job_to_the_factory",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToTheCompletionOfTheJob",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.COMPLETE_UNINSTALL_TIME != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.COMPLETE_UNINSTALL_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.COMPLETE_UNINSTALL_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_the_completion_of_the_job",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "theTimeFromEnteringTheFactoryToLeavingTheFactory",

        CASE
        WHEN a.ENTER_FACTORY != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.ENTER_FACTORY) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.ENTER_FACTORY, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_entering_the_factory_to_leaving_the_factory",

        CASE
        WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "registeredToTheFactoryTime",

        CASE
        WHEN a.CHECK_DATE != ' ' AND a.LEAVE_FACTORY_DATE != ''
        AND LENGTH(a.CHECK_DATE) >= 14 AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.CHECK_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "registered_to_the_factory_time",

        COALESCE(sign_data.SIGN_OFF_TIME, '') as "sign_off_time",
        COALESCE(sign_data.SIGN_OFF_TIME, '') as "signOffTime",

        CASE
        WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
        AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
        CASE
        WHEN TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) > 0
        THEN CONCAT(
        FLOOR(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')) / 60),
        '时',
        MOD(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')), 60),
        '分')
        ELSE '0时0分'
        END
        ELSE ''
        END as "theTimeFromTheFactoryToTheTimeOfReceipt",

        CASE
        WHEN a.LEAVE_FACTORY_DATE != '' AND sign_data.SIGN_OFF_TIME != ''
        AND LENGTH(a.LEAVE_FACTORY_DATE) >= 14 AND LENGTH(sign_data.SIGN_OFF_TIME) >= 14 THEN
        GREATEST(0, TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(LEFT(a.LEAVE_FACTORY_DATE, 14), '%Y%m%d%H%i%s'),
        STR_TO_DATE(LEFT(sign_data.SIGN_OFF_TIME, 14), '%Y%m%d%H%i%s')))
        ELSE 0
        END as "the_time_from_the_factory_to_the_time_of_receipt",

        COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signing_location_longitude",
        COALESCE(sign_data.SIGNING_LOCATION_LONGITUDE, '') as "signingLocationLongitude",
        COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signing_location_latitude",
        COALESCE(sign_data.SIGNING_LOCATION_LATITUDE, '') as "signingLocationLatitude",
        COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitude_latitude_check",
        COALESCE(sign_data.LONGITUDE_LATITUDE_CHECK, '') as "longitudeLatitudeCheck",
        COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "final_station_longitude",
        COALESCE(sign_data.FINAL_STATION_LONGITUDE, '') as "finalStationLongitude",
        COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "final_station_latitude",
        COALESCE(sign_data.FINAL_STATION_LATITUDE, '') as "finalStationLatitude",
        COALESCE(sign_data.outWeight, 0) as "outWeight",
        COALESCE(sign_data.outQuantity, 0) as "outQuantity",
        COALESCE(sign_data.inWeight, 0) as "inWeight",
        COALESCE(sign_data.inQuantity, 0) as "inQuantity",
        COALESCE(sign_data.SIGNING_ADDRESS, '') as "signingAddress",
        COALESCE(sign_data.SIGNING_ADDRESS, '') as "signing_address",
        COALESCE(sign_data.DEST_SPOT_ADDR, '') as "destSpotAddr",
        COALESCE(sign_data.DEST_SPOT_ADDR, '') as "dest_spot_addr",
        a.EXPECTED_LOADING_TIME as "expectedLoadingTime",
        (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOCATE_VEHICLE_NO SEPARATOR ',')
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = a.SEG_NO
        and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0502.STATUS != '00')                                       as "allocateVehicleNo",
        (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOC_TYPE SEPARATOR ',')
        from meli.tlirl0502 tlirl0502
        where 1 = 1
        and tlirl0502.SEG_NO = a.SEG_NO
        and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
        and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
        and tlirl0502.STATUS != '00')                                       as "allocType",
        case when reservation.RESERVATION_NUMBER !='' then 10 else '00' end                                                                as "isReservation",
        case
        when reservation.RESERVATION_NUMBER is not null
        then reservation.VISIT_UNIT
        else
        '重庆宝钢'
        end                                                                as "visitUnit"
        FROM (
        SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
        ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
        ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
        CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
        UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
        REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
        REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME,
        (SELECT GROUP_CONCAT(DISTINCT t3.VOUCHER_NUM SEPARATOR ',')
        FROM meli.tlirl0503 t3
        WHERE 1 = 1
        AND t3.ALLOCATE_VEHICLE_NO = a.ALLOCATE_VEHICLE_NO
        AND t3.SEG_NO = a.SEG_NO
        AND t3.STATUS != '00') as "VOUCHER_NUM"
        FROM MELI.tlirl0301 a
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOCATE_VEHICLE_NO SEPARATOR ',')
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = a.SEG_NO
            and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
            and tlirl0502.STATUS != '00')   like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>

        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="visitUnit">
            case
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = '' then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '10' then "重庆宝钢"
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '20' then "杭州宝伟"
            else (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) end = #visitUnit#
        </isNotEmpty>
        UNION ALL

        SELECT SEG_NO, UNIT_CODE, CAR_TRACE_NO, STATUS, HAND_TYPE, VEHICLE_NO,
        ID_CARD, DRIVER_NAME, TEL_NUM, RESERVATION_NUMBER, CHECK_DATE,
        ENTER_FACTORY, BEGIN_ENTRUCKING_TIME, COMPLETE_UNINSTALL_TIME,
        LEAVE_FACTORY_DATE, CUSTOMER_SIGNING_TIME, TARGET_HAND_POINT_ID,
        CURRENT_HAND_POINT_ID, FACTORY_AREA, FACTORY_AREA_NAME,
        UNLOAD_LEAVE_FLAG, REC_CREATOR, REC_CREATOR_NAME, REC_CREATE_TIME,
        REC_REVISOR, REC_REVISOR_NAME, REC_REVISE_TIME, DEL_FLAG,
        REMARK, SYS_REMARK, UUID, EXPECTED_LOADING_TIME,
        (SELECT GROUP_CONCAT(DISTINCT t3.VOUCHER_NUM SEPARATOR ',')
        FROM meli.tlirl0503 t3
        WHERE 1 = 1
        AND t3.ALLOCATE_VEHICLE_NO = a.ALLOCATE_VEHICLE_NO
        AND t3.SEG_NO = a.SEG_NO
        AND t3.STATUS != '00') as "VOUCHER_NUM"
        FROM MELI.tlirl0311 a
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="status">
            a.STATUS in ($status$)
        </isNotEmpty>
        <isEmpty prepend=" AND " property="status">
            a.STATUS != '00'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="inHouse">
            a.STATUS in ('20','30','40')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="outHouse">
            a.STATUS in ('50','60')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noHouse">
            a.STATUS in ('10')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            a.SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="telNum">
            a.TEL_NUM like concat('%',#telNum#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="driverName">
            a.DRIVER_NAME like concat('%',#driverName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            a.VEHICLE_NO like concat('%',#vehicleNo#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="allocateVehicleNo">
            (select GROUP_CONCAT(DISTINCT tlirl0502.ALLOCATE_VEHICLE_NO SEPARATOR ',')
            from meli.tlirl0502 tlirl0502
            where 1 = 1
            and tlirl0502.SEG_NO = a.SEG_NO
            and tlirl0502.CAR_TRACE_NO = a.CAR_TRACE_NO
            and tlirl0502.VEHICLE_NO = a.VEHICLE_NO
            and tlirl0502.STATUS != '00')   like concat('%',#allocateVehicleNo#,'%')
        </isNotEmpty>
        <!--起始预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateStart">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) >= replace(#reservationDateStart#,'-','')
            )
        </isNotEmpty>
        <!--截止预约日期-->
        <isNotEmpty prepend=" and " property="reservationDateEnd">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and substr(b.RESERVATION_DATE,1,8) <![CDATA[<=]]> replace(#reservationDateEnd#,'-','')
            )
        </isNotEmpty>
        <!--起始登记日期-->
        <isNotEmpty prepend=" and " property="checkDateStart">
            substr(a.CHECK_DATE,1,8) >= replace(#checkDateStart#,'-','')
        </isNotEmpty>
        <!--截止登记日期-->
        <isNotEmpty prepend=" and " property="checkDateEnd">
            substr(a.CHECK_DATE,1,8) <![CDATA[<=]]> replace(#checkDateEnd#,'-','')
        </isNotEmpty>
        <!--起始进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryStart">
            substr(a.ENTER_FACTORY,1,8) >= replace(#enterFactoryStart#,'-','')
        </isNotEmpty>
        <!--截止进厂日期-->
        <isNotEmpty prepend=" and " property="enterFactoryEnd">
            substr(a.ENTER_FACTORY,1,8) <![CDATA[<=]]> replace(#enterFactoryEnd#,'-','')
        </isNotEmpty>
        <!--起始出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateStart">
            substr(a.LEAVE_FACTORY_DATE,1,8) >= replace(#leaveFactoryDateStart#,'-','')
        </isNotEmpty>
        <!--截止出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDateEnd">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[<=]]> replace(#leaveFactoryDateEnd#,'-','')
        </isNotEmpty>
        <!--出厂日期-->
        <isNotEmpty prepend=" and " property="leaveFactoryDate">
            substr(a.LEAVE_FACTORY_DATE,1,8) <![CDATA[=]]> replace(#leaveFactoryDate#,'-','')
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="businessType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.BUSINESS_TYPE = #businessType#
            <isNotEmpty prepend=" and " property="voucherNumN">
                b.VOUCHER_NUM = ' '
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="handType">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0302 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.HAND_TYPE = #handType#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="voucherNum">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.VOUCHER_NUM like concat('%',#voucherNum#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="packId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0308 b
            WHERE 1 = 1
            and b.CAR_TRACE_NO = a.CAR_TRACE_NO
            and b.DEL_FLAG = '0'
            and b.PACK_ID like concat('%',#packId#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="startOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.START_OF_TRANSPORT like concat('%',#startOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="purposeOfTransport">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.PURPOSE_OF_TRANSPORT like concat('%',#purposeOfTransport#,'%')
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerId2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_ID = #customerId2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="customerName2">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.CUSTOMER_NAME = #customerName2#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="carTraceNo">
            a.CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusListStr">
            a.STATUS in ( $statusListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusNotListStr">
            a.STATUS NOT in ( $statusNotListStr$ )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            a.DEL_FLAG = #delFlag#
        </isNotEmpty>
        <!--<isNotEmpty prepend=" AND " property="status">
            STATUS <![CDATA[<]]> #status#
        </isNotEmpty>-->
        <isNotEmpty prepend=" AND " property="handPointId">
            a.TARGET_HAND_POINT_ID = #handPointId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="statusList">
            a.STATUS in
            <iterate property="statusList" open="("
                     close=")" conjunction=" , ">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" and " property="isReservation">
            EXISTS (
            SELECT
            1
            FROM
            ${meliSchema}.tlirl0201 b
            WHERE 1 = 1
            and b.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            and b.DEL_FLAG = '0'
            and b.IS_RESERVATION = #isReservation#
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="visitUnit">
            case
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = '' then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '10' then "重庆宝钢"
            when (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) = '20' then "杭州宝伟"
            else (case
            when a.RESERVATION_NUMBER is null or a.RESERVATION_NUMBER = ''
            then (select case
            when tlirl0501.AFFILIATED_UNIT is null or tlirl0501.AFFILIATED_UNIT = ''
            then 10
            else tlirl0501.AFFILIATED_UNIT end
            from MELI.tlirl0501 tlirl0501
            where tlirl0501.SEG_NO = a.SEG_NO
            and tlirl0501.VEHICLE_NO = a.VEHICLE_NO
            limit 1)
            else (select case
            when tlirl0201.VISIT_UNIT is null or tlirl0201.VISIT_UNIT = '' then 10
            else tlirl0201.VISIT_UNIT end
            from MELI.tlirl0201 tlirl0201
            where tlirl0201.SEG_NO = a.SEG_NO
            and tlirl0201.RESERVATION_NUMBER = a.RESERVATION_NUMBER
            limit 1) end) end = #visitUnit#
        </isNotEmpty>

        ) a

        LEFT JOIN MELI.tlirl0201 reservation
        ON reservation.SEG_NO = a.SEG_NO
        AND reservation.RESERVATION_NUMBER = a.RESERVATION_NUMBER
        AND reservation.STATUS != '00'
        AND reservation.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MIN(LOAD_DATE) as min_load_date
        FROM MELI.tlirl0406
        WHERE SEG_NO = #segNo# AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) load_data ON load_data.SEG_NO = a.SEG_NO
        AND load_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND load_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT SEG_NO, CAR_TRACE_NO, VEHICLE_NO, MAX(FINISH_LOAD_DATE) as max_finish_date
        FROM MELI.tlirl0407
        WHERE SEG_NO = #segNo# AND NEXT_TATGET = '20' AND STATUS != '00'
        GROUP BY SEG_NO, CAR_TRACE_NO, VEHICLE_NO
        ) finish_data ON finish_data.SEG_NO = a.SEG_NO
        AND finish_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND finish_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN (
        SELECT DISTINCT
        t1.SEG_NO,
        t1.CAR_TRACE_NO,
        t1.VEHICLE_NO,
        t1.NEXT_TATGET as next_target
        FROM MELI.tlirl0407 t1
        WHERE t1.SEG_NO = #segNo# AND t1.STATUS != '00'
        AND t1.REC_CREATE_TIME = (
        SELECT MAX(t2.REC_CREATE_TIME)
        FROM MELI.tlirl0407 t2
        WHERE t2.SEG_NO = t1.SEG_NO
        AND t2.CAR_TRACE_NO = t1.CAR_TRACE_NO
        AND t2.VEHICLE_NO = t1.VEHICLE_NO
        AND t2.STATUS != '00'
        )
        ) next_target_data ON next_target_data.SEG_NO = a.SEG_NO
        AND next_target_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND next_target_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0304 hp_target
        ON hp_target.SEG_NO = a.SEG_NO
        AND hp_target.HAND_POINT_ID = a.TARGET_HAND_POINT_ID
        AND hp_target.STATUS = '30'
        AND hp_target.DEL_FLAG = '0'

        LEFT JOIN MELI.tlirl0304 hp_current
        ON hp_current.SEG_NO = a.SEG_NO
        AND hp_current.HAND_POINT_ID = a.CURRENT_HAND_POINT_ID
        AND hp_current.STATUS = '30'
        AND hp_current.DEL_FLAG = '0'

        LEFT JOIN (
        SELECT
        SEG_NO, VEHICLE_NO, CAR_TRACE_NO,
        MAX(SIGNING_LOCATION_LONGITUDE) as SIGNING_LOCATION_LONGITUDE,
        MAX(SIGNING_LOCATION_LATITUDE) as SIGNING_LOCATION_LATITUDE,
        MAX(LONGITUDE_LATITUDE_CHECK) as LONGITUDE_LATITUDE_CHECK,
        MAX(FINAL_STATION_LONGITUDE) as FINAL_STATION_LONGITUDE,
        MAX(FINAL_STATION_LATITUDE) as FINAL_STATION_LATITUDE,
        MAX(SIGN_OFF_TIME) as SIGN_OFF_TIME,
        MAX(SIGNING_ADDRESS) as SIGNING_ADDRESS,
        MAX(DEST_SPOT_ADDR) as DEST_SPOT_ADDR,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN 1 ELSE 0 END) as outQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN 1 ELSE 0 END) as inQuantity,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '20' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as outWeight,
        SUM(CASE WHEN PUT_IN_OUT_FLAG = '10' THEN IFNULL(WEIGHT, 0) ELSE 0 END) as inWeight
        FROM MELI.tlirl0308
        WHERE SEG_NO = #segNo# AND DEL_FLAG = '0'
        GROUP BY SEG_NO, VEHICLE_NO, CAR_TRACE_NO
        ) sign_data ON sign_data.SEG_NO = a.SEG_NO
        AND sign_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        AND sign_data.VEHICLE_NO = a.VEHICLE_NO

        LEFT JOIN MELI.tlirl0502 lil0502
        ON lil0502.SEG_NO = a.SEG_NO AND lil0502.CAR_TRACE_NO = a.CAR_TRACE_NO AND
        lil0502.STATUS != '00'

        LEFT JOIN (
        SELECT CAR_TRACE_NO, MAX(QUEUE_DATE) as QUEUE_DATE
        FROM (
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0402 WHERE DEL_FLAG = '0'
        UNION ALL
        SELECT CAR_TRACE_NO, QUEUE_DATE FROM MELI.tlirl0409 WHERE DEL_FLAG = '0'
        ) queue_union
        GROUP BY CAR_TRACE_NO
        ) queue_data ON queue_data.CAR_TRACE_NO = a.CAR_TRACE_NO
        ORDER BY a.CHECK_DATE DESC, a.CAR_TRACE_NO, a.REC_CREATE_TIME DESC
        ) a1
    </select>
    <select id="queryOutNumOne" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        SEG_NO	as "segNo",  <!-- 账套 -->
        UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
        LEAVE_FACTORY_ID	as "leaveFactoryId",  <!-- 出厂流水号 -->
        VEHICLE_NO	as "vehicleNo",  <!-- 车牌号 -->
        STATUS	as "status",  <!-- 状态(00撤销 10新增) -->
        LEAVE_FACTORY_DATE	as "leaveFactoryDate",  <!-- 出厂时间 -->
        CANCEL_LEAVE_FACTORY_DATE	as "cancelLeaveFactoryDate",  <!-- 撤销出厂时间 -->
        CAR_TRACE_NO	as "carTraceNo",  <!-- 车辆跟踪号 -->
        DATE_SOURCE	as "dateSource",  <!-- 数据来源(10：mes 20pda 30：车牌识别) -->
        DEVICE_NUMBER	as "deviceNumber",  <!-- 设备号（车牌识别） -->
        DEVICE_VOUCHER_NUM	as "deviceVoucherNum",  <!-- 依据凭单号(车牌识别系统流水号) -->
        VOUCHER_NUM	as "voucherNum",  <!-- 依据凭单号(PAD作业流水号) -->
        FACTORY_AREA	as "factoryArea",  <!-- 厂区 -->
        UNLOAD_LEAVE_FLAG	as "unloadLeaveFlag",  <!-- 未装离厂标记 -->
        REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
        REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
        REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
        REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
        ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
        REMARK	as "remark",  <!-- 备注 -->
        SYS_REMARK	as "sysRemark",  <!-- 系统备注 -->
        UUID	as "uuid",  <!-- uuid -->
        TENANT_ID	as "tenantId", <!-- 租户ID -->
        BACK_STATUS	as "backStatus"  <!-- 返单状态 -->
        FROM ${meliSchema}.tlirl0408 WHERE 1=1 and status != '00'
        <isNotEmpty prepend=" AND " property="carTraceNo">
            CAR_TRACE_NO = #carTraceNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="vehicleNo">
            VEHICLE_NO = #vehicleNo#
        </isNotEmpty>
    </select>
    
</sqlMap>