<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!--      table information
    Generate time : 2024-12-31 15:35:00
       Version :  1.0
    tableName :mevi.tvipm0005
     STANDARD_HOURS_SEQ_ID  VARCHAR   NOT NULL,
     MACHINE_CODE  VARCHAR   NOT NULL,
     MACHINE_NAME  VARCHAR   NOT NULL,
     STANDARD_WORKING_HOURS_STATUS  VARCHAR   NOT NULL,
     E_ARCHIVES_NO  VARCHAR   NOT NULL,
     EQUIPMENT_NAME  VARCHAR   NOT NULL,
     PROCESS_CATEGORY  VARCHAR   NOT NULL,
     PROCESS_CATEGORY_NAME  VARCHAR   NOT NULL,
     STRIP_COUNT  INTEGER   NOT NULL,
     PART_ID  VARCHAR   NOT NULL,
     PROD_TYPE_ID  VARCHAR   NOT NULL,
     PROD_TYPE_NAME  VARCHAR   NOT NULL,
     SURFACE_GRADE  VARCHAR   NOT NULL,
     TENSILE  DECIMAL   NOT NULL,
     SPECS_DESC  VARCHAR   NOT NULL,
     PROCESS_MARK  VARCHAR   NOT NULL,
     SPH  DECIMAL   NOT NULL,
     PROCESS_SPEED  DECIMAL   NOT NULL,
     UUID  VARCHAR   NOT NULL   primarykey,
     REC_CREATOR  VARCHAR   NOT NULL,
     REC_CREATOR_NAME  VARCHAR   NOT NULL,
     REC_CREATE_TIME  VARCHAR   NOT NULL,
     REC_REVISOR  VARCHAR   NOT NULL,
     REC_REVISOR_NAME  VARCHAR   NOT NULL,
     REC_REVISE_TIME  VARCHAR   NOT NULL,
     TENANT_ID  VARCHAR   NOT NULL,
     ARCHIVE_FLAG  VARCHAR   NOT NULL,
     DEL_FLAG  VARCHAR   NOT NULL,
     SEG_NO  VARCHAR   NOT NULL,
     UNIT_CODE  VARCHAR   NOT NULL
-->
<sqlMap namespace="tvipm0005">

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.common.vi.domain.Tvipm0005">
        SELECT
        STANDARD_HOURS_SEQ_ID as "standardHoursSeqId",  <!-- 标准工时序列号 -->
        MACHINE_CODE as "machineCode",  <!-- 机组代码 -->
        MACHINE_NAME as "machineName",  <!-- 机组名称 -->
        STANDARD_WORKING_HOURS_STATUS as "standardWorkingHoursStatus",  <!-- 标准工时状态 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        PROCESS_CATEGORY as "processCategory",  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME as "processCategoryName",  <!-- 工序大类名称 -->
        STRIP_COUNT as "stripCount",  <!-- 分条数 -->
        PART_ID as "partId",  <!-- 物料号 -->
        PROD_TYPE_ID as "prodTypeId",  <!-- 材质（品种） -->
        PROD_TYPE_NAME as "prodTypeName",  <!-- 材质名称（品种名称） -->
        SURFACE_GRADE as "surfaceGrade",  <!-- 内外板（表面等级） -->
        TENSILE as "tensile",  <!-- 抗拉强度 -->
        SPECS_DESC as "specsDesc",  <!-- 规格表述 -->
        PROCESS_MARK as "processMark",  <!-- 加工剪切标志（精整分流标志） -->
        SPH as "sph",  <!-- sph值(片/h)每小时加工片数(落料和精剪) -->
        PROCESS_SPEED as "processSpeed",  <!-- 加工速度(米/分钟) -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode" <!-- 业务单元代码 -->
        FROM mevi.tvipm0005 WHERE 1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                UUID asc
            </isEmpty>
        </dynamic>

    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM mevi.tvipm0005 WHERE 1=1
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
    </select>

    <!--
        <isNotEmpty prepend=" AND " property="standardHoursSeqId">
            STANDARD_HOURS_SEQ_ID = #standardHoursSeqId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineCode">
            MACHINE_CODE = #machineCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="machineName">
            MACHINE_NAME = #machineName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="standardWorkingHoursStatus">
            STANDARD_WORKING_HOURS_STATUS = #standardWorkingHoursStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equipmentName">
            EQUIPMENT_NAME = #equipmentName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategory">
            PROCESS_CATEGORY = #processCategory#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCategoryName">
            PROCESS_CATEGORY_NAME = #processCategoryName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="stripCount">
            STRIP_COUNT = #stripCount#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="partId">
            PART_ID = #partId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="prodTypeId">
            PROD_TYPE_ID = #prodTypeId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="prodTypeName">
            PROD_TYPE_NAME = #prodTypeName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="surfaceGrade">
            SURFACE_GRADE = #surfaceGrade#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tensile">
            TENSILE = #tensile#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="specsDesc">
            SPECS_DESC = #specsDesc#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processMark">
            PROCESS_MARK = #processMark#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sph">
            SPH = #sph#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="processSpeed">
            PROCESS_SPEED = #processSpeed#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreator">
            REC_CREATOR = #recCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreatorName">
            REC_CREATOR_NAME = #recCreatorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recCreateTime">
            REC_CREATE_TIME = #recCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisor">
            REC_REVISOR = #recRevisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recRevisorName">
            REC_REVISOR_NAME = #recRevisorName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="recReviseTime">
            REC_REVISE_TIME = #recReviseTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="tenantId">
            TENANT_ID = #tenantId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archiveFlag">
            ARCHIVE_FLAG = #archiveFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
    -->

    <insert id="insert">
        INSERT INTO mevi.tvipm0005 (STANDARD_HOURS_SEQ_ID,  <!-- 标准工时序列号 -->
        MACHINE_CODE,  <!-- 机组代码 -->
        MACHINE_NAME,  <!-- 机组名称 -->
        STANDARD_WORKING_HOURS_STATUS,  <!-- 标准工时状态 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        PROCESS_CATEGORY,  <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME,  <!-- 工序大类名称 -->
        STRIP_COUNT,  <!-- 分条数 -->
        PART_ID,  <!-- 物料号 -->
        PROD_TYPE_ID,  <!-- 材质（品种） -->
        PROD_TYPE_NAME,  <!-- 材质名称（品种名称） -->
        SURFACE_GRADE,  <!-- 内外板（表面等级） -->
        TENSILE,  <!-- 抗拉强度 -->
        SPECS_DESC,  <!-- 规格表述 -->
        PROCESS_MARK,  <!-- 加工剪切标志（精整分流标志） -->
        SPH,  <!-- sph值(片/h)每小时加工片数(落料和精剪) -->
        PROCESS_SPEED,  <!-- 加工速度(米/分钟) -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE  <!-- 业务单元代码 -->
        )
        VALUES (#standardHoursSeqId#, #machineCode#, #machineName#, #standardWorkingHoursStatus#, #eArchivesNo#,
        #equipmentName#, #processCategory#, #processCategoryName#, #stripCount#, #partId#, #prodTypeId#, #prodTypeName#,
        #surfaceGrade#, #tensile#, #specsDesc#, #processMark#, #sph#, #processSpeed#, #uuid#, #recCreator#,
        #recCreatorName#, #recCreateTime#, #recRevisor#, #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#,
        #delFlag#, #segNo#, #unitCode#)
    </insert>

    <delete id="delete">
        DELETE FROM mevi.tvipm0005 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE mevi.tvipm0005
        SET
        STANDARD_HOURS_SEQ_ID = #standardHoursSeqId#,   <!-- 标准工时序列号 -->
        MACHINE_CODE = #machineCode#,   <!-- 机组代码 -->
        MACHINE_NAME = #machineName#,   <!-- 机组名称 -->
        STANDARD_WORKING_HOURS_STATUS = #standardWorkingHoursStatus#,   <!-- 标准工时状态 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        PROCESS_CATEGORY = #processCategory#,   <!-- 工序大类代码 -->
        PROCESS_CATEGORY_NAME = #processCategoryName#,   <!-- 工序大类名称 -->
        STRIP_COUNT = #stripCount#,   <!-- 分条数 -->
        PART_ID = #partId#,   <!-- 物料号 -->
        PROD_TYPE_ID = #prodTypeId#,   <!-- 材质（品种） -->
        PROD_TYPE_NAME = #prodTypeName#,   <!-- 材质名称（品种名称） -->
        SURFACE_GRADE = #surfaceGrade#,   <!-- 内外板（表面等级） -->
        TENSILE = #tensile#,   <!-- 抗拉强度 -->
        SPECS_DESC = #specsDesc#,   <!-- 规格表述 -->
        PROCESS_MARK = #processMark#,   <!-- 加工剪切标志（精整分流标志） -->
        SPH = #sph#,   <!-- sph值(片/h)每小时加工片数(落料和精剪) -->
        PROCESS_SPEED = #processSpeed#,   <!-- 加工速度(米/分钟) -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#  <!-- 业务单元代码 -->
        WHERE
        UUID = #uuid#
    </update>

</sqlMap>