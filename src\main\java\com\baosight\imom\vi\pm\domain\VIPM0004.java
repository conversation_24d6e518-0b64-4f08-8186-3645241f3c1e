package com.baosight.imom.vi.pm.domain;

import com.baosight.imom.common.vi.domain.Tvipm0004;

/**
 * 机组离线时间表实体类
 */
public class VIPM0004 extends Tvipm0004 {
    /**
     * 查询
     */
    public static final String QUERY = "VIPM0004.query";

    public static final String QUERY_FOR_B1 = "VIPM0004.queryForB1";
    /**
     * 查询条数
     */
    public static final String COUNT = "VIPM0004.count";
    /**
     * 新增
     */
    public static final String INSERT = "VIPM0004.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VIPM0004.update";
    /**
     * 修改
     */
    public static final String UPD_FOR_DEL = "VIPM0004.updForDel";
    /**
     * 查询
     */
    public static final String QUERY_FOR_AVG = "VIPM0004.queryForAvg";
}
