/**
 * Generate time : 2024-08-27 9:31:25
 * Version : 1.0
 */
package com.baosight.imom.li.rl.dao;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Tlirl0302 车辆进厂登记表
 *
 */
public class LIRL0302 extends DaoEPBase {
    public static final String QUERY = "LIRL0302.query";
    public static final String QUERY_CHECK_DATE = "LIRL0302.queryCheckDate";
    public static final String CHECK_PENDING_NON_APPOINTMENT = "LIRL0302.checkPendingNonAppointment";
    public static final String CHECK_PENDING_NON_APPOINTMENT_COUNT = "LIRL0302.checkPendingNonAppointmentCount";
    public static final String QUERY_ALL = "LIRL0302.queryAll";
    public static final String COUNT = "LIRL0302.count";
    public static final String INSERT = "LIRL0302.insert";
    public static final String UPDATE = "LIRL0302.update";
    public static final String UPDATE_BY_CUSTOMER_INFO = "LIRL0302.updateByCustomerInfo";
    public static final String DELETE = "LIRL0302.delete";
    public static final String DELETE_STATUS= "LIRL0302.deleteStatus";
    public static final String QUERY_MAX_HAND_TYPE = "LIRL0302.queryMaxHandType";
    public static final String QUERY_HAND_POINT_IS_EXIST = "LIRL0302.queryHandPointIsExist";
    public static final String QUERY_HAND_POINT_IS_USED = "LIRL0302.queryHandPointIsUsed";
    public static final String QUERY_MIN_HAND_POINT = "LIRL0302.queryMinHandPoint";
    public static final String QUERY_PROCESSING_APPOINTMENT_BLACKLIST = "LIRL0302.queryProcessingAppointmentBlacklist";
    public static final String QUERY_MIN_HAND_POINT_BY_JOB_NUMBER = "LIRL0302.queryMinHandPointByJobNumber";
    public static final String QUERY_HAND_POINT_BY_JOB_NUMBER_DESC = "LIRL0302.queryHandPointByJobNumberDesc";
    public static final String UPDATE_CALL_DATE = "LIRL0302.updateCallDate";
    public static final String QUERY_CUSTOMER_INFO = "LIRL0301.queryCustomerInfo";
    public static final String QUERY_LIRL0302 = "LIRL0302.queryLirl0302";


    private String segNo = " ";        /* 系统账套*/
    private String segName = "";        /* 业务单元简称*/
    private String unitCode = " ";        /* 业务单元代码*/
    private String checkId = " ";        /* 车辆登记流水号*/
    private String carTraceNo = " ";        /* 车辆跟踪号*/
    private String carTraceStatus = " ";
    private String status = " ";        /* 状态(00撤销  10待审核 20审核 99驳回)*/
    private String handType = " ";        /* 装卸类型(10 装 20卸 30装卸)*/
    private String vehicleNo = " ";        /* 车牌号*/
    private String idCard = " ";        /* 身份证号*/
    private String driverName = " ";        /* 驾驶员姓名*/
    private String telNum = " ";        /* 手机号*/
    private String reservationNumber = " ";        /* 车辆预约单号*/
    private String checkSource = " ";        /* 数据来源(10一体机)*/
    private String voucherNum = " ";        /* 提单号*/
    private String businessType = " ";        /* 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货)*/
    private String reservationDate = " ";        /* 车辆预约时间*/
    private String reservationTime = " ";        /* 车辆预约时段*/
    private String checkDate = " ";        /* 进厂登记时间*/
    private String lateEarlyFlag = " ";        /* 迟到早到标记*/
    private String callDate = " ";        /* 叫号日期*/
    private String callTime = " ";        /* 叫号时段*/
    private String factoryArea = " ";        /* 厂区*/
    private String factoryAreaName = " ";        /* 厂区名称*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private Integer archiveFlag = Integer.valueOf(0);        /* 归档标记*/
    private Integer delFlag = Integer.valueOf(0);        /* 记录删除标记*/
    private String remark = " ";        /* 备注*/
    private String sysRemark = " ";        /* 系统备注*/
    private String uuid = " ";        /* uuid*/
    private String tenantId = " ";        /* 租户ID*/
    private String customerName = " ";        /* 客户名称*/
    private String customerId = " ";        /* 客户代码*/
    private String nextTarget = " ";        /* 下一目标*/

    /**
     * the constructor
     */
    public LIRL0302() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkId");
        eiColumn.setDescName("车辆登记流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceNo");
        eiColumn.setDescName("车辆跟踪号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("carTraceStatus");
        eiColumn.setDescName("车辆跟踪号状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(00撤销  10待审核 20审核 99驳回)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("handType");
        eiColumn.setDescName("装卸类型(10 装 20卸 30装卸)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("idCard");
        eiColumn.setDescName("身份证号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("driverName");
        eiColumn.setDescName("驾驶员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("telNum");
        eiColumn.setDescName("手机号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationNumber");
        eiColumn.setDescName("车辆预约单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkSource");
        eiColumn.setDescName("数据来源(10一体机)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("voucherNum");
        eiColumn.setDescName("提单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("businessType");
        eiColumn.setDescName("业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationDate");
        eiColumn.setDescName("车辆预约时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("reservationTime");
        eiColumn.setDescName("车辆预约时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("checkDate");
        eiColumn.setDescName("进厂登记时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("lateEarlyFlag");
        eiColumn.setDescName("迟到早到标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callDate");
        eiColumn.setDescName("叫号日期");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("callTime");
        eiColumn.setDescName("叫号时段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sysRemark");
        eiColumn.setDescName("系统备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("客户名称");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("客户代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("nextTarget");
        eiColumn.setDescName("下一目标");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * get the segNo - 系统账套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the segName - 业务单元简称
     *
     * @return the segName
     */
    public String getSegName() {
        return this.segName;
    }

    /**
     * set the segName - 业务单元简称
     */
    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the unitCode - 业务单元代码
     *
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the checkId - 车辆登记流水号
     * @return the checkId
     */
    public String getCheckId() {
        return this.checkId;
    }

    /**
     * set the checkId - 车辆登记流水号
     */
    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }

    /**
     * get the carTraceNo - 车辆跟踪号
     * @return the carTraceNo
     */
    public String getCarTraceNo() {
        return this.carTraceNo;
    }

    /**
     * set the carTraceNo - 车辆跟踪号
     */
    public void setCarTraceNo(String carTraceNo) {
        this.carTraceNo = carTraceNo;
    }
    /**
     * get the carTraceStatus - 车辆跟踪号状态
     * @return the carTraceStatus
     */
    public String getCarTraceStatus() {
        return this.carTraceStatus;
    }

    /**
     * set the carTraceStatus - 车辆跟踪号状态
     */
    public void setCarTraceStatus(String carTraceStatus) {
        this.carTraceStatus = carTraceStatus;
    }

    /**
     * get the status - 状态(00撤销  10待审核 20审核 99驳回)
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态(00撤销  10待审核 20审核 99驳回)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the handType - 装卸类型(10 装 20卸 30装卸)
     * @return the handType
     */
    public String getHandType() {
        return this.handType;
    }

    /**
     * set the handType - 装卸类型(10 装 20卸 30装卸)
     */
    public void setHandType(String handType) {
        this.handType = handType;
    }

    /**
     * get the vehicleNo - 车牌号
     * @return the vehicleNo
     */
    public String getVehicleNo() {
        return this.vehicleNo;
    }

    /**
     * set the vehicleNo - 车牌号
     */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    /**
     * get the idCard - 身份证号
     * @return the idCard
     */
    public String getIdCard() {
        return this.idCard;
    }

    /**
     * set the idCard - 身份证号
     */
    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    /**
     * get the driverName - 驾驶员姓名
     * @return the driverName
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * set the driverName - 驾驶员姓名
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * get the telNum - 手机号
     * @return the telNum
     */
    public String getTelNum() {
        return this.telNum;
    }

    /**
     * set the telNum - 手机号
     */
    public void setTelNum(String telNum) {
        this.telNum = telNum;
    }

    /**
     * get the reservationNumber - 车辆预约单号
     * @return the reservationNumber
     */
    public String getReservationNumber() {
        return this.reservationNumber;
    }

    /**
     * set the reservationNumber - 车辆预约单号
     */
    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }

    /**
     * get the checkSource - 数据来源(10一体机)
     * @return the checkSource
     */
    public String getCheckSource() {
        return this.checkSource;
    }

    /**
     * set the checkSource - 数据来源(10一体机)
     */
    public void setCheckSource(String checkSource) {
        this.checkSource = checkSource;
    }

    /**
     * get the voucherNum - 提单号
     * @return the voucherNum
     */
    public String getVoucherNum() {
        return this.voucherNum;
    }

    /**
     * set the voucherNum - 提单号
     */
    public void setVoucherNum(String voucherNum) {
        this.voucherNum = voucherNum;
    }

    /**
     * get the businessType - 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货)
     * @return the businessType
     */
    public String getBusinessType() {
        return this.businessType;
    }

    /**
     * set the businessType - 业务类型(原料卸货、废料提货、周转架运输、资材卸货、欧冶提货)
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * get the reservationDate - 车辆预约时间
     * @return the reservationDate
     */
    public String getReservationDate() {
        return this.reservationDate;
    }

    /**
     * set the reservationDate - 车辆预约时间
     */
    public void setReservationDate(String reservationDate) {
        this.reservationDate = reservationDate;
    }

    /**
     * get the reservationTime - 车辆预约时段
     * @return the reservationTime
     */
    public String getReservationTime() {
        return this.reservationTime;
    }

    /**
     * set the reservationTime - 车辆预约时段
     */
    public void setReservationTime(String reservationTime) {
        this.reservationTime = reservationTime;
    }

    /**
     * get the checkDate - 进厂登记时间
     * @return the checkDate
     */
    public String getCheckDate() {
        return this.checkDate;
    }

    /**
     * set the checkDate - 进厂登记时间
     */
    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    /**
     * get the lateEarlyFlag - 迟到早到标记
     * @return the lateEarlyFlag
     */
    public String getLateEarlyFlag() {
        return this.lateEarlyFlag;
    }

    /**
     * set the lateEarlyFlag - 迟到早到标记
     */
    public void setLateEarlyFlag(String lateEarlyFlag) {
        this.lateEarlyFlag = lateEarlyFlag;
    }

    /**
     * get the callDate - 叫号日期
     * @return the callDate
     */
    public String getCallDate() {
        return this.callDate;
    }

    /**
     * set the callDate - 叫号日期
     */
    public void setCallDate(String callDate) {
        this.callDate = callDate;
    }

    /**
     * get the callTime - 叫号时段
     * @return the callTime
     */
    public String getCallTime() {
        return this.callTime;
    }

    /**
     * set the callTime - 叫号时段
     */
    public void setCallTime(String callTime) {
        this.callTime = callTime;
    }

    /**
     * get the factoryArea - 厂区
     * @return the factoryArea
     */
    public String getFactoryArea() {
        return this.factoryArea;
    }

    /**
     * set the factoryArea - 厂区
     */
    public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public Integer getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the delFlag - 记录删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 记录删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the remark - 备注
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the sysRemark - 系统备注
     * @return the sysRemark
     */
    public String getSysRemark() {
        return this.sysRemark;
    }

    /**
     * set the sysRemark - 系统备注
     */
    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    /**
     * get the uuid - uuid
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the tenantId - 租户ID
     * @return the tenantId
     */
    public String getTenantId() {
        return this.tenantId;
    }

    /**
     * set the tenantId - 租户ID
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getFactoryAreaName() {
        return factoryAreaName;
    }

    public void setFactoryAreaName(String factoryAreaName) {
        this.factoryAreaName = factoryAreaName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }


    public String getNextTarget() {
        return nextTarget;
    }

    public void setNextTarget(String nextTarget) {
        this.nextTarget = nextTarget;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setCheckId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkId")), checkId));
        setCarTraceNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceNo")), carTraceNo));
        setCarTraceStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("carTraceStatus")), carTraceStatus));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setHandType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("handType")), handType));
        setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
        setIdCard(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("idCard")), idCard));
        setDriverName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("driverName")), driverName));
        setTelNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("telNum")), telNum));
        setReservationNumber(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationNumber")), reservationNumber));
        setCheckSource(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkSource")), checkSource));
        setVoucherNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("voucherNum")), voucherNum));
        setBusinessType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("businessType")), businessType));
        setReservationDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationDate")), reservationDate));
        setReservationTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("reservationTime")), reservationTime));
        setCheckDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("checkDate")), checkDate));
        setLateEarlyFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("lateEarlyFlag")), lateEarlyFlag));
        setCallDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("callDate")), callDate));
        setCallTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("callTime")), callTime));
        setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
        setFactoryAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryAreaName")), factoryAreaName));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setSysRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sysRemark")), sysRemark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
        setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
        setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
        setNextTarget(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("nextTarget")), nextTarget));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("checkId", StringUtils.toString(checkId, eiMetadata.getMeta("checkId")));
        map.put("carTraceNo", StringUtils.toString(carTraceNo, eiMetadata.getMeta("carTraceNo")));
        map.put("carTraceStatus", StringUtils.toString(carTraceStatus, eiMetadata.getMeta("carTraceStatus")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("handType", StringUtils.toString(handType, eiMetadata.getMeta("handType")));
        map.put("vehicleNo", StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
        map.put("idCard", StringUtils.toString(idCard, eiMetadata.getMeta("idCard")));
        map.put("driverName", StringUtils.toString(driverName, eiMetadata.getMeta("driverName")));
        map.put("telNum", StringUtils.toString(telNum, eiMetadata.getMeta("telNum")));
        map.put("reservationNumber", StringUtils.toString(reservationNumber, eiMetadata.getMeta("reservationNumber")));
        map.put("checkSource", StringUtils.toString(checkSource, eiMetadata.getMeta("checkSource")));
        map.put("voucherNum", StringUtils.toString(voucherNum, eiMetadata.getMeta("voucherNum")));
        map.put("businessType", StringUtils.toString(businessType, eiMetadata.getMeta("businessType")));
        map.put("reservationDate", StringUtils.toString(reservationDate, eiMetadata.getMeta("reservationDate")));
        map.put("reservationTime", StringUtils.toString(reservationTime, eiMetadata.getMeta("reservationTime")));
        map.put("checkDate", StringUtils.toString(checkDate, eiMetadata.getMeta("checkDate")));
        map.put("lateEarlyFlag", StringUtils.toString(lateEarlyFlag, eiMetadata.getMeta("lateEarlyFlag")));
        map.put("callDate", StringUtils.toString(callDate, eiMetadata.getMeta("callDate")));
        map.put("callTime", StringUtils.toString(callTime, eiMetadata.getMeta("callTime")));
        map.put("factoryArea", StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
        map.put("factoryAreaName", StringUtils.toString(factoryAreaName, eiMetadata.getMeta("factoryAreaName")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("sysRemark", StringUtils.toString(sysRemark, eiMetadata.getMeta("sysRemark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("tenantId", StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
        map.put("customerName", StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
        map.put("customerId", StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
        map.put("nextTarget", StringUtils.toString(nextTarget, eiMetadata.getMeta("nextTarget")));

        return map;

    }
}