package com.baosight.imom.li.ds.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.imom.common.utils.TokenUtils;
import com.baosight.imom.li.ds.domain.LIDS0101;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public class ServiceLIDS001R extends ServiceBase {


    /**
     * 页面初始化
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        super.initLoad(inInfo);
        return inInfo;
    }

    /**
     * 调用IMC接口，获取产销日报表数据
     *
     * @param inInfo
     * @return
     */
    public EiInfo getDailyReport(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //获取查询条件
            Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
            //表格分页条件
            Map hashMap = inInfo.getBlock(EiConstant.resultBlock).getAttr();
            int limit = MapUtils.getInteger(hashMap, "limit");
            int offset = MapUtils.getInteger(hashMap, "offset");
            //系统账套
            String segNo = MapUtils.getString(queryMap, "segNo");
            //报表日期
            String reportDate = MapUtils.getString(queryMap, "reportDate");
            //调用IMC查询接口
            EiInfo sendInfo = new EiInfo();
            sendInfo.set("segNo", segNo);
            sendInfo.set("reportDate", reportDate);
            sendInfo.set("limit", limit);
            sendInfo.set("offset", offset);
            sendInfo.set(EiConstant.serviceId, "S_VI_PM_9070");
            log("调用IMC获取产销报表传入参数：" + sendInfo);
            System.out.println("调用IMC获取产销报表传入参数：" + sendInfo);
            outInfo = EServiceManager.call(sendInfo, TokenUtils.getXplatToken());
            log("调用IMC获取产销报表返回参数：" + outInfo);
            System.out.println("调用IMC获取产销报表返回参数：" + outInfo);
            if (outInfo.getStatus() < EiConstant.STATUS_DEFAULT) {
                throw new RuntimeException("调用IMC获取产销报表接口失败：" + outInfo.getMsg());
            }
            //获取返回数据
            List<Map> reportList = (List<Map>) outInfo.get("reportList");
            if(CollectionUtils.isEmpty(reportList)){
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("没有查询到数据！");
            }else{
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                outInfo.setMsg("查询成功！");
            }
            outInfo.addBlock(EiConstant.resultBlock).addRows(reportList);
            outInfo.set("count", reportList.size());
        } catch (Exception ex) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(ex.getMessage());
        }
        return outInfo;
    }
}
