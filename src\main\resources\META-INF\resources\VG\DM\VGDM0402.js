$(function () {
    // 业务单元默认条件
    IMOMUtil.fillUnitInfo();

    // 设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "equipmentInfoMainQuery",
        _open: function (e, iframejQuery) {
            iframejQuery("#inqu_status-0-unitCode").val($("#inqu_status-0-unitCode").val());
            iframejQuery("#inqu_status-0-segNo").val($("#inqu_status-0-segNo").val());
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-eArchivesNo").val(rows[0].eArchivesNo);
                $("#inqu_status-0-equipmentName").val(rows[0].equipmentName);
                //$("#detail_status-0-deviceCode").val("");
                //$("#detail_status-0-deviceName").val("");
            }
        }
    });
    //分部设备弹窗
    IMOMUtil.windowTemplate({
        windowId: "deviceInfoMainQuery",
        _open: function (e, iframejQuery) {
            const eArchivesNo = $("#inqu_status-0-eArchivesNo").val().trim();
            if (!eArchivesNo) {
                NotificationUtil("操作失败，原因[请先选择设备名称！]", "error");
                e.preventDefault();
                return;
            }
            iframejQuery("#inqu_status-0-eArchivesNo").val(eArchivesNo);
        },
        afterSelect: function (rows) {
            if (rows.length > 0) {
                $("#inqu_status-0-deviceCode").val(rows[0].deviceCode);
                $("#inqu_status-0-deviceName").val(rows[0].deviceName);
            }
        }
    });

    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    // 点检日期默认当天查询条件
    const now = new Date();
    $("#inqu_status-0-checkStartDate").val(DateUtils.format(now, "yyyy-MM-dd"));
    $("#inqu_status-0-checkEndDate").val(DateUtils.format(now, "yyyy-MM-dd"));
    // grid配置
    IPLATUI.EFGrid = {
        // 点检计划子项
        "result": {
            columns: [
                {
                    field: "upperLimit",
                    // 渲染列的时候，会调用此方法，返回的内容将会作为列内容
                    template: function (item) {
                        return Number(item["upperLimit"]);
                    }
                },
                {
                    field: "lowerLimit",
                    // 渲染列的时候，会调用此方法，返回的内容将会作为列内容
                    template: function (item) {
                        return Number(item["upperLimit"]);
                    }
                }
            ],
            beforeEdit: function (e) {
                // 控制只有未点检状态可修改
                if (e.field === "actualsRemark" || e.field === "isNormal") {
                    if (e.model.checkPlanSubStatus !== "10") {
                        e.preventDefault();
                    }
                }
            },
            afterEdit: function (e) {
                // 正常时点检实绩同判断标准，异常时清空点检实绩
                if (e.field === "isNormal") {
                    if (e.model.isNormal === "0") {
                        resultGrid.setCellValue(e.row, "actualsRemark", e.model.judgmentStandard);
                    } else {
                        resultGrid.setCellValue(e.row, "actualsRemark", "");
                    }
                }
            },
            loadComplete: function (grid) {
                //实绩确认
                $("#UPDATE1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0402", "update", true, null, null, false);
                    //resultGrid.dataSource.page(1);
                });
                // 实绩撤销
                $("#CANCEL1").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result", "VGDM0402", "cancel", true, null, null, false);
                    //resultGrid.dataSource.page(1);
                });
                // 附件信息按钮
                $("#FILES").click(function () {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    const row = resultGrid.getCheckedRows()[0];
                    $("#inqu2_status-0-relevanceId").val(row.checkPlanSubId);
                    $("#inqu2_status-0-segNo").val(row.segNo);
                    result2Grid.dataSource.page(1);
                    attachmentInfoWindow.open().center();
                });
            }
        },
        // 附件信息
        "result2": {
            columns: [
                {
                    field: "uploadFilePathD",
                    enable: false,
                    readonly: true,
                    hidden: false,
                    locked: false,
                    template: function (e) {
                        if (!IPLAT.isBlankString(e.uploadFilePath)) {
                            return '<a href="' + e.uploadFilePath + '" target="_blank" download="' + e.uploadFileName + '">下载</a>';
                        } else {
                            return "";
                        }
                    }
                }
            ],
            loadComplete: function (grid) {
                // 附件上传按钮
                $("#FILEUPLOAD").click(function () {
                    $("#fileForm").click();
                });
            }
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo();

    var relevanceId = "";
    var segNo = "";
    IPLAT.FileUploader({
        id: "fileForm",
        ename: "fileForm",
        serviceName: "VGDM0802",
        methodName: "fileUpload",
        callback: function (e) {
            result2Grid.dataSource.page(1);
        },
        upload: function (e) {
            e.sender.options.async.saveUrl =
                IPLATUI.CONTEXT_PATH +
                "/XS/FA/XSFA4000.jsp?ename=" +
                "fileForm" +
                "&serviceName=" +
                "VGDM0802" +
                "&methodName=" +
                "fileUpload" +
                "&id=" +
                relevanceId +
                "&segNo=" +
                segNo +
                "&id2=VGDM0402";
        },
        select: function (e) {
            IPLAT.progress($("body"), true);
            relevanceId = $("#inqu2_status-0-relevanceId").val();
            segNo = $("#inqu2_status-0-segNo").val();
        }
    });
});
