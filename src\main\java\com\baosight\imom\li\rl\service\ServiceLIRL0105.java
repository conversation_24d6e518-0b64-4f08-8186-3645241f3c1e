package com.baosight.imom.li.rl.service;


import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.utils.CodeValueUtils;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.li.rl.dao.LIRL0105;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;


/**
 * @Author: 张博翔
 * @Description: ${车辆预约数管理}
 * @Date: 2024/8/7 10:37
 * @Version: 1.0
 */
public class ServiceLIRL0105 extends ServiceBase {

    public EiInfo initLoad(EiInfo inInfo) {
        List<String> list = new ArrayList<>();
        list.add("TEST_TYPE1");
        list.add("TEST_TYPE2");
        inInfo = CodeValueUtils.queryToBlock("QL00000", list, inInfo);
        inInfo.addBlock(EiConstant.resultBlock).addBlockMeta(new LIRL0105().eiMetadata);
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock(EiConstant.queryBlock).getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        queryMap.put("typeOfHandlingNotNull",1);
        outInfo = super.query(inInfo, LIRL0105.QUERY);
        return outInfo;
    }

    public EiInfo query2(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        Map queryMap = inInfo.getBlock("inqu2_status").getRow(0);
        String segNo = MapUtils.getString(queryMap, "segNo", "");
        String segName = MapUtils.getString(queryMap, "segName", "");
        if (org.apache.commons.lang.StringUtils.isBlank(segNo)) {
            String massage = "缺少业务单元代码不能查询！";
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(massage);
            return inInfo;
        }
        String customerId = MapUtils.getString(queryMap, "customerId", "");
        String customerId2 = MapUtils.getString(queryMap, "customerId2", "");
        if (StringUtils.isNotEmpty(customerId)){
            queryMap.put("reservationIdentity",20);
        }else if(StringUtils.isNotEmpty(customerId2)){
            queryMap.put("reservationIdentity",10);
        }
        queryMap.put("customerIdNotNull",1);
        outInfo = super.query(inInfo, LIRL0105.QUERY, new LIRL0105(), false, null, "inqu2_status", "result2", "result2");
        return outInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {

        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {

                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ05";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                //RecordUtils.setCreator(hashMap);
                // 创建人工号
                hashMap.put("recCreator", UserSession.getUserId());
                // 创建人姓名
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                // 创建时间
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                hashMap.put("recRevisor", UserSession.getUserId());
                // 修改人姓名
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                // 删除标记: 0-新增;1-删除;
                hashMap.put("delFlag", "0");
                hashMap.put("archiveFlag", "0");
                hashMap.put("tenantId", " ");
            }
            inInfo = super.insert(inInfo, LIRL0105.INSERT);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert2(EiInfo inInfo) {

        try {
            List<HashMap> listHashMap = inInfo.getBlock("result2").getRows();
            for (HashMap hashMap : listHashMap) {

                hashMap.put("status", 10);//状态
                hashMap.put("delFlag", 0);//记录删除标记
                hashMap.put("remark", MapUtils.getString(hashMap, "remark", "").trim());//备注
                String strSeqTypeId = "TLIRL_SEQ05";
                String segNo = MapUtils.getString(hashMap, "segNo");//系统账套
                Date date = new Date(System.currentTimeMillis());
                String[] args = {segNo.substring(0, 2), date.toString(), ""};
                String uuid = SequenceGenerator.getNextSequence(strSeqTypeId, args);
                hashMap.put("uuid", uuid);//UUID
                //RecordUtils.setCreator(hashMap);
                // 创建人工号
                hashMap.put("recCreator", UserSession.getUserId());
                // 创建人姓名
                hashMap.put("recCreatorName", UserSession.getLoginCName());
                // 创建时间
                hashMap.put("recCreateTime", DateUtil.curDateTimeStr14());
                // 修改人工号
                hashMap.put("recRevisor", UserSession.getUserId());
                // 修改人姓名
                hashMap.put("recRevisorName", UserSession.getLoginCName());
                // 修改时间
                hashMap.put("recReviseTime", DateUtil.curDateTimeStr14());
                // 删除标记: 0-新增;1-删除;
                hashMap.put("delFlag", "0");
                hashMap.put("archiveFlag", "0");
                hashMap.put("tenantId", " ");
            }
            inInfo = super.insert(inInfo, LIRL0105.INSERT, new LIRL0105(), true, "result2");
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0105);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0105.UPDATE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update2(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("result2").getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0105);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0105.UPDATE, new LIRL0105(), true, "result2");
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0105);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0105.DELETE);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete2(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("result2").getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0105);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put("status", "00");//记录删除标记
                hashMap.put("delFlag", 1);//记录删除标记
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0105.DELETE, new LIRL0105(), true, "result2");
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRM(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0105);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String typeOfHandling = MapUtils.getString(hashMap, "typeOfHandling", "");
                Map countMap = new HashMap();
                countMap.put("segNo", segNo);
                countMap.put("status", 20);
                countMap.put("typeOfHandling", typeOfHandling);
                countMap.put("delFlag", 0);
                //同一装卸业务只能有一条确认状态的预约最大数。
                int count = super.count(LIRL0105.COUNT, countMap);
                if (count > 0) {
                    String massage = "同一装卸业务只能有一条确认状态的预约最大数！";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("status", 20);//状态
                RecordUtils.setRevisor(hashMap);
                dao.update(LIRL0105.UPDATE, hashMap);
            }
            /*inInfo = super.update(inInfo, LIRL0105.UPDATE);*/
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRM2(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("result2").getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, lirl0105);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                String segNo = MapUtils.getString(hashMap, "segNo", "");
                String reservationIdentity = MapUtils.getString(hashMap, "reservationIdentity", "");
                String customerId = MapUtils.getString(hashMap, "customerId", "");
                Map countMap = new HashMap();
                countMap.put("segNo", segNo);
                countMap.put("status", 20);
                countMap.put("reservationIdentity", reservationIdentity);
                countMap.put("customerId", customerId);
                countMap.put("delFlag", 0);
                //同一承运商或客户只能有一条确认状态的预约最大数。
                int count = super.count(LIRL0105.COUNT, countMap);
                if (count > 0) {
                    String massage = "同一承运商或客户只能有一条确认状态的预约最大数！";
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    inInfo.setMsg(massage);
                    return inInfo;
                }
                hashMap.put("status", 20);//状态
                RecordUtils.setRevisor(hashMap);
                dao.update(LIRL0105.UPDATE, hashMap);
            }
            /*inInfo = super.update(inInfo, LIRL0105.UPDATE);*/
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRMNO(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(EiConstant.resultBlock).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
                    /*EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0105);*/
                    String status = lirl0105.getStatus();
                    //TODO
                    if (!"20".equals(status)) {
                        String massage = MessageCodeConstant.errorMessage.MSG_ERROR_STATUS_CAN_BE_COUNTER_CONFIRMED;
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("status", 10);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0105.UPDATE_STATUS);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 反确认.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo CONFIRMNO2(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock("result2").getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                Map map = new HashMap();
                map.put("uuid", MapUtils.getString(hashMap, "uuid", ""));
                List<LIRL0105> query = dao.query(LIRL0105.QUERY, map);
                for (LIRL0105 lirl0105 : query) {
//                    EiInfo outInfo = DaoUtils.isThereConfirmStatusAdded(inInfo, lirl0105);
                    String status = lirl0105.getStatus();
                    //TODO
                    if (!"20".equals(status)) {
                        String massage = MessageCodeConstant.errorMessage.MSG_ERROR_STATUS_CAN_BE_COUNTER_CONFIRMED;
                        inInfo.setStatus(EiConstant.STATUS_FAILURE);
                        inInfo.setMsg(massage);
                        return inInfo;
                    }
                }
                hashMap.put("status", 10);//状态
                RecordUtils.setRevisor(hashMap);
            }
            inInfo = super.update(inInfo, LIRL0105.UPDATE_STATUS, new LIRL0105(), true, "result2");
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
