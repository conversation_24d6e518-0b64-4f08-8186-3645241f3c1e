package com.baosight.imom.li.rl.service;


import com.baosight.bpm.util.UUIDUtil;
import com.baosight.imom.common.constants.MesConstant;
import com.baosight.imom.common.utils.DaoUtils;
import com.baosight.imom.common.utils.RecordUtils;
import com.baosight.imom.common.utils.UUIDUtils;
import com.baosight.imom.li.rl.dao.LIRL0309;
import com.baosight.imom.li.rl.dao.LIRL0315;
import com.baosight.imom.li.rl.dao.LIRL0315;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: 韩亚宁
 * @Description: ${装卸点起始地配置}
 * @Date: 2024/8/26 09:10
 * @Version: 1.0
 */
public class ServiceLIRL0315 extends ServiceBase {

    public static final String STATUS = "status"; //状态
    public static final String DEL_FLAG = "delFlag"; //删除标记
    public static final String REMARK = "remark"; //备注
    public static final String UUID = "uuid"; //uuid
    public static final String ZERO = "0"; //uuid
    public static final String ONE = "1"; //uuid
    public static final String RESULT2 = "result2"; //uuid
    public static final String RESULT = "result"; //uuid
    public static final String HAND_POINT_ID = "handPointId"; //uuid
    public static final String LOAD_FLAG = "loadFlag"; //uuid
    public static final String UNLOAD_FLAG = "unloadFlag"; //uuid
    public static final String EXPECTED_RECOVERY_TIME = "expectedRecoveryTime"; //uuid
    public static final String SEQ_NUM = "seqNum"; //uuid
    public static final String SYS_REMARK = "sysRemark"; //uuid
    public static final String CAR_TRACE_NO = "carTraceNo"; //uuid

    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.addBlock(RESULT2).addBlockMeta(new LIRL0315().eiMetadata);
        return inInfo;
    }

    /**
     * 查询
     * @param inInfo
     * @return
     */
    public EiInfo query(EiInfo inInfo) {
        EiInfo eiInfo = new EiInfo();
        Map listHashMap = (HashMap) inInfo.getBlock(RESULT).getRow(0);
        listHashMap.put("status","");
        List<LIRL0315> list = this.dao.query(LIRL0315.QUERY, listHashMap);
        eiInfo.addBlock(RESULT2).addBlockMeta(new LIRL0315().eiMetadata);
        eiInfo.getBlock(RESULT2).setRows(list);
        return eiInfo;
    }
    
    /**
     * 新增.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo insert(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT2).getRows();
            for (HashMap hashMap : listHashMap) {



                String unitCode = MapUtils.getString(hashMap, "unitCode");
                //去重
                HashMap<String, Object> hashMapQ = new HashMap<>();
                hashMapQ.put("handPointId", MapUtils.getString(hashMap, "handPointId"));
                hashMapQ.put("segNo", unitCode);
                hashMapQ.put("siteName", MapUtils.getString(hashMap, "siteName"));
                hashMapQ.put("statusNo","00");
                List<LIRL0315> queryLIRL0315 = this.dao.query(LIRL0315.QUERY, hashMapQ);
                if (CollectionUtils.isNotEmpty(queryLIRL0315)){
                    inInfo.setStatus(-1);
                    inInfo.setMsg("当前装卸点已存在"+MapUtils.getString(hashMap, "siteName")+"站点!");
                    return inInfo;
                }
                //装卸点
                String uuid  = UUIDUtil.genId();
                hashMap.put(CAR_TRACE_NO, "");//车辆跟踪号
                hashMap.put(STATUS, 10);//状态
                hashMap.put(DEL_FLAG, 0);//记录删除标记
                hashMap.put(REMARK, MapUtils.getString(hashMap, REMARK, "").trim());//备注
                hashMap.put(HAND_POINT_ID, MapUtils.getString(hashMap, HAND_POINT_ID, "").trim());//装卸点
                hashMap.put(UUID, UUIDUtils.getUUID());//uuid
                hashMap.put(SYS_REMARK,"");//uuid
                hashMap.put("uuid",uuid);//uuid
                hashMap.put("segNo",unitCode);//uuid
                //添加创建人、姓名、时间
                RecordUtils.setCreator(hashMap);
                this.dao.insert( LIRL0315.INSERT,hashMap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2005);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0003, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 修改.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo update(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT2).getRows();
            for (HashMap hashmap : listHashMap) {
                //后台查询状态判断
                List<LIRL0315> query = dao.query(LIRL0315.QUERY, hashmap);
                for (LIRL0315 LIRL0315:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0315);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                String businessType = MapUtils.getString(hashmap, "businessType");
                hashmap.put("sysRemark", " ");//记录删除标记
                RecordUtils.setRevisor(hashmap);//修改创建人、姓名、时间
                this.dao.update(LIRL0315.UPDATE,hashmap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }

    /**
     * 删除.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo delete(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT2).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0315> query = dao.query(LIRL0315.QUERY, hashMap);
                for (LIRL0315 LIRL0315:query){
                    EiInfo outInfo = DaoUtils.isThereNewStatusAdded(inInfo, LIRL0315);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K00);//撤销状态
                hashMap.put(DEL_FLAG, ONE);//记录删除标记
                hashMap.put("sysRemark", " ");//记录删除标记
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0315.UPDATE,hashMap);
            }
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2007);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0005, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 启用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo enable(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT2).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0315> query = dao.query(LIRL0315.QUERY, hashMap);
                for (LIRL0315 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereConfirmStatusEanbledDisabled1(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K30);//启用状态
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0315.UPDATE,hashMap);

            }
            // inInfo = super.update(inInfo, LIRL0315.UPDATE,RESULT2);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }


    /**
     * 停用.
     *
     * @param inInfo
     * @return
     * @Service:
     */
    public EiInfo disable(EiInfo inInfo) {
        try {
            List<HashMap> listHashMap = inInfo.getBlock(RESULT2).getRows();
            for (HashMap hashMap : listHashMap) {
                //后台查询状态判断
                List<LIRL0315> query = dao.query(LIRL0315.QUERY, hashMap);
                for (LIRL0315 LIRL0304:query){
                    EiInfo outInfo = DaoUtils.isThereEnabledStatusDisabled(inInfo, LIRL0304);
                    if (outInfo.getStatus() < 0) {
                        return outInfo;
                    }
                }
                hashMap.put(STATUS, MesConstant.Status.K99);//停用状态
                RecordUtils.setRevisor(hashMap);
                this.dao.update(LIRL0315.UPDATE,hashMap);
            }
            // inInfo = super.update(inInfo, LIRL0309.UPDATE,RESULT2);
            // 返回成功状态和消息
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_2006);
        } catch (Exception ex) {
            // 异常返回失败状态和消息
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsgByKey(MesConstant.EPResource.EP_0004, new String[]{ex.getMessage()});
        }
        return inInfo;
    }
}
