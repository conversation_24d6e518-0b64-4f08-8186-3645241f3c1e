<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="VGDM0102">

    <!-- 公共的查询结果列 -->
    <sql id="columnList">
        DEVICE_ID as "deviceId",  <!-- 分部设备序号 -->
        DEVICE_CODE as "deviceCode",  <!-- 分部设备代码 -->
        DEVICE_NAME as "deviceName",  <!-- 分部设备名称 -->
        E_ARCHIVES_NO as "eArchivesNo",  <!-- 设备档案编号 -->
        EQUIPMENT_NAME as "equipmentName",  <!-- 设备名称 -->
        EQUIPMENT_TYPE as "equipmentType",  <!-- 设备类型 -->
        MAKER_NAME as "makerName",  <!-- 制造商名称 -->
        USE_DATE as "useDate",  <!-- 使用日期 -->
        DRAWING_NUM as "drawingNum",  <!-- 图号 -->
        UUID as "uuid",  <!-- 唯一编码 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME as "recCreatorName",  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME as "recRevisorName",  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        TENANT_ID as "tenantId",  <!-- 租户ID -->
        ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
        DEL_FLAG as "delFlag",  <!-- 删除标记 -->
        SEG_NO as "segNo",  <!-- 系统帐套 -->
        UNIT_CODE as "unitCode", <!-- 业务单元代码 -->
        DEVICE_STATUS as "deviceStatus" <!-- 分部设备状态 -->
    </sql>

    <!-- 提取公共查询条件 -->
    <sql id="condition">
        <isNotEmpty prepend=" AND " property="segNo">
            SEG_NO = #segNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCode">
            UNIT_CODE = #unitCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="uuid">
            UUID = #uuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="eArchivesNo">
            E_ARCHIVES_NO = #eArchivesNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceCode">
            DEVICE_CODE like concat('%',#deviceCode#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="equalDeviceCode">
            DEVICE_CODE = #equalDeviceCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="deviceName">
            DEVICE_NAME like concat('%',#deviceName#,'%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = #delFlag#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="delFlag">
            DEL_FLAG = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="deviceStatus">
            DEVICE_STATUS = #deviceStatus#
        </isNotEmpty>
        <isEmpty prepend=" AND " property="deviceStatus">
            DEVICE_STATUS != '00'
        </isEmpty>
    </sql>

    <select id="query" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0102">
        SELECT
        <include refid="columnList"/>
        FROM ${mevgSchema}.TVGDM0102 WHERE 1=1
        <include refid="condition"/>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                DEVICE_CODE asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0102 WHERE 1=1
        <include refid="condition"/>
    </select>

    <select id="countById" resultClass="int">
        SELECT COUNT(*) FROM ${mevgSchema}.TVGDM0102 WHERE UUID = #uuid#
    </select>

    <select id="queryByNo" parameterClass="java.util.HashMap"
            resultClass="com.baosight.imom.vg.dm.domain.VGDM0102">
        SELECT
        <include refid="columnList"/>
        FROM ${mevgSchema}.TVGDM0102 WHERE DEVICE_CODE = #deviceCode#
        AND DEVICE_STATUS != '00'
    </select>

    <insert id="insert">
        INSERT INTO ${mevgSchema}.TVGDM0102 (DEVICE_ID,  <!-- 分部设备序号 -->
        DEVICE_CODE,  <!-- 分部设备代码 -->
        DEVICE_NAME,  <!-- 分部设备名称 -->
        E_ARCHIVES_NO,  <!-- 设备档案编号 -->
        EQUIPMENT_NAME,  <!-- 设备名称 -->
        EQUIPMENT_TYPE,  <!-- 设备类型 -->
        MAKER_NAME,  <!-- 制造商名称 -->
        USE_DATE,  <!-- 使用日期 -->
        DRAWING_NUM,  <!-- 图号 -->
        UUID,  <!-- 唯一编码 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        TENANT_ID,  <!-- 租户ID -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        DEL_FLAG,  <!-- 删除标记 -->
        SEG_NO,  <!-- 系统帐套 -->
        UNIT_CODE,  <!-- 业务单元代码 -->
        DEVICE_STATUS  <!-- 分部设备状态 -->
        )
        VALUES (#deviceId#, #deviceCode#, #deviceName#, #eArchivesNo#, #equipmentName#, #equipmentType#, #makerName#,
        #useDate#, #drawingNum#, #uuid#, #recCreator#, #recCreatorName#, #recCreateTime#, #recRevisor#,
        #recRevisorName#, #recReviseTime#, #tenantId#, #archiveFlag#, #delFlag#, #segNo#, #unitCode#, #deviceStatus#)
    </insert>

    <delete id="delete">
        DELETE FROM ${mevgSchema}.TVGDM0102 WHERE
        UUID = #uuid#
    </delete>

    <update id="update">
        UPDATE ${mevgSchema}.TVGDM0102
        SET
        DEVICE_ID = #deviceId#,   <!-- 分部设备序号 -->
        DEVICE_CODE = #deviceCode#,   <!-- 分部设备代码 -->
        DEVICE_NAME = #deviceName#,   <!-- 分部设备名称 -->
        E_ARCHIVES_NO = #eArchivesNo#,   <!-- 设备档案编号 -->
        EQUIPMENT_NAME = #equipmentName#,   <!-- 设备名称 -->
        EQUIPMENT_TYPE = #equipmentType#,   <!-- 设备类型 -->
        MAKER_NAME = #makerName#,   <!-- 制造商名称 -->
        USE_DATE = #useDate#,   <!-- 使用日期 -->
        DRAWING_NUM = #drawingNum#,   <!-- 图号 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATOR_NAME = #recCreatorName#,   <!-- 记录创建人姓名 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        TENANT_ID = #tenantId#,   <!-- 租户ID -->
        ARCHIVE_FLAG = #archiveFlag#,   <!-- 归档标记 -->
        DEL_FLAG = #delFlag#,   <!-- 删除标记 -->
        SEG_NO = #segNo#,   <!-- 系统帐套 -->
        UNIT_CODE = #unitCode#,  <!-- 业务单元代码 -->
        DEVICE_STATUS = #deviceStatus#  <!-- 分部设备状态 -->
        WHERE
        UUID = #uuid#
    </update>

    <update id="updateArchive">
        UPDATE ${mevgSchema}.TVGDM0102
        SET
        DEVICE_STATUS = '98',  <!-- 分部设备状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        SEG_NO = #segNo#
        AND DEVICE_CODE = #deviceCode#
    </update>

    <update id="updateArchive2">
        UPDATE ${mevgSchema}.TVGDM0102
        SET
        DEVICE_STATUS = '98',  <!-- 分部设备状态 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISOR_NAME = #recRevisorName#,   <!-- 记录修改人姓名 -->
        REC_REVISE_TIME = #recReviseTime#   <!-- 记录修改时刻 -->
        WHERE
        SEG_NO = #segNo#
        AND E_ARCHIVES_NO = #eArchivesNo#
    </update>

</sqlMap>
