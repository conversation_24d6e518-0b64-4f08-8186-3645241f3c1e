$(function () {
    // 业务单元默认条件
    var unitInfo = IMOMUtil.fillUnitInfo();
    // 查询按钮
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
            // onCheckRow: function (e) {
            //     if (e.checked === true) {
            //         $("#inqu2_status-0-eArchivesNo").val(e.model.eArchivesNo);
            //         const startTime = DateUtils.parseDate(e.model.startTime, 'yyyyMMddHHmmss');
            //         const endTime = DateUtils.parseDate(e.model.endTime, 'yyyyMMddHHmmss');
            //         $("#inqu2_status-0-startTime").val(DateUtils.format(startTime, 'yyyy-MM-dd HH:mm:ss'));
            //         $("#inqu2_status-0-endTime").val(DateUtils.format(endTime, 'yyyy-MM-dd HH:mm:ss'));
            //         result2Grid.dataSource.page(1);
            //     }
            // },
            loadComplete: function (grid) {
                // 计算工序时间
                $("#CALCULATE").on("click", function (e) {
                    if (!IMOMUtil.checkOneSelect(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData(
                        "result",
                        "VGDM10",
                        "calculate",
                        false, null, null, false);
                });
                // 计算加工工时
                $("#CALWORKHOUR").on("click", function (e) {
                    if (!IMOMUtil.checkOneSelect(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData(
                        "result",
                        "VGDM10",
                        "calWorkHour",
                        false, null, null, false);
                });
                // 显示计算结果
                $("#QUERY2").on("click", function (e) {
                    if (!IMOMUtil.checkOneSelect(resultGrid)) {
                        return;
                    }
                    const row = resultGrid.getCheckedRows()[0];
                    $("#inqu2_status-0-relevanceId").val(row.uuid);
                    calResultWindow.open().center();
                    result2Grid.dataSource.page(1);
                });
                // 显示生产实绩
                $("#QUERY3").on("click", function (e) {
                    if (!IMOMUtil.checkOneSelect(resultGrid)) {
                        return;
                    }
                    const row = resultGrid.getCheckedRows()[0];
                    $("#inqu3_status-0-relevanceId").val(row.uuid);
                    outResultWindow.open().center();
                    result3Grid.dataSource.page(1);
                });
                // 绘图按钮
                $("#DRAW").on("click", function (e) {
                    if (!IMOMUtil.checkSelected(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result",
                        "VGDM10",
                        "draw",
                        false,
                        function (ei) {
                            // 查询结果
                            const rows = ei.getBlock("result3").getMappedRows();
                            if (!rows?.length) {
                                processChart?.clear();
                                return;
                            }
                            // 优化数据处理
                            const {data, categories, baseTime} = processChartData(rows);
                            // 构建图表配置
                            if (processChart) {
                                const chartOption = buildChartOption(data, categories, baseTime);
                                processChart.setOption(chartOption);
                            }
                        }, null, false);
                });
                // 手工结束
                $("#FORCEEND").on("click", function (e) {
                    if (!IMOMUtil.checkOneSelect(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData("result",
                        "VGDM10",
                        "forceEnd",
                        true, null, null, false);
                });
                // 生产实绩重新上传
                $("#RESEND").on("click", function (e) {
                    if (!IMOMUtil.checkOneSelect(resultGrid)) {
                        return;
                    }
                    IMOMUtil.submitGridsData(
                        "result",
                        "VGDM10",
                        "reSendResult",
                        false, null, null, false);
                });

            }
        },
        "result3": {
            pageable: {
                pageSize: 50//单页展示50条数据
            },
        }
    };
    // 查询条件业务单元弹窗
    IMOMUtil.importUnitInfo({}, unitInfo);
    // 初始化ECharts图表实例
    var processChart = echarts.init(document.getElementById("processChart"));

    /**
     * 将时间字符串转换为秒数
     * @param {string} timeStr - 格式为"yyyy-MM-dd HH:mm:ss.SSS"的时间字符串
     * @returns {number} 转换后的秒数
     */
    function convertToSeconds(timeStr) {
        var date = DateUtils.parseDate(timeStr, "yyyy-MM-dd HH:mm:ss.SSS");
        return Math.floor(date.getTime() / 1000);
    }

    /**
     * 自定义渲染甘特图中的矩形项
     * @param {Object} params - 渲染参数
     * @param {Object} api - ECharts API接口
     * @returns {Object} 渲染的图形配置对象
     */
    function renderItem(params, api) {
        // 获取类别索引
        var categoryIndex = api.value(0);
        // 计算开始和结束坐标点
        var start = api.coord([api.value(1), categoryIndex]);
        var end = api.coord([api.value(2), categoryIndex]);
        // 计算矩形高度
        var height = api.size([0, 1])[1] * 0.6;
        // 裁剪矩形,确保在坐标系内显示
        var rectShape = echarts.graphic.clipRectByRect(
            {
                x: start[0],
                // todo 数据中增加分组根据分组动态计算y轴坐标实现柱状图分层展示
                y: start[1] - height / 2,
                width: end[0] - start[0],
                height: height
            },
            {
                x: params.coordSys.x,
                y: params.coordSys.y,
                width: params.coordSys.width,
                height: params.coordSys.height
            }
        );
        // 返回矩形的渲染配置
        return (
            rectShape && {
                type: "rect",
                transition: ["shape"],
                shape: rectShape,
                style: api.style()
            }
        );
    }

    // 数据处理函数
    function processChartData(rows) {
        const data = new Map();
        const categories = new Set();
        const categoryMap = new Map();
        // 找到最早的开始时间作为基准时间
        const baseTime = Math.min(...rows.map(row => convertToSeconds(row.startTime)));
        // 遍历数据进行处理
        rows.forEach(row => {
            const {equipmentName, procedureName, startTime, stopTime, duration} = row;
            // 根据设备名称生成类别信息(y轴数据)
            if (!categoryMap.has(equipmentName)) {
                categories.add(equipmentName);
                categoryMap.set(equipmentName, categoryMap.size);
            }
            // 初始化设备下数据（x轴数据）
            if (!data.has(procedureName)) {
                data.set(procedureName, []);
            }
            // 每一个设备对应的数据
            data.get(procedureName).push({
                name: procedureName,
                value: [
                    categoryMap.get(equipmentName),
                    convertToSeconds(startTime),
                    convertToSeconds(stopTime),
                    duration,
                    startTime,
                    stopTime
                ]
            });
        });
        // 转换数据结构为series数组
        const series = Array.from(data.entries()).map(([name, points]) => ({
            name,
            type: "custom",
            renderItem,
            label: {
                show: true,
                formatter: params => `${params.data.value[3]}s`
            },
            encode: {
                x: [1, 2],
                y: 0
            },
            data: points
        }));
        // 返回处理结果
        return {
            data: series,
            categories: Array.from(categories),
            baseTime
        };
    }

    // 构建图表配置
    function buildChartOption(series, categories, baseTime) {
        const dateArr = [];
        return {
            // 图例
            legend: {},
            // 提示框
            tooltip: {
                formatter: params => `开始:${params.value[4]}<br />结束:${params.value[5]}`
            },
            grid: {
                // 防止坐标轴标签超出图表区域
                containLabel: true
            },
            xAxis: {
                min: baseTime,
                axisLabel: {
                    formatter: val => {
                        const date = new Date(val * 1000).toLocaleString();
                        const [dateStr, timeStr] = date.split(" ");
                        // 如果日期已存在，则返回时间，否则返回日期
                        return dateArr.includes(dateStr) ? timeStr : (dateArr.push(dateStr), date);
                    }
                }
            },
            yAxis: {
                data: categories
            },
            series
        };
    }

    // 鼠标移入高亮
    processChart.on("mouseover", (param) => {
        processChart.dispatchAction({
            type: "highlight",
            seriesName: param.seriesName
        });
    });
    // 鼠标移出恢复
    processChart.on("mouseout", (param) => {
        processChart.dispatchAction({
            type: "downplay"
        });
    });
});