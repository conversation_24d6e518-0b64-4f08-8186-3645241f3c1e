/**
* Generate time : 2025-08-20 10:43:40
* Version : 1.0
*/
package com.baosight.imom.li.rl.dao;
import com.baosight.iplat4j.core.util.NumberUtils;
import java.math.BigDecimal;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;
import java.util.HashMap;
import java.util.Map;
import com.baosight.iplat4j.core.util.StringUtils;

/**
* Tlirl0509
* 
*/
public class LIRL0509 extends DaoEPBase {
    public static final String QUERY = "LIRL0509.query";
    public static final String QUERY_ALL = "LIRL0509.queryAll";
    public static final String COUNT = "LIRL0509.count";
    public static final String UPDATE = "LIRL0509.update";
    public static final String UPDATE_D = "LIRL0509.updateD";
    public static final String UPDATE_STATUS = "LIRL0509.updateStatus";
    public static final String UPDATE_REMAINING_TIME_DISPLAY = "LIRL0509.updateRemainingTimeDisplay";
    public static final String INSERT = "LIRL0509.insert";
    public static final String QUERY_ALL_DELIVERY = "LIRL0509.queryAllDelivery";
                private String uuid = " ";	/* 主键ID*/
                private String segNo = " ";		/* 系统账套*/
                private String segName = " ";		/* 业务单元简称*/
                private String vehicleNo = " ";		/* 车牌号*/
                private String deliveryStatus = " ";		/* 交付状态*/
                private String d_userNum = " ";		/* 分户号代码*/
                private String d_userNumName = " ";		/* 分户号简称*/
                private String ladingBillId = " ";		/* 提单号*/
                private BigDecimal ladingWeight = new BigDecimal("0");		/* 提单量*/
                private String dispensingQuantity = " ";		/* 配单量*/
                private String jobStatus = " ";		/* 作业状态*/
                private String customerId = " ";		/* 运输单位代码*/
                private String customerName = " ";		/* 运输单位名称*/
                private String status = "10";		/* 状态：10,20*/
                private String requireFinishDate = " ";		/* 交付时间*/
                private String recCreator = " ";		/* 记录创建人*/
                private String recCreatorName = " ";		/* 记录创建人姓名*/
                private String recCreateTime = " ";		/* 记录创建时间*/
                private String recRevisor = " ";		/* 记录修改人姓名*/
                private String recRevisorName = " ";		/* 记录修改人*/
                private String recReviseTime = " ";		/* 记录修改时间*/
                private Integer archiveFlag = Integer.valueOf(0);		/* 归档标记*/
                private Integer delFlag = Integer.valueOf(0);		/* 记录删除标记*/
                private String remark = " ";		/* 备注*/
                private String tenantId = " ";		/* 租户ID*/
                private String remainingTimeDisplay = " ";		/* 租户ID*/
                private String billRequireFinishDate = " ";		/* 租户ID*/
/**
* initialize the metadata
*/
public void initMetaData() {
EiColumn eiColumn;

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("主键ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("vehicleNo");
        eiColumn.setDescName("车牌号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deliveryStatus");
        eiColumn.setDescName("交付状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userNum");
        eiColumn.setDescName("分户号代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("d_userNumName");
        eiColumn.setDescName("分户号简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingBillId");
        eiColumn.setDescName("提单号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ladingWeight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(6);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("提单量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("dispensingQuantity");
        eiColumn.setDescName("配单量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("jobStatus");
        eiColumn.setDescName("作业状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerId");
        eiColumn.setDescName("运输单位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("customerName");
        eiColumn.setDescName("运输单位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态：10,20");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("requireFinishDate");
        eiColumn.setDescName("交付时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("记录删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantId");
        eiColumn.setDescName("租户ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remainingTimeDisplay");
        eiColumn.setDescName("截止交付剩余时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("billRequireFinishDate");
        eiColumn.setDescName("原提单交付时间");
        eiMetadata.addMeta(eiColumn);


}
/**
* the constructor
*/
public LIRL0509() {
initMetaData();
}

    public String getRemainingTimeDisplay() {
        return remainingTimeDisplay;
    }

    public void setRemainingTimeDisplay(String remainingTimeDisplay) {
        this.remainingTimeDisplay = remainingTimeDisplay;
    }

    /**
        * get the uuid - 主键ID
        * @return the uuid
        */
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
        * get the segNo - 系统账套
        * @return the segNo
        */
        public String getSegNo() {
        return this.segNo;
        }

        /**
        * set the segNo - 系统账套
        */
        public void setSegNo(String segNo) {
        this.segNo = segNo;
        }
        /**
        * get the vehicleNo - 车牌号
        * @return the vehicleNo
        */
        public String getVehicleNo() {
        return this.vehicleNo;
        }

        /**
        * set the vehicleNo - 车牌号
        */
        public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
        }
        /**
        * get the deliveryStatus - 交付状态
        * @return the deliveryStatus
        */
        public String getDeliveryStatus() {
        return this.deliveryStatus;
        }

        /**
        * set the deliveryStatus - 交付状态
        */
        public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
        }
        /**
        * get the d_userNum - 分户号代码
        * @return the d_userNum
        */
        public String getD_userNum() {
        return this.d_userNum;
        }

        /**
        * set the d_userNum - 分户号代码
        */
        public void setD_userNum(String d_userNum) {
        this.d_userNum = d_userNum;
        }
        /**
        * get the d_userNumName - 分户号简称
        * @return the d_userNumName
        */
        public String getD_userNumName() {
        return this.d_userNumName;
        }

        /**
        * set the d_userNumName - 分户号简称
        */
        public void setD_userNumName(String d_userNumName) {
        this.d_userNumName = d_userNumName;
        }
        /**
        * get the ladingBillId - 提单号
        * @return the ladingBillId
        */
        public String getLadingBillId() {
        return this.ladingBillId;
        }

        /**
        * set the ladingBillId - 提单号
        */
        public void setLadingBillId(String ladingBillId) {
        this.ladingBillId = ladingBillId;
        }
        /**
        * get the ladingWeight - 提单量
        * @return the ladingWeight
        */
        public BigDecimal getLadingWeight() {
        return this.ladingWeight;
        }

        /**
        * set the ladingWeight - 提单量
        */
        public void setLadingWeight(BigDecimal ladingWeight) {
        this.ladingWeight = ladingWeight;
        }
        /**
        * get the dispensingQuantity - 配单量
        * @return the dispensingQuantity
        */
        public String getDispensingQuantity() {
        return this.dispensingQuantity;
        }

        /**
        * set the dispensingQuantity - 配单量
        */
        public void setDispensingQuantity(String dispensingQuantity) {
        this.dispensingQuantity = dispensingQuantity;
        }
        /**
        * get the jobStatus - 作业状态
        * @return the jobStatus
        */
        public String getJobStatus() {
        return this.jobStatus;
        }

        /**
        * set the jobStatus - 作业状态
        */
        public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
        }
        /**
        * get the customerId - 运输单位代码
        * @return the customerId
        */
        public String getCustomerId() {
        return this.customerId;
        }

        /**
        * set the customerId - 运输单位代码
        */
        public void setCustomerId(String customerId) {
        this.customerId = customerId;
        }
        /**
        * get the customerName - 运输单位名称
        * @return the customerName
        */
        public String getCustomerName() {
        return this.customerName;
        }

        /**
        * set the customerName - 运输单位名称
        */
        public void setCustomerName(String customerName) {
        this.customerName = customerName;
        }
        /**
        * get the status - 状态：10,20
        * @return the status
        */
        public String getStatus() {
        return this.status;
        }

        /**
        * set the status - 状态：10,20
        */
        public void setStatus(String status) {
        this.status = status;
        }
        /**
        * get the requireFinishDate - 交付时间
        * @return the requireFinishDate
        */
        public String getRequireFinishDate() {
        return this.requireFinishDate;
        }

        /**
        * set the requireFinishDate - 交付时间
        */
        public void setRequireFinishDate(String requireFinishDate) {
        this.requireFinishDate = requireFinishDate;
        }
        /**
        * get the recCreator - 记录创建人
        * @return the recCreator
        */
        public String getRecCreator() {
        return this.recCreator;
        }

        /**
        * set the recCreator - 记录创建人
        */
        public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
        }
        /**
        * get the recCreatorName - 记录创建人姓名
        * @return the recCreatorName
        */
        public String getRecCreatorName() {
        return this.recCreatorName;
        }

        /**
        * set the recCreatorName - 记录创建人姓名
        */
        public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
        }
        /**
        * get the recCreateTime - 记录创建时间
        * @return the recCreateTime
        */
        public String getRecCreateTime() {
        return this.recCreateTime;
        }

        /**
        * set the recCreateTime - 记录创建时间
        */
        public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
        }
        /**
        * get the recRevisor - 记录修改人姓名
        * @return the recRevisor
        */
        public String getRecRevisor() {
        return this.recRevisor;
        }

        /**
        * set the recRevisor - 记录修改人姓名
        */
        public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
        }
        /**
        * get the recRevisorName - 记录修改人
        * @return the recRevisorName
        */
        public String getRecRevisorName() {
        return this.recRevisorName;
        }

        /**
        * set the recRevisorName - 记录修改人
        */
        public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
        }
        /**
        * get the recReviseTime - 记录修改时间
        * @return the recReviseTime
        */
        public String getRecReviseTime() {
        return this.recReviseTime;
        }

        /**
        * set the recReviseTime - 记录修改时间
        */
        public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
        }
        /**
        * get the archiveFlag - 归档标记
        * @return the archiveFlag
        */
        public Integer getArchiveFlag() {
        return this.archiveFlag;
        }

        /**
        * set the archiveFlag - 归档标记
        */
        public void setArchiveFlag(Integer archiveFlag) {
        this.archiveFlag = archiveFlag;
        }
        /**
        * get the delFlag - 记录删除标记
        * @return the delFlag
        */
        public Integer getDelFlag() {
        return this.delFlag;
        }

        /**
        * set the delFlag - 记录删除标记
        */
        public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
        }
        /**
        * get the remark - 备注
        * @return the remark
        */
        public String getRemark() {
        return this.remark;
        }

        /**
        * set the remark - 备注
        */
        public void setRemark(String remark) {
        this.remark = remark;
        }
        /**
        * get the tenantId - 租户ID
        * @return the tenantId
        */
        public String getTenantId() {
        return this.tenantId;
        }

        /**
        * set the tenantId - 租户ID
        */
        public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
        }

    public String getBillRequireFinishDate() {
        return billRequireFinishDate;
    }

    public void setBillRequireFinishDate(String billRequireFinishDate) {
        this.billRequireFinishDate = billRequireFinishDate;
    }

    /**
* get the value from Map
*/
public void fromMap(Map map) {

                setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
                setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
                setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
                setVehicleNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("vehicleNo")), vehicleNo));
                setDeliveryStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deliveryStatus")), deliveryStatus));
                setD_userNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userNum")), d_userNum));
                setD_userNumName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("d_userNumName")), d_userNumName));
                setLadingBillId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ladingBillId")), ladingBillId));
                setLadingWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("ladingWeight")), ladingWeight));
                setDispensingQuantity(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("dispensingQuantity")), dispensingQuantity));
                setJobStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("jobStatus")), jobStatus));
                setCustomerId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerId")), customerId));
                setCustomerName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("customerName")), customerName));
                setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
                setRequireFinishDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("requireFinishDate")), requireFinishDate));
                setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
                setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
                setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
                setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
                setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
                setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
                setArchiveFlag(NumberUtils.toInteger(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
                setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
                setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
                setTenantId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantId")), tenantId));
                setRemainingTimeDisplay(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remainingTimeDisplay")), remainingTimeDisplay));
                setBillRequireFinishDate(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("billRequireFinishDate")), billRequireFinishDate));
}

/**
* set the value to Map
*/
public Map toMap() {

Map map = new HashMap();
                map.put("uuid",StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
                map.put("segNo",StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
                map.put("segName",StringUtils.toString(segName, eiMetadata.getMeta("segName")));
                map.put("vehicleNo",StringUtils.toString(vehicleNo, eiMetadata.getMeta("vehicleNo")));
                map.put("deliveryStatus",StringUtils.toString(deliveryStatus, eiMetadata.getMeta("deliveryStatus")));
                map.put("d_userNum",StringUtils.toString(d_userNum, eiMetadata.getMeta("d_userNum")));
                map.put("d_userNumName",StringUtils.toString(d_userNumName, eiMetadata.getMeta("d_userNumName")));
                map.put("ladingBillId",StringUtils.toString(ladingBillId, eiMetadata.getMeta("ladingBillId")));
                map.put("ladingWeight",StringUtils.toString(ladingWeight, eiMetadata.getMeta("ladingWeight")));
                map.put("dispensingQuantity",StringUtils.toString(dispensingQuantity, eiMetadata.getMeta("dispensingQuantity")));
                map.put("jobStatus",StringUtils.toString(jobStatus, eiMetadata.getMeta("jobStatus")));
                map.put("customerId",StringUtils.toString(customerId, eiMetadata.getMeta("customerId")));
                map.put("customerName",StringUtils.toString(customerName, eiMetadata.getMeta("customerName")));
                map.put("status",StringUtils.toString(status, eiMetadata.getMeta("status")));
                map.put("requireFinishDate",StringUtils.toString(requireFinishDate, eiMetadata.getMeta("requireFinishDate")));
                map.put("recCreator",StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
                map.put("recCreatorName",StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
                map.put("recCreateTime",StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
                map.put("recRevisor",StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
                map.put("recRevisorName",StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
                map.put("recReviseTime",StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
                map.put("archiveFlag",StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
                map.put("delFlag",StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
                map.put("remark",StringUtils.toString(remark, eiMetadata.getMeta("remark")));
                map.put("tenantId",StringUtils.toString(tenantId, eiMetadata.getMeta("tenantId")));
                map.put("remainingTimeDisplay",StringUtils.toString(remainingTimeDisplay, eiMetadata.getMeta("remainingTimeDisplay")));
                map.put("billRequireFinishDate",StringUtils.toString(billRequireFinishDate, eiMetadata.getMeta("billRequireFinishDate")));


return map;

}
}