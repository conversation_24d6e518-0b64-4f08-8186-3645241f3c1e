package com.baosight.imom.common.utils;

import cfca.sadk.org.bouncycastle.jce.provider.BouncyCastleProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.security.*;
import java.security.spec.InvalidParameterSpecException;
import java.util.*;

public class WechatUtil {
    private static final Logger log = LoggerFactory.getLogger(WechatUtil.class);

    public static JSONObject getSessionKeyOrOpenId(String code) {
        String appid = PlatApplicationContext.getProperty("service.wx.appid");
        String secret = PlatApplicationContext.getProperty("service.wx.secret");
        String requestUrl = "https://api.weixin.qq.com/sns/jscode2session";
        Map<String, String> requestUrlParam = new HashMap<>();
        //小程序appId
        requestUrlParam.put("appid", appid);
        //小程序secret
        requestUrlParam.put("secret", secret);
        //小程序端返回的code
        requestUrlParam.put("js_code", code);
        //默认参数
        requestUrlParam.put("grant_type", "authorization_code");
        //发送post请求读取调用微信接口获取openid用户唯一标识
        JSONObject jsonObject = JSON.parseObject(doPost(requestUrl, requestUrlParam));
        return jsonObject;
    }


    public static String doGet(String url, Map<String, String> param) {

        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();

        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            // 创建uri
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, param.get(key));
                }
            }
            URI uri = builder.build();

            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);

            // 执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultString;
    }

    public static String doPost(String url, Map<String, String> param) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            // 创建参数列表
            if (param != null) {
                List<BasicNameValuePair> paramList = new ArrayList<>();
                for (String key : param.keySet()) {
                    paramList.add(new BasicNameValuePair(key, param.get(key)));
                }
                // 模拟表单
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList);
                httpPost.setEntity(entity);
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return resultString;
    }

    public static String doPost(String url, JSONObject param) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // // 创建Http Post请求
            // HttpPost httpPost = new HttpPost(url);
            // List<BasicNameValuePair> nameValuePairList = new ArrayList<>();
            // // 创建参数列表
            // if (param != null) {
            //     ObjectMapper objectMapper = new ObjectMapper();
            //     LinkedHashMap<String, String> jsonMap = objectMapper.readValue(param, LinkedHashMap.class);
            //     for (String key : jsonMap.keySet()) {
            //         nameValuePairList.add(new BasicNameValuePair(key, jsonMap.get(key)));
            //     }
            //     // 模拟表单
            //     UrlEncodedFormEntity entity = new UrlEncodedFormEntity(nameValuePairList);
            //     httpPost.setEntity(entity);
            // }
            // // 执行http请求
            // response = httpClient.execute(httpPost);
            // resultString = EntityUtils.toString(response.getEntity(), "utf-8");

            // 创建 HttpClient
            HttpPost httpPost = new HttpPost(url);

            // 设置请求体为 JSON 字符串
            StringEntity entity = new StringEntity((param.toString()));
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-Type", "application/json");

            // 发送请求并获取响应
            HttpResponse response1 = httpClient.execute(httpPost);
            HttpEntity responseEntity = response1.getEntity();
            resultString = EntityUtils.toString(responseEntity);
            System.out.println(resultString);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resultString;
    }


    public static JSONObject getUserInfo(String encryptedData, String iv, String sessionKey) {
        // 被加密的数据
        byte[] dataByte = cn.hutool.core.codec.Base64.decode(encryptedData);
        // 加密秘钥
        byte[] keyByte = cn.hutool.core.codec.Base64.decode(sessionKey);
        // 偏移量
        byte[] ivByte = cn.hutool.core.codec.Base64.decode(iv);
        try {
            // 如果密钥不足16位，那么就补足.
            int base = 16;
            keyByte = completToBase(keyByte, base);
            ivByte = completToBase(ivByte, base);

            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, "UTF-8");
                System.out.println(result);
                return JSONObject.parseObject(result);
            }
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
        } catch (NoSuchPaddingException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidParameterSpecException e) {
            log.error(e.getMessage(), e);
        } catch (IllegalBlockSizeException e) {
            log.error(e.getMessage(), e);
        } catch (BadPaddingException e) {
            log.error(e.getMessage(), e);
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidAlgorithmParameterException e) {
            log.error(e.getMessage(), e);
        } catch (NoSuchProviderException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 补全数组位数
     *
     * @param bytes
     * @param base
     * @return
     */
    public static byte[] completToBase(byte[] bytes, int base) {
        byte[] temp = new byte[]{};
        if (bytes.length % base != 0) {
            int groups = bytes.length / base + (bytes.length % base != 0 ? 1 : 0);
            temp = new byte[groups * base];
            Arrays.fill(temp, (byte) 0);
            System.arraycopy(bytes, 0, temp, 0, bytes.length);
            return temp;
        } else {
            return bytes;
        }
    }


    public static String getWxToken() {
        String appid = PlatApplicationContext.getProperty("service.wx.appid");
        String secret = PlatApplicationContext.getProperty("service.wx.secret");

        // String requestUrl = "https://api.weixin.qq.com/cgi-bin/token";
        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+appid+"&secret="+secret;
        // Map<String, String> requestUrlParam = new HashMap<>();
        // //小程序appId
        // requestUrlParam.put("appid", appid);
        // //小程序secret
        // requestUrlParam.put("secret", secret);
        // //小程序端返回的code
        // //默认参数
        // requestUrlParam.put("grant_type", "client_credential");
        //发送post请求读取调用微信接口获取openid用户唯一标识

        try {
            URL url = new URL(requestUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine())!= null) {
                    response.append(inputLine);
                }
                in.close();
                return response.toString();
            } else {
                throw new Exception("获取微信凭证失败，响应码：" + responseCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return doGet(requestUrl, null);
    }


    /***
     * 获取用户信息
     * @param code
     * @param access_token
     * @return
     */
    public static String getWxUserInfo(String code,String access_token ) {
        String appid = PlatApplicationContext.getProperty("service.wx.appid");
        String secret = PlatApplicationContext.getProperty("service.wx.secret");
        String requestUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token="+access_token;
        JSONObject jsonParams = new JSONObject();
        jsonParams.put("code", code);
        //小程序端返回的code
        //发送post请求读取调用微信接口获取openid用户唯一标识
        String jsonParams1 = doPost(requestUrl, jsonParams);
        // JSONObject jsonObject = JSON.parseObject();
        return jsonParams1;
    }

}