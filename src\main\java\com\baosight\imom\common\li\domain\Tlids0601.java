/**
 * Generate time : 2024-11-25 16:20:14
 * Version : 1.0
 */
package com.baosight.imom.common.li.domain;

import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tlids0601
 *
 */
public class Tlids0601 extends DaoEPBase {

    private String segNo = " ";        /* 系统账套*/
    private String segName = " ";        /* 业务单元简称*/
    private String unitCode = " ";        /* 业务单元代代码*/
    private String warehouseCode = " ";        /* 仓库代码*/
    private String warehouseName = " ";        /* 仓库名称*/
    private String locationId = " ";        /* 库位代码*/
    private String locationName = " ";        /* 库位名称*/
    private String factoryArea = " ";        /* 厂区代码*/
    private String factoryAreaName = " ";        /* 厂区名称*/
    private String factoryBuilding = " ";        /* 厂房代码*/
    private String factoryBuildingName = " ";        /* 厂房名称*/
    private String crossArea = " ";        /* 跨区编码*/
    private String crossAreaName = " ";        /* 跨区名称*/
    private String posDirCode = " ";        /* 层数标记*/
    private String managementStyle = " ";        /* 管理方式(10点状 20条状 21条状特殊)*/
    private String actionFlag = " ";        /* 板卷标记(0板 1卷)*/
    private String ifPlanFlag = " ";        /* 是否参与库位推荐(0参与 1不参与)*/
    private String loadingChannelNo = " ";        /* 装卸通道编码*/
    private String loadingChannelName = " ";        /* 装卸通道名称*/
    private String loadingPointNo = " ";        /* 装卸点编码*/
    private String loadingPointName = " ";        /* 装卸点名称*/
    private String xInitialPoint = null;        /* X轴起始点*/
    private String xDestination = null;        /* X轴终到点*/
    private String yInitialPoint = null;        /* Y轴起始点*/
    private String yDestination = null;        /* Y轴终到点*/
    private String status = " ";        /* 状态(启用：10、停用：20)*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/
    private String archiveFlag = " ";        /* 归档标记*/
    private String tenantUser = " ";        /* 租户*/
    private Integer delFlag = Integer.valueOf(0);        /* 删除标记*/
    private String uuid = " ";        /* ID*/
    private String specUpper = " ";		/* 宽度上限 单位(mm)*/
    private String specLower = " ";		/* 宽度下限 单位(mm)*/
    private String pointLowerLength = " ";		/* 点状库位长度下限*/
    private String pointUpperLength = " ";		/* 点状库位长度上限*/
    private String standFlag = " ";		/* 是否立式库位。0：卧式、1：立式*/
    private String c_no = " ";		/* 所在跨区序列号*/
    private String endPoint = " ";		/* 终点标记*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseCode");
        eiColumn.setDescName("仓库代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("warehouseName");
        eiColumn.setDescName("仓库名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryArea");
        eiColumn.setDescName("厂区代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryAreaName");
        eiColumn.setDescName("厂区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuilding");
        eiColumn.setDescName("厂房代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("factoryBuildingName");
        eiColumn.setDescName("厂房名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossArea");
        eiColumn.setDescName("跨区编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("crossAreaName");
        eiColumn.setDescName("跨区名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("posDirCode");
        eiColumn.setDescName("层数标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("managementStyle");
        eiColumn.setDescName("管理方式(10点状 20条状 21条状特殊)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actionFlag");
        eiColumn.setDescName("板卷标记(0板 1卷)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ifPlanFlag");
        eiColumn.setDescName("是否参与库位推荐(0参与 1不参与)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingChannelNo");
        eiColumn.setDescName("装卸通道编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingChannelName");
        eiColumn.setDescName("装卸通道名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingPointNo");
        eiColumn.setDescName("装卸点编码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("loadingPointName");
        eiColumn.setDescName("装卸点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("xInitialPoint");
        eiColumn.setDescName("X轴起始点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("xDestination");
        eiColumn.setDescName("X轴终到点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("yInitialPoint");
        eiColumn.setDescName("Y轴起始点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("yDestination");
        eiColumn.setDescName("Y轴终到点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("状态(启用：10、停用：20)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("tenantUser");
        eiColumn.setDescName("租户");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("delFlag");
        eiColumn.setDescName("删除标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setDescName("ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specUpper");
        eiColumn.setDescName("宽度上限 单位(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("specLower");
        eiColumn.setDescName("宽度下限 单位(mm)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pointLowerLength");
        eiColumn.setDescName("点状库位长度下限");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("pointUpperLength");
        eiColumn.setDescName("点状库位长度上限");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("standFlag");
        eiColumn.setDescName("是否立式库位。0：卧式、1：立式");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("c_no");
        eiColumn.setDescName("所在跨区序列号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("endPoint");
        eiColumn.setDescName("终点标记");
        eiMetadata.addMeta(eiColumn);

    }

    /**
     * the constructor
     */
    public Tlids0601() {
        initMetaData();
    }

    /**
     * get the segNo - 系统账套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the unitCode - 业务单元代代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the warehouseCode - 仓库代码
     * @return the warehouseCode
     */
    public String getWarehouseCode() {
        return this.warehouseCode;
    }

    /**
     * set the warehouseCode - 仓库代码
     */
    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    /**
     * get the warehouseName - 仓库名称
     * @return the warehouseName
     */
    public String getWarehouseName() {
        return this.warehouseName;
    }

    /**
     * set the warehouseName - 仓库名称
     */
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    /**
     * get the locationId - 库位代码
     * @return the locationId
     */
    public String getLocationId() {
        return this.locationId;
    }

    /**
     * set the locationId - 库位代码
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * get the locationName - 库位名称
     * @return the locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * set the locationName - 库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * get the factoryArea - 厂区代码
     * @return the factoryArea
     */
    public String getFactoryArea() {
        return this.factoryArea;
    }

    /**
     * set the factoryArea - 厂区代码
     */
    public void setFactoryArea(String factoryArea) {
        this.factoryArea = factoryArea;
    }

    /**
     * get the factoryAreaName - 厂区名称
     * @return the factoryAreaName
     */
    public String getFactoryAreaName() {
        return this.factoryAreaName;
    }

    /**
     * set the factoryAreaName - 厂区名称
     */
    public void setFactoryAreaName(String factoryAreaName) {
        this.factoryAreaName = factoryAreaName;
    }

    /**
     * get the factoryBuilding - 厂房代码
     * @return the factoryBuilding
     */
    public String getFactoryBuilding() {
        return this.factoryBuilding;
    }

    /**
     * set the factoryBuilding - 厂房代码
     */
    public void setFactoryBuilding(String factoryBuilding) {
        this.factoryBuilding = factoryBuilding;
    }

    /**
     * get the factoryBuildingName - 厂房名称
     * @return the factoryBuildingName
     */
    public String getFactoryBuildingName() {
        return this.factoryBuildingName;
    }

    /**
     * set the factoryBuildingName - 厂房名称
     */
    public void setFactoryBuildingName(String factoryBuildingName) {
        this.factoryBuildingName = factoryBuildingName;
    }

    /**
     * get the crossArea - 跨区编码
     * @return the crossArea
     */
    public String getCrossArea() {
        return this.crossArea;
    }

    /**
     * set the crossArea - 跨区编码
     */
    public void setCrossArea(String crossArea) {
        this.crossArea = crossArea;
    }

    /**
     * get the crossAreaName - 跨区名称
     * @return the crossAreaName
     */
    public String getCrossAreaName() {
        return this.crossAreaName;
    }

    /**
     * set the crossAreaName - 跨区名称
     */
    public void setCrossAreaName(String crossAreaName) {
        this.crossAreaName = crossAreaName;
    }

    /**
     * get the posDirCode - 层数标记
     * @return the posDirCode
     */
    public String getPosDirCode() {
        return this.posDirCode;
    }

    /**
     * set the posDirCode - 层数标记
     */
    public void setPosDirCode(String posDirCode) {
        this.posDirCode = posDirCode;
    }

    /**
     * get the managementStyle - 管理方式(10点状 20条状 21条状特殊)
     * @return the managementStyle
     */
    public String getManagementStyle() {
        return this.managementStyle;
    }

    /**
     * set the managementStyle - 管理方式(10点状 20条状 21条状特殊)
     */
    public void setManagementStyle(String managementStyle) {
        this.managementStyle = managementStyle;
    }

    /**
     * get the actionFlag - 板卷标记(0板 1卷)
     * @return the actionFlag
     */
    public String getActionFlag() {
        return this.actionFlag;
    }

    /**
     * set the actionFlag - 板卷标记(0板 1卷)
     */
    public void setActionFlag(String actionFlag) {
        this.actionFlag = actionFlag;
    }

    /**
     * get the ifPlanFlag - 是否参与库位推荐(0参与 1不参与)
     * @return the ifPlanFlag
     */
    public String getIfPlanFlag() {
        return this.ifPlanFlag;
    }

    /**
     * set the ifPlanFlag - 是否参与库位推荐(0参与 1不参与)
     */
    public void setIfPlanFlag(String ifPlanFlag) {
        this.ifPlanFlag = ifPlanFlag;
    }

    /**
     * get the loadingChannelNo - 装卸通道编码
     * @return the loadingChannelNo
     */
    public String getLoadingChannelNo() {
        return this.loadingChannelNo;
    }

    /**
     * set the loadingChannelNo - 装卸通道编码
     */
    public void setLoadingChannelNo(String loadingChannelNo) {
        this.loadingChannelNo = loadingChannelNo;
    }

    /**
     * get the loadingChannelName - 装卸通道名称
     * @return the loadingChannelName
     */
    public String getLoadingChannelName() {
        return this.loadingChannelName;
    }

    /**
     * set the loadingChannelName - 装卸通道名称
     */
    public void setLoadingChannelName(String loadingChannelName) {
        this.loadingChannelName = loadingChannelName;
    }

    /**
     * get the loadingPointNo - 装卸点编码
     * @return the loadingPointNo
     */
    public String getLoadingPointNo() {
        return this.loadingPointNo;
    }

    /**
     * set the loadingPointNo - 装卸点编码
     */
    public void setLoadingPointNo(String loadingPointNo) {
        this.loadingPointNo = loadingPointNo;
    }

    /**
     * get the loadingPointName - 装卸点名称
     * @return the loadingPointName
     */
    public String getLoadingPointName() {
        return this.loadingPointName;
    }

    /**
     * set the loadingPointName - 装卸点名称
     */
    public void setLoadingPointName(String loadingPointName) {
        this.loadingPointName = loadingPointName;
    }

    /**
     * get the xInitialPoint - X起始
     * @return the xInitialPoint
     */
    public String getxInitialPoint() {
        return xInitialPoint;
    }

    /**
     * set the xInitialPoint - X起始
     */
    public void setxInitialPoint(String xInitialPoint) {
        this.xInitialPoint = xInitialPoint;
    }

    /**
     * get the xDestination - X终点
     * @return the xDestination
     */
    public String getxDestination() {
        return xDestination;
    }

    /**
     * set the xDestination - X终点
     */
    public void setxDestination(String xDestination) {
        this.xDestination = xDestination;
    }

    /**
     * get the yInitialPoint - Y起始
     * @return the yInitialPoint
     */
    public String getyInitialPoint() {
        return yInitialPoint;
    }

    /**
     * set the yInitialPoint - Y起始
     */
    public void setyInitialPoint(String yInitialPoint) {
        this.yInitialPoint = yInitialPoint;
    }

    /**
     * get the yDestination - Y终点
     * @return the yDestination
     */
    public String getyDestination() {
        return yDestination;
    }

    /**
     * set the yDestination - Y终点
     */
    public void setyDestination(String yDestination) {
        this.yDestination = yDestination;
    }

    /**
     * get the status - 状态(启用：10、停用：20)
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态(启用：10、停用：20)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the archiveFlag - 归档标记
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the tenantUser - 租户
     * @return the tenantUser
     */
    public String getTenantUser() {
        return this.tenantUser;
    }

    /**
     * set the tenantUser - 租户
     */
    public void setTenantUser(String tenantUser) {
        this.tenantUser = tenantUser;
    }

    /**
     * get the delFlag - 删除标记
     * @return the delFlag
     */
    public Integer getDelFlag() {
        return this.delFlag;
    }

    /**
     * set the delFlag - 删除标记
     */
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * get the uuid - ID
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - ID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the specUpper - 宽度上限 单位(mm)
     * @return the specUpper
     */
    public String getSpecUpper() {
        return this.specUpper;
    }

    /**
     * set the specUpper - 宽度上限 单位(mm)
     */
    public void setSpecUpper(String specUpper) {
        this.specUpper = specUpper;
    }
    /**
     * get the specLower - 宽度下限 单位(mm)
     * @return the specLower
     */
    public String getSpecLower() {
        return this.specLower;
    }

    /**
     * set the specLower - 宽度下限 单位(mm)
     */
    public void setSpecLower(String specLower) {
        this.specLower = specLower;
    }
    /**
     * get the pointLowerLength - 点状库位长度下限
     * @return the pointLowerLength
     */
    public String getPointLowerLength() {
        return this.pointLowerLength;
    }

    /**
     * set the pointLowerLength - 点状库位长度下限
     */
    public void setPointLowerLength(String pointLowerLength) {
        this.pointLowerLength = pointLowerLength;
    }
    /**
     * get the pointUpperLength - 点状库位长度上限
     * @return the pointUpperLength
     */
    public String getPointUpperLength() {
        return this.pointUpperLength;
    }

    /**
     * set the pointUpperLength - 点状库位长度上限
     */
    public void setPointUpperLength(String pointUpperLength) {
        this.pointUpperLength = pointUpperLength;
    }
    /**
     * get the standFlag - 是否立式库位。0：卧式、1：立式
     * @return the standFlag
     */
    public String getStandFlag() {
        return this.standFlag;
    }

    /**
     * set the standFlag - 是否立式库位。0：卧式、1：立式
     */
    public void setStandFlag(String standFlag) {
        this.standFlag = standFlag;
    }
    /**
     * get the c_no - 所在跨区序列号
     * @return the c_no
     */
    public String getC_no() {
        return this.c_no;
    }

    /**
     * set the c_no - 所在跨区序列号
     */
    public void setC_no(String c_no) {
        this.c_no = c_no;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    /**
     * get the value from Map
     */


    public void fromMap(Map map) {

        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setWarehouseCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseCode")), warehouseCode));
        setWarehouseName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("warehouseName")), warehouseName));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setFactoryArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryArea")), factoryArea));
        setFactoryAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryAreaName")), factoryAreaName));
        setFactoryBuilding(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuilding")), factoryBuilding));
        setFactoryBuildingName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("factoryBuildingName")), factoryBuildingName));
        setCrossArea(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossArea")), crossArea));
        setCrossAreaName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("crossAreaName")), crossAreaName));
        setPosDirCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("posDirCode")), posDirCode));
        setManagementStyle(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("managementStyle")), managementStyle));
        setActionFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actionFlag")), actionFlag));
        setIfPlanFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ifPlanFlag")), ifPlanFlag));
        setLoadingChannelNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingChannelNo")), loadingChannelNo));
        setLoadingChannelName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingChannelName")), loadingChannelName));
        setLoadingPointNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingPointNo")), loadingPointNo));
        setLoadingPointName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("loadingPointName")), loadingPointName));
        setxInitialPoint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("xInitialPoint")), xInitialPoint));
        setxDestination(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("xDestination")), xDestination));
        setyInitialPoint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("yInitialPoint")), yInitialPoint));
        setyDestination(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("yDestination")), yDestination));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("tenantUser")), tenantUser));
        setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get("delFlag")), delFlag));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
        setSpecUpper(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specUpper")), specUpper));
        setSpecLower(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("specLower")), specLower));
        setPointLowerLength(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pointLowerLength")), pointLowerLength));
        setPointUpperLength(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("pointUpperLength")), pointUpperLength));
        setStandFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("standFlag")), standFlag));
        setC_no(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("c_no")), c_no));
        setEndPoint(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("endPoint")), endPoint));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("warehouseCode", StringUtils.toString(warehouseCode, eiMetadata.getMeta("warehouseCode")));
        map.put("warehouseName", StringUtils.toString(warehouseName, eiMetadata.getMeta("warehouseName")));
        map.put("locationId", StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locationName", StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("factoryArea", StringUtils.toString(factoryArea, eiMetadata.getMeta("factoryArea")));
        map.put("factoryAreaName", StringUtils.toString(factoryAreaName, eiMetadata.getMeta("factoryAreaName")));
        map.put("factoryBuilding", StringUtils.toString(factoryBuilding, eiMetadata.getMeta("factoryBuilding")));
        map.put("factoryBuildingName", StringUtils.toString(factoryBuildingName, eiMetadata.getMeta("factoryBuildingName")));
        map.put("crossArea", StringUtils.toString(crossArea, eiMetadata.getMeta("crossArea")));
        map.put("crossAreaName", StringUtils.toString(crossAreaName, eiMetadata.getMeta("crossAreaName")));
        map.put("posDirCode", StringUtils.toString(posDirCode, eiMetadata.getMeta("posDirCode")));
        map.put("managementStyle", StringUtils.toString(managementStyle, eiMetadata.getMeta("managementStyle")));
        map.put("actionFlag", StringUtils.toString(actionFlag, eiMetadata.getMeta("actionFlag")));
        map.put("ifPlanFlag", StringUtils.toString(ifPlanFlag, eiMetadata.getMeta("ifPlanFlag")));
        map.put("loadingChannelNo", StringUtils.toString(loadingChannelNo, eiMetadata.getMeta("loadingChannelNo")));
        map.put("loadingChannelName", StringUtils.toString(loadingChannelName, eiMetadata.getMeta("loadingChannelName")));
        map.put("loadingPointNo", StringUtils.toString(loadingPointNo, eiMetadata.getMeta("loadingPointNo")));
        map.put("loadingPointName", StringUtils.toString(loadingPointName, eiMetadata.getMeta("loadingPointName")));
        map.put("xInitialPoint", StringUtils.toString(xInitialPoint, eiMetadata.getMeta("xInitialPoint")));
        map.put("xDestination", StringUtils.toString(xDestination, eiMetadata.getMeta("xDestination")));
        map.put("yInitialPoint", StringUtils.toString(yInitialPoint, eiMetadata.getMeta("yInitialPoint")));
        map.put("yDestination", StringUtils.toString(yDestination, eiMetadata.getMeta("yDestination")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("tenantUser", StringUtils.toString(tenantUser, eiMetadata.getMeta("tenantUser")));
        map.put("delFlag", StringUtils.toString(delFlag, eiMetadata.getMeta("delFlag")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));
        map.put("specUpper",StringUtils.toString(specUpper, eiMetadata.getMeta("specUpper")));
        map.put("specLower",StringUtils.toString(specLower, eiMetadata.getMeta("specLower")));
        map.put("pointLowerLength",StringUtils.toString(pointLowerLength, eiMetadata.getMeta("pointLowerLength")));
        map.put("pointUpperLength",StringUtils.toString(pointUpperLength, eiMetadata.getMeta("pointUpperLength")));
        map.put("standFlag",StringUtils.toString(standFlag, eiMetadata.getMeta("standFlag")));
        map.put("c_no",StringUtils.toString(c_no, eiMetadata.getMeta("c_no")));
        map.put("endPoint",StringUtils.toString(endPoint, eiMetadata.getMeta("endPoint")));
        return map;

    }
}