/**
 * Generate time : 2024-12-24 10:43:48
 * Version : 1.0
 */
package com.baosight.imom.common.li.domain;

import com.baosight.iplat4j.core.util.NumberUtils;

import java.math.BigDecimal;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.data.DaoEPBase;

import java.util.HashMap;
import java.util.Map;

import com.baosight.iplat4j.core.util.StringUtils;

/**
 * Tlids1103
 *
 */
public class Tlids1103 extends DaoEPBase {

    private String unitCode = " ";        /* 业务单元代码*/
    private String segName = " ";        /* 业务单元简称*/

    private String segNo = " ";        /* 系统账套*/
    private String craneId = " ";        /* 行车编号*/
    private String craneName = " ";        /* 行车名称*/
    private String actionType = " ";        /* 动作类型*/
    private String grabSysId = " ";        /* 抓取流水号*/
    private String releaseSysId = " ";        /* 释放流水号*/
    private String x_value = " ";        /* X轴值*/
    private String y_value = " ";        /* Y轴值*/
    private String z_value = " ";        /* Z轴值*/
    private String locationId = " ";        /* 库位代码*/
    private String locationName = " ";        /* 库位名称*/
    private BigDecimal weight = new BigDecimal(0.000);        /* 重量*/
    private String uploadTime = " ";        /* 上传时间*/
    private String status = " ";        /* 接收状态*/
    private String remark = " ";        /* 备注*/
    private String uuid = " ";        /* uuid*/
    private String recCreator = " ";        /* 记录创建人*/
    private String recCreatorName = " ";        /* 记录创建人姓名*/
    private String recCreateTime = " ";        /* 记录创建时间*/
    private String recRevisor = " ";        /* 记录修改人*/
    private String recRevisorName = " ";        /* 记录修改人姓名*/
    private String recReviseTime = " ";        /* 记录修改时间*/

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("unitCode");
        eiColumn.setDescName("业务单元代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segName");
        eiColumn.setDescName("业务单元简称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("segNo");
        eiColumn.setDescName("系统账套");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneId");
        eiColumn.setDescName("行车编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("craneName");
        eiColumn.setDescName("行车名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("actionType");
        eiColumn.setDescName("动作类型");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("grabSysId");
        eiColumn.setDescName("抓取流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("releaseSysId");
        eiColumn.setDescName("释放流水号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("x_value");
        eiColumn.setDescName("X轴值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("y_value");
        eiColumn.setDescName("Y轴值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("z_value");
        eiColumn.setDescName("Z轴值");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationId");
        eiColumn.setDescName("库位代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("locationName");
        eiColumn.setDescName("库位名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("weight");
        eiColumn.setType("N");
        eiColumn.setScaleLength(3);
        eiColumn.setFieldLength(12);
        eiColumn.setDescName("重量");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uploadTime");
        eiColumn.setDescName("上传时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setDescName("接收状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("remark");
        eiColumn.setDescName("备注");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("uuid");
        eiColumn.setPrimaryKey(true);
        eiColumn.setDescName("uuid");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setDescName("记录创建人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreatorName");
        eiColumn.setDescName("记录创建人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setDescName("记录创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setDescName("记录修改人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisorName");
        eiColumn.setDescName("记录修改人姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setDescName("记录修改时间");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * the constructor
     */
    public Tlids1103() {
        initMetaData();
    }

    /**
     * get the unitCode - 业务单元代码
     * @return the unitCode
     */
    public String getUnitCode() {
        return this.unitCode;
    }

    /**
     * set the unitCode - 业务单元代码
     */
    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    /**
     * get the segNo - 系统账套
     * @return the segNo
     */
    public String getSegNo() {
        return this.segNo;
    }

    /**
     * set the segNo - 系统账套
     */
    public void setSegNo(String segNo) {
        this.segNo = segNo;
    }

    /**
     * get the craneId - 行车编号
     * @return the craneId
     */
    public String getCraneId() {
        return this.craneId;
    }

    /**
     * set the craneId - 行车编号
     */
    public void setCraneId(String craneId) {
        this.craneId = craneId;
    }

    /**
     * get the craneName - 行车名称
     * @return the craneName
     */
    public String getCraneName() {
        return this.craneName;
    }

    /**
     * set the craneName - 行车名称
     */
    public void setCraneName(String craneName) {
        this.craneName = craneName;
    }

    /**
     * get the actionType - 动作类型
     * @return the actionType
     */
    public String getActionType() {
        return this.actionType;
    }

    /**
     * set the actionType - 动作类型
     */
    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    /**
     * get the grabSysId - 抓取流水号
     * @return the grabSysId
     */
    public String getGrabSysId() {
        return this.grabSysId;
    }

    /**
     * set the grabSysId - 抓取流水号
     */
    public void setGrabSysId(String grabSysId) {
        this.grabSysId = grabSysId;
    }

    /**
     * get the releaseSysId - 释放流水号
     * @return the releaseSysId
     */
    public String getReleaseSysId() {
        return this.releaseSysId;
    }

    /**
     * set the releaseSysId - 释放流水号
     */
    public void setReleaseSysId(String releaseSysId) {
        this.releaseSysId = releaseSysId;
    }

    /**
     * get the x_value - X轴值
     * @return the x_value
     */
    public String getX_value() {
        return this.x_value;
    }

    /**
     * set the x_value - X轴值
     */
    public void setX_value(String x_value) {
        this.x_value = x_value;
    }

    /**
     * get the y_value - Y轴值
     * @return the y_value
     */
    public String getY_value() {
        return this.y_value;
    }

    /**
     * set the y_value - Y轴值
     */
    public void setY_value(String y_value) {
        this.y_value = y_value;
    }

    /**
     * get the z_value - Z轴值
     * @return the z_value
     */
    public String getZ_value() {
        return this.z_value;
    }

    /**
     * set the z_value - Z轴值
     */
    public void setZ_value(String z_value) {
        this.z_value = z_value;
    }

    /**
     * get the locationId - 库位代码
     * @return the locationId
     */
    public String getLocationId() {
        return this.locationId;
    }

    /**
     * set the locationId - 库位代码
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * get the locationName - 库位名称
     * @return the locationName
     */
    public String getLocationName() {
        return this.locationName;
    }

    /**
     * set the locationName - 库位名称
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     * get the weight - 重量
     * @return the weight
     */
    public BigDecimal getWeight() {
        return this.weight;
    }

    /**
     * set the weight - 重量
     */
    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    /**
     * get the uploadTime - 上传时间
     * @return the uploadTime
     */
    public String getUploadTime() {
        return this.uploadTime;
    }

    /**
     * set the uploadTime - 上传时间
     */
    public void setUploadTime(String uploadTime) {
        this.uploadTime = uploadTime;
    }

    /**
     * get the status - 接收状态
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 接收状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the remark - 备注
     * @return the remark
     */
    public String getRemark() {
        return this.remark;
    }

    /**
     * set the remark - 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * get the uuid - uuid
     * @return the uuid
     */
    public String getUuid() {
        return this.uuid;
    }

    /**
     * set the uuid - uuid
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * get the recCreator - 记录创建人
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 记录创建人
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreatorName - 记录创建人姓名
     * @return the recCreatorName
     */
    public String getRecCreatorName() {
        return this.recCreatorName;
    }

    /**
     * set the recCreatorName - 记录创建人姓名
     */
    public void setRecCreatorName(String recCreatorName) {
        this.recCreatorName = recCreatorName;
    }

    /**
     * get the recCreateTime - 记录创建时间
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 记录创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 记录修改人
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 记录修改人
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recRevisorName - 记录修改人姓名
     * @return the recRevisorName
     */
    public String getRecRevisorName() {
        return this.recRevisorName;
    }

    /**
     * set the recRevisorName - 记录修改人姓名
     */
    public void setRecRevisorName(String recRevisorName) {
        this.recRevisorName = recRevisorName;
    }

    /**
     * get the recReviseTime - 记录修改时间
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 记录修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    public String getSegName() {
        return segName;
    }

    public void setSegName(String segName) {
        this.segName = segName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("unitCode")), unitCode));
        setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segNo")), segNo));
        setCraneId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneId")), craneId));
        setCraneName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("craneName")), craneName));
        setActionType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("actionType")), actionType));
        setGrabSysId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("grabSysId")), grabSysId));
        setReleaseSysId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("releaseSysId")), releaseSysId));
        setX_value(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("x_value")), x_value));
        setY_value(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("y_value")), y_value));
        setZ_value(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("z_value")), z_value));
        setLocationId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationId")), locationId));
        setLocationName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("locationName")), locationName));
        setWeight(NumberUtils.toBigDecimal(StringUtils.toString(map.get("weight")), weight));
        setUploadTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uploadTime")), uploadTime));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setRemark(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("remark")), remark));
        setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("uuid")), uuid));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreatorName")), recCreatorName));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisorName")), recRevisorName));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setSegName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("segName")), segName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("unitCode", StringUtils.toString(unitCode, eiMetadata.getMeta("unitCode")));
        map.put("segNo", StringUtils.toString(segNo, eiMetadata.getMeta("segNo")));
        map.put("craneId", StringUtils.toString(craneId, eiMetadata.getMeta("craneId")));
        map.put("craneName", StringUtils.toString(craneName, eiMetadata.getMeta("craneName")));
        map.put("actionType", StringUtils.toString(actionType, eiMetadata.getMeta("actionType")));
        map.put("grabSysId", StringUtils.toString(grabSysId, eiMetadata.getMeta("grabSysId")));
        map.put("releaseSysId", StringUtils.toString(releaseSysId, eiMetadata.getMeta("releaseSysId")));
        map.put("x_value", StringUtils.toString(x_value, eiMetadata.getMeta("x_value")));
        map.put("y_value", StringUtils.toString(y_value, eiMetadata.getMeta("y_value")));
        map.put("z_value", StringUtils.toString(z_value, eiMetadata.getMeta("z_value")));
        map.put("locationId", StringUtils.toString(locationId, eiMetadata.getMeta("locationId")));
        map.put("locationName", StringUtils.toString(locationName, eiMetadata.getMeta("locationName")));
        map.put("weight", StringUtils.toString(weight, eiMetadata.getMeta("weight")));
        map.put("uploadTime", StringUtils.toString(uploadTime, eiMetadata.getMeta("uploadTime")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("remark", StringUtils.toString(remark, eiMetadata.getMeta("remark")));
        map.put("uuid", StringUtils.toString(uuid, eiMetadata.getMeta("uuid")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreatorName", StringUtils.toString(recCreatorName, eiMetadata.getMeta("recCreatorName")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recRevisorName", StringUtils.toString(recRevisorName, eiMetadata.getMeta("recRevisorName")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("segName", StringUtils.toString(segName, eiMetadata.getMeta("segName")));

        return map;

    }
}