package com.baosight.imom.vg.dm.domain;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.imom.common.constants.MessageCodeConstant;
import com.baosight.imom.common.vg.domain.Tvgdm0401;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 点检计划主项表
 */
public class VGDM0401 extends Tvgdm0401 implements CheckStatus {
    /**
     * 查询
     */
    public static final String QUERY = "VGDM0401.query";
    /**
     * 根据主项号查询
     */
    public static final String QUERY_BY_ID = "VGDM0401.queryById";
    /**
     * 查询今日待点检设备
     */
    public static final String QUERY_TODAY_EQUIPMENT = "VGDM0401.queryTodayEquipment";
    /**
     * 查询条数
     */
    public static final String COUNT = "VGDM0401.count";
    /**
     * 新增
     */
    public static final String INSERT = "VGDM0401.insert";
    /**
     * 修改
     */
    public static final String UPDATE = "VGDM0401.update";
    /**
     * 更新状态
     */
    public static final String UPDATE_STATUS = "VGDM0401.updateStatus";
    /**
     * 根据点检日期修改
     */
    public static final String UPDATE_BY_DAY = "VGDM0401.updateByDay";

    @Override
    public String getCheckStatus() {
        return this.getCheckPlanStatus();
    }

    @Override
    public String getQuerySqlId() {
        return QUERY;
    }

    /**
     * 根据点检计划主项号查询主项信息
     *
     * @param dao         dao
     * @param checkPlanId 点检计划主项号
     * @return 主项信息或null
     */
    public static VGDM0401 queryById(Dao dao, String checkPlanId) {
        Map<String, String> map = new HashMap<>(1);
        map.put("checkPlanId", checkPlanId);
        List list = dao.query(VGDM0401.QUERY_BY_ID, map);
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatException(MessageCodeConstant.errorMessage.MSG_ERROR_NOT_EXIST);
        }
        return (VGDM0401) list.get(0);
    }
}
