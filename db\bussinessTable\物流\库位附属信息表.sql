create table meli.tlids0601
(
    SEG_NO                varchar(12)  default ' ' not null comment '系统账套',
    UNIT_CODE             varchar(12)  default ' ' not null comment '业务单元代代码',
    WAREHOUSE_CODE        varchar(20)  default ' ' not null comment '仓库代码',
    WAREHOUSE_NAME        varchar(20)  default ' ' not null comment '仓库名称',
    LOCATION_ID           varchar(20)  default ' ' not null comment '库位代码',
    LOCATION_NAME         varchar(20)  default ' ' not null comment '库位名称',
    FACTORY_AREA          varchar(20)  default ' ' not null comment '厂区代码',
    FACTORY_AREA_NAME     varchar(50)  default ' ' not null comment '厂区名称',
    FACTORY_BUILDING      varchar(20)  default ' ' not null comment '厂房代码',
    FACTORY_BUILDING_NAME varchar(20)  default ' ' not null comment '厂房名称',
    CROSS_AREA            varchar(20)  default ' ' not null comment '跨区编码',
    CROSS_AREA_NAME       varchar(20)  default ' ' not null comment '跨区名称',
    POS_DIR_CODE          varchar(2)   default ' ' not null comment '层数标记',
    MANAGEMENT_STYLE      varchar(2)   default ' ' not null comment '管理方式(10点状 20条状 21条状特殊)',
    ACTION_FLAG           varchar(1)   default ' ' not null comment '板卷标记(0板 1卷)',
    IF_PLAN_FLAG          varchar(1)   default ' ' not null comment '是否参与库位推荐(0参与 1不参与)',
    LOADING_CHANNEL_NO    varchar(20)  default ' ' not null comment '装卸通道编码',
    LOADING_CHANNEL_NAME  varchar(20)  default ' ' not null comment '装卸通道名称',
    LOADING_POINT_NO      varchar(20)  default ' ' not null comment '装卸点编码',
    LOADING_POINT_NAME    varchar(20)  default ' ' not null comment '装卸点名称',
    X_INITIAL_POINT       varchar(20)  default ' ' not null comment 'X轴起始点',
    X_DESTINATION         varchar(20)  default ' ' not null comment 'X轴终到点',
    Y_INITIAL_POINT       varchar(20)  default ' ' not null comment 'Y轴起始点',
    Y_DESTINATION         varchar(20)  default ' ' not null comment 'Y轴终到点',
    STATUS                varchar(2)   default ' ' not null comment '状态(启用：10、停用：20)',
    REC_CREATOR           varchar(32)  default ' ' null comment '记录创建人',
    REC_CREATOR_NAME      varchar(100) default ' ' null comment '记录创建人姓名',
    REC_CREATE_TIME       varchar(17)  default ' ' null comment '记录创建时间',
    REC_REVISOR           varchar(32)  default ' ' null comment '记录修改人',
    REC_REVISOR_NAME      varchar(100) default ' ' null comment '记录修改人姓名',
    REC_REVISE_TIME       varchar(17)  default ' ' null comment '记录修改时间',
    ARCHIVE_FLAG          varchar(1)   default ' ' null comment '归档标记',
    TENANT_USER           varchar(10)  default ' ' null comment '租户',
    DEL_FLAG              smallint     default 0   null comment '删除标记',
    UUID                  varchar(32)  default ' ' not null comment 'ID'
        primary key,
    constraint tlids0601_UUID_uindex
        unique (UUID)
)
    comment '库位附属信息表' collate = utf8_bin;

